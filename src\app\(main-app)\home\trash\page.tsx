"use client";
import React, { useState, useEffect } from "react";
import FormsList from "@/components/trash/FormsList";
import FoldersList from "@/components/trash/FoldersList";
import { useRestoreFolder, useGetTrashFolders, useDeleteFolderPermanently } from "@/api-services/folder";
import { useRestoreForm, useGetTrashedForms, useDeleteForm, useMoveFormToTrash } from "@/api-services/form";
import toast from "react-hot-toast";
import { Loader2 } from "lucide-react";

interface TrashFolder {
  id: string;
  name: string;
  deletedBy: string;
  deletedOn: string;
  selected: boolean;
}

interface TrashForm {
  id: string;
  name: string;
  deletedBy: string;
  deletedOn: string;
  selected: boolean;
}

const TrashPage = () => {
  const [activeTab, setActiveTab] = useState<"forms" | "folders">("forms");
  const [selectedFolderIds, setSelectedFolderIds] = useState<Set<string>>(new Set());
  const [selectedFormIds, setSelectedFormIds] = useState<Set<string>>(new Set());
  
  // Track forms being deleted for optimistic updates
  const [deletingFormIds, setDeletingFormIds] = useState<Set<string>>(new Set());
  const [restoringFormIds, setRestoringFormIds] = useState<Set<string>>(new Set());
  
  // Track folders being restored for optimistic updates
  const [restoringFolderIds, setRestoringFolderIds] = useState<Set<string>>(new Set());
  
  // Track folders being deleted for optimistic updates
  const [deletingFolderIds, setDeletingFolderIds] = useState<Set<string>>(new Set());
  
  // API hooks
  const { mutate: restoreFolder, isPending: isRestoringFolder } = useRestoreFolder();
  const { data: trashFoldersData, isLoading: isLoadingFolders, error: foldersError, refetch: refetchFolders } = useGetTrashFolders();
  
  const { mutate: restoreForm, isPending: isRestoringForm } = useRestoreForm();
  const { data: trashFormsData, isLoading: isLoadingForms, error: formsError, refetch: refetchForms } = useGetTrashedForms();
  
  // Delete form from trash (permanent deletion)
  const { mutate: deleteForm, isPending: isDeletingForm } = useDeleteForm({ limit: 10, offset: 0 });

  // Delete folder from trash (permanent deletion)
  const { mutate: deleteFolderPermanently, isPending: isDeletingFolder } = useDeleteFolderPermanently();

  // Transform API data to match component interface
  const folders: TrashFolder[] = trashFoldersData?.data?.folders?.map((folder: any) => ({
    id: folder.id,
    name: folder.name,
    deletedBy: "Unknown",
    deletedOn: folder.created_at || new Date().toISOString(),
    selected: selectedFolderIds.has(folder.id),
  })).filter((folder: any) => !restoringFolderIds.has(folder.id) && !deletingFolderIds.has(folder.id)) || [];

  const forms: TrashForm[] = trashFormsData?.data?.forms?.map((form: any) => ({
    id: form.id,
    name: form.name || form.title || "Untitled Form",
    deletedBy: "Unknown",
    deletedOn: form.created_at || form.deleted_at || new Date().toISOString(),
    selected: selectedFormIds.has(form.id),
  })).filter((form: any) => !deletingFormIds.has(form.id) && !restoringFormIds.has(form.id)) || [];

  // Refresh data when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refetchForms();
        refetchFolders();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refetchForms, refetchFolders]);

  const handleSelectForm = (id: string) => {
    setSelectedFormIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };
  
  const handleSelectFolder = (id: string) => {
    setSelectedFolderIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleDeleteForms = () => {
    const selectedForms = forms.filter((f: TrashForm) => f.selected);
    if (selectedForms.length === 0) return;

    // Track deletion progress
    let completedDeletions = 0;
    let failedDeletions = 0;
    const totalDeletions = selectedForms.length;
    const selectedFormIdsArray = Array.from(selectedFormIds);

    // Optimistic update: immediately hide the forms being deleted
    setDeletingFormIds(prev => {
      const newSet = new Set(prev);
      selectedFormIdsArray.forEach(id => newSet.add(id));
      return newSet;
    });
    
    // Clear selections immediately for instant UI feedback
    setSelectedFormIds(new Set());

    // Show toast immediately
    toast.success("Deleted successfully");

    selectedForms.forEach((form: TrashForm) => {
      deleteForm(form.id, {
        onSuccess: () => {
          completedDeletions++;
          
          // Only refetch after all deletions are complete
          if (completedDeletions + failedDeletions === totalDeletions) {
            // Don't clear deletingFormIds on success - keep forms hidden
            refetchForms();
          }
        },
        onError: () => {
          failedDeletions++;
          toast.error(`Failed to delete form "${form.name}"`);
          
          // If any deletion fails, remove from deleting set and refetch
          if (completedDeletions + failedDeletions === totalDeletions) {
            setDeletingFormIds(prev => {
              const newSet = new Set(prev);
              selectedFormIdsArray.forEach(id => newSet.delete(id));
              return newSet;
            });
            refetchForms();
          }
        }
      });
    });
  };

  const handleDeleteSingleForm = (formId: string) => {
    const form = forms.find((f: TrashForm) => f.id === formId);
    if (!form) return;

    // Optimistic update: immediately hide the form being deleted
    setDeletingFormIds(prev => new Set(prev).add(formId));

    deleteForm(formId, {
      onSuccess: () => {
        toast.success("Deleted successfully");
        // Don't clear deletingFormIds on success - keep the form hidden
        refetchForms();
      },
      onError: () => {
        toast.error(`Failed to delete form "${form.name}"`);
        // Only clear deletingFormIds on error to show the form again
        setDeletingFormIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(formId);
          return newSet;
        });
      }
    });
  };

  const handleRestoreFolders = () => {
    const selectedFolders = folders.filter((f: TrashFolder) => f.selected);
    if (selectedFolders.length === 0) return;

    // Track restoration progress
    let completedRestorations = 0;
    let failedRestorations = 0;
    const totalRestorations = selectedFolders.length;
    const selectedFolderIdsArray = Array.from(selectedFolderIds);

    // Optimistic update: immediately hide the folders being restored
    setRestoringFolderIds(prev => {
      const newSet = new Set(prev);
      selectedFolderIdsArray.forEach(id => newSet.add(id));
      return newSet;
    });

    // Clear selections immediately for instant UI feedback
    setSelectedFolderIds(new Set());

    // Show toast immediately
    toast.success("Restored successfully");

    selectedFolders.forEach((folder: TrashFolder) => {
      restoreFolder(folder.id, {
        onSuccess: () => {
          completedRestorations++;
          
          // Only refetch after all restorations are complete
          if (completedRestorations + failedRestorations === totalRestorations) {
            setRestoringFolderIds(new Set()); // Clear the restoring set
            refetchFolders();
          }
        },
        onError: () => {
          failedRestorations++;
          toast.error(`Failed to restore folder "${folder.name}"`);
          
          // If any restoration fails, remove from restoring set and refetch
          if (completedRestorations + failedRestorations === totalRestorations) {
            setRestoringFolderIds(prev => {
              const newSet = new Set(prev);
              selectedFolderIdsArray.forEach(id => newSet.delete(id));
              return newSet;
            });
            refetchFolders();
          }
        }
      });
    });
  };

  const handleRestoreSingleFolder = (folderId: string) => {
    const folder = folders.find((f: TrashFolder) => f.id === folderId);
    if (!folder) return;

    // Optimistic update: immediately hide the folder being restored
    setRestoringFolderIds(prev => new Set(prev).add(folderId));

    restoreFolder(folderId, {
      onSuccess: () => {
        toast.success("Restored successfully");
        setRestoringFolderIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(folderId);
          return newSet;
        });
        refetchFolders();
      },
      onError: () => {
        toast.error(`Failed to restore folder "${folder.name}"`);
        setRestoringFolderIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(folderId);
          return newSet;
        });
      }
    });
  };

  const handleRestoreForms = () => {
    const selectedForms = forms.filter((f: TrashForm) => f.selected);
    if (selectedForms.length === 0) return;

    // Track restoration progress
    let completedRestorations = 0;
    let failedRestorations = 0;
    const totalRestorations = selectedForms.length;
    const selectedFormIdsArray = Array.from(selectedFormIds);

    // Optimistic update: immediately hide the forms being restored
    setRestoringFormIds(prev => {
      const newSet = new Set(prev);
      selectedFormIdsArray.forEach(id => newSet.add(id));
      return newSet;
    });

    // Clear selections immediately for instant UI feedback
    setSelectedFormIds(new Set());

    // Show toast immediately
    toast.success("Restored successfully");

    selectedForms.forEach((form: TrashForm) => {
      restoreForm(form.id, {
        onSuccess: () => {
          completedRestorations++;
          
          // Only refetch after all restorations are complete
          if (completedRestorations + failedRestorations === totalRestorations) {
            setRestoringFormIds(new Set()); // Clear the restoring set
            refetchForms();
          }
        },
        onError: () => {
          failedRestorations++;
          toast.error(`Failed to restore form "${form.name}"`);
          
          // If any restoration fails, remove from restoring set and refetch
          if (completedRestorations + failedRestorations === totalRestorations) {
            setRestoringFormIds(prev => {
              const newSet = new Set(prev);
              selectedFormIdsArray.forEach(id => newSet.delete(id));
              return newSet;
            });
            refetchForms();
          }
        }
      });
    });
  };

  const handleRestoreSingleForm = (formId: string) => {
    const form = forms.find((f: TrashForm) => f.id === formId);
    if (!form) return;

    // Optimistic update: immediately hide the form being restored
    setRestoringFormIds(prev => new Set(prev).add(formId));

    restoreForm(formId, {
      onSuccess: () => {
        toast.success("Restored successfully");
        setRestoringFormIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(formId);
          return newSet;
        });
        refetchForms();
      },
      onError: () => {
        toast.error(`Failed to restore form "${form.name}"`);
        setRestoringFormIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(formId);
          return newSet;
        });
      }
    });
  };

  const handleDeleteFolders = () => {
    const selectedFolders = folders.filter((f: TrashFolder) => f.selected);
    if (selectedFolders.length === 0) return;

    // Track deletion progress
    let completedDeletions = 0;
    let failedDeletions = 0;
    const totalDeletions = selectedFolders.length;
    const selectedFolderIdsArray = Array.from(selectedFolderIds);

    // Optimistic update: immediately hide the folders being deleted
    setDeletingFolderIds(prev => {
      const newSet = new Set(prev);
      selectedFolderIdsArray.forEach(id => newSet.add(id));
      return newSet;
    });
    
    // Clear selections immediately for instant UI feedback
    setSelectedFolderIds(new Set());

    // Show toast immediately
    toast.success("Deleted successfully");

    selectedFolders.forEach((folder: TrashFolder) => {
      deleteFolderPermanently(folder.id, {
        onSuccess: () => {
          completedDeletions++;
          
          // Only refetch after all deletions are complete
          if (completedDeletions + failedDeletions === totalDeletions) {
            // Don't clear deletingFolderIds on success - keep folders hidden
            refetchFolders();
          }
        },
        onError: () => {
          failedDeletions++;
          toast.error(`Failed to delete folder "${folder.name}"`);
          
          // If any deletion fails, remove from deleting set and refetch
          if (completedDeletions + failedDeletions === totalDeletions) {
            setDeletingFolderIds(prev => {
              const newSet = new Set(prev);
              selectedFolderIdsArray.forEach(id => newSet.delete(id));
              return newSet;
            });
            refetchFolders();
          }
        }
      });
    });
  };

  const handleDeleteSingleFolder = (folderId: string) => {
    const folder = folders.find((f: TrashFolder) => f.id === folderId);
    if (!folder) return;

    // Optimistic update: immediately hide the folder being deleted
    setDeletingFolderIds(prev => new Set(prev).add(folderId));

    deleteFolderPermanently(folderId, {
      onSuccess: () => {
        toast.success("Deleted successfully");
        // Don't clear deletingFolderIds on success - keep the folder hidden
        refetchFolders();
      },
      onError: () => {
        toast.error(`Failed to delete folder "${folder.name}"`);
        // Only clear deletingFolderIds on error to show the folder again
        setDeletingFolderIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(folderId);
          return newSet;
        });
      }
    });
  };

  const anySelected =
    activeTab === "forms"
      ? forms.some((f: TrashForm) => f.selected)
      : folders.some((f: TrashFolder) => f.selected);

  const isLoading = isLoadingFolders || isLoadingForms;
  const hasError = foldersError || formsError;
  const isRestoring = isRestoringFolder || isRestoringForm;
  const isDeleting = isDeletingForm || isDeletingFolder;

  if (isLoading) {
    return (
      <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
        <div>
          <h2 className="text-3xl font-semibold text-left text-app-text-color">
            Trash
          </h2>
          <p className="font-semibold text-app-text-color">
            Your trash data will be permanently deleted after 30 days.
          </p>
        </div>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-app-primary" />
          <span className="ml-2 text-app-text-secondary">Loading trash data...</span>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
        <div>
          <h2 className="text-3xl font-semibold text-left text-app-text-color">
            Trash
          </h2>
          <p className="font-semibold text-app-text-color">
            Your trash data will be permanently deleted after 30 days.
          </p>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-500 mb-2">Failed to load trash data</p>
            <button 
              onClick={() => {
                refetchFolders();
                refetchForms();
              }}
              className="px-4 py-2 bg-app-primary text-white rounded hover:bg-app-primary/90"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
      <div>
        <h2 className="text-3xl font-semibold text-left text-app-text-color">
          Trash
        </h2>
        <p className="font-semibold text-app-text-color">
          Your trash data will be permanently deleted after 30 days.
        </p>
      </div>
      <div className="flex items-center justify-between space-x-6 pb-2">
        <div className="flex items-center space-x-6">
          <button
            className={`text-base font-medium pb-1 ${
              activeTab === "forms"
                ? "border-b-2 border-app-text-color text-app-text-color"
                : "text-app-text-secondary"
            }`}
            onClick={() => setActiveTab("forms")}
          >
            Forms ({forms.length})
          </button>
          <button
            className={`text-base font-medium pb-1 ${
              activeTab === "folders"
                ? "border-b-2 border-app-text-color text-app-text-color"
                : "text-app-text-secondary"
            }`}
            onClick={() => setActiveTab("folders")}
          >
            Folders ({folders.length})
          </button>
        </div>
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => {
              refetchFolders();
              refetchForms();
            }}
            disabled={isLoading}
            className="p-2 text-app-text-secondary hover:text-app-text-color hover:bg-app-hero-background rounded-full transition-colors"
            title="Refresh trash data"
          >
            <svg 
              className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
              />
            </svg>
          </button>
          {anySelected && (
            <div className="flex space-x-2">
              <button 
                className="border border-red-500 text-red-500 px-4 py-1 rounded hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={activeTab === "forms" ? handleDeleteForms : handleDeleteFolders}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Permanently"}
              </button>
              <button 
                className="border border-app-primary text-app-primary px-4 py-1 rounded hover:bg-app-primary/10 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={activeTab === "folders" ? handleRestoreFolders : handleRestoreForms}
                disabled={isRestoring}
              >
                {isRestoring ? "Restoring..." : "Restore"}
              </button>
            </div>
          )}
        </div>
      </div>
      <div>
        {activeTab === "forms" ? (
          <FormsList 
            forms={forms} 
            onSelect={handleSelectForm}
            onRestore={handleRestoreSingleForm}
            onDelete={handleDeleteSingleForm}
          />
        ) : (
          <FoldersList 
            folders={folders} 
            onSelect={handleSelectFolder} 
            onRestore={handleRestoreSingleFolder}
            onDelete={handleDeleteSingleFolder}
          />
        )}
      </div>
    </div>
  );
};

export default TrashPage;
