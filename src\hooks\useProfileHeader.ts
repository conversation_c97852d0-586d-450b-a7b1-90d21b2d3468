import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { useLogoutUser } from "@/api-services/auth";
import { supabase } from "@/lib/supabase";
import posthog from "posthog-js";

const useProfileHeader = () => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isLogoutDialogOpen, setIsLogoutDialogOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { mutateAsync: logout } = useLogoutUser();

  const togglePopover = () => {
    setIsPopoverOpen((prev) => !prev);
  };

  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => !prev);
  };

  const handleLogout = async () => {
    try {
      // Get user info before logout for tracking
      const { data: { session } } = await supabase.auth.getSession();
      const userRole = localStorage.getItem("role");
      const workspaceId = localStorage.getItem("workspace_id");

      await supabase.auth.signOut();
      (window as any).$crisp.push(["do", "session:reset"]);
      localStorage.clear();

      // Track successful logout
      posthog.capture('logout_successful', {
        role: userRole,
        workspace_id: workspaceId
      });

      // Reset user identification in PostHog
      posthog.reset();

      setIsLogoutDialogOpen(false);
      toast.success("Logged out successfully!");
      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
    } catch (error: any) {
      console.error("Logout failed:", error);
      
      // Track failed logout
      posthog.capture('logout_failed', {
        error: error?.message || 'Unknown error'
      });

      // Even if the API call fails, clear local data
      localStorage.removeItem("name");
      localStorage.removeItem("role");

      toast.error("Logout failed, but you've been logged out locally.");
      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
    }
  };

  return {
    isPopoverOpen,
    togglePopover,
    isDrawerOpen,
    toggleDrawer,
    isLogoutDialogOpen,
    setIsLogoutDialogOpen,
    handleLogout,
    router,
    pathname,
  };
};

export default useProfileHeader;
