"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { registerUser } from "@/api-services/auth";
import toast from "react-hot-toast";
import posthog from "posthog-js";

export interface SignUpFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  countryCode: string;
  country: string;
  terms_conditions: boolean;
  refferal?: string;
}

export function useSignUpForm(refferal?: string) {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<SignUpFormData>({
    mode: "onBlur",
    defaultValues: {
      phone: "",
    },
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const password = watch("password");

  const onSubmit = async (data: SignUpFormData) => {
    try {
      posthog.capture('signup_attempted', {
        email: data.email,
        country: data.country
      });

      const { countryCode, phone, ...rest  } = data;
      const phoneWithoutCountryCode = phone.replace(
        countryCode.replace("+", ""),
        ""
      );

      const res = await registerUser({
        ...rest,
        phone: phoneWithoutCountryCode,
        countryCode,
        country: data.country,
        refferal
      });

      // Check if the response indicates an error
      if (res && typeof res === 'object' && !res.success) {
        toast.error(res.error);
        return;
      }

      if (
        res?.data?.message ===
        "Registration successful! Please check your email to verify your account."
      ) {
        posthog.capture('signup_successful', {
          email: data.email,
          country: data.country
        });

        toast.success(
          "Registration successful! Please check your email to verify your account then only you can login in your account."
        );
        router.push("/login");
      }
    } catch (error: any) {
      
      // Track signup failure with specific reason
      let failureReason = 'unknown';
      let errorMessage = error?.message || 'Registration failed. Please try again.';

      // Handle specific error cases
      if (errorMessage.includes("User already registered")) {
        failureReason = 'email_exists';
        errorMessage = "This email is already registered. Please use a different email or try logging in.";
      } else if (errorMessage.includes("Invalid phone number")) {
        failureReason = 'invalid_phone';
        errorMessage = "Please enter a valid phone number.";
      } else if (errorMessage.includes("Password")) {
        failureReason = 'invalid_password';
        errorMessage = "Password must be at least 6 characters long and contain a mix of letters and numbers.";
      }

      toast.error(errorMessage);

      posthog.capture('signup_failed', {
        email: data.email,
        country: data.country,
        reason: failureReason,
        error: error?.message || 'Unknown error'
      });
    }
  };

  return {
    register,
    handleSubmit,
    errors,
    isSubmitting,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    password,
    onSubmit,
    setValue,
    watch,
  };
}
