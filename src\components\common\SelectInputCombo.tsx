import { Option } from "@/types/types";
import React, { useRef, useState, useEffect } from "react";

interface SelectInputComboProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
}

// Helper to convert {{index.Name}} to {{fieldid.Name.fieldtitle}}
export function convertTemplateToMapped(
  template: string,
  options: Option[],
  fields: any[]
): string {
  // Replace all {{index.Name}} with correct backend key
  return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
    // Find the option with matching index and name
    const opt = options.find(
      (opt) => String(opt.index) === idx && opt.name === name
    );
    if (!opt) return match;
    // For NAME_INPUT, use the new format {{fieldId.name.firstName}} or {{fieldId.name.lastName}}
    const field = fields.find((f) => f.id === opt.id);
    if (field && field.component === "NAME_INPUT") {
      return `{{${opt.id}.name.${opt.name}}}`;
    }
    // For ADDRESS, use the format {{fieldId.address.address}}, {{fieldId.address.city}}, etc.
    if (field && field.component === "ADDRESS") {
      return `{{${opt.id}.address.${opt.name}}}`;
    }
    // For other fields, just use the field ID and name if available
    return `{{${opt.id}}}`;
  });
}

// Helper to convert backend format to template format (handles multiple fields in a string)
export function convertMappedToTemplate(
  key: string,
  options: Option[]
): string {
  if (!key) return "";

  // Replace all {{fieldid.name.fieldtitle}} with {{index.name}}
  return key.replace(
    /\{\{([^.]+)(?:\.[^.]+)*\.([^.]+)\}\}/g,
    (match, fieldId, fieldName) => {
      // Find the option for this field
      const opt = options.find(
        (opt) => opt.id === fieldId && opt.name === fieldName
      );
      if (!opt) return match;
      return `{{${opt.index}.${fieldName}}}`;
    }
  );
}

const SelectInputCombo: React.FC<SelectInputComboProps> = ({
  options,
  value,
  onChange,
  placeholder = "Type or insert fields...",
  label,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen]);

  // Insert {{index.Name}} at cursor
  const insertField = (index: number, option: Option & { index?: number }) => {
    if (!inputRef.current) return;
    const el = inputRef.current;
    const cursor = el.selectionStart || 0;
    const before = value.slice(0, cursor);
    const after = value.slice(cursor);
    const insert = `{{${option.index ?? index + 1}.${option.name}}}`;
    const newValue = before + insert + after;
    onChange(newValue);
    // Move cursor after inserted field
    setTimeout(() => {
      el.focus();
      el.setSelectionRange(
        before.length + insert.length,
        before.length + insert.length
      );
    }, 0);
  };

  return (
    <div className="w-full relative" ref={wrapperRef}>
      {label && (
        <label className="block text-sm font-medium mb-1">{label}</label>
      )}
      <input
        ref={inputRef}
        className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setDropdownOpen(true)}
        placeholder={placeholder}
        autoComplete="off"
        spellCheck={false}
      />
      {dropdownOpen && (
        <div className="absolute z-50 bg-app-background border border-app-border-primary rounded-md mt-1 w-full max-h-48 overflow-y-auto shadow-lg scroller-style">
          {options.length > 0 ? (
            options.map((option, idx) => (
              <div
                key={option.id + option.name}
                className="px-3 py-2 hover:bg-app-hero-background cursor-pointer text-sm"
                onMouseDown={() => insertField(idx, option)}
              >
                {`${option.index ?? idx + 1}.${option.title}`}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-app-text-color text-sm">
              No fields
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SelectInputCombo;
