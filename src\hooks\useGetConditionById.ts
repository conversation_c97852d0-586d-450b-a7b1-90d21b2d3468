import { useAppStore } from "@/state-store/app-state-store";
import globalForCondition, { defaultHideFieldState } from "@/state-store/globalForCondition";
import { ConditionOperator, evaluateCondition, ThenAction } from "@/types/condition";
import { useSearchParams } from "next/navigation";
import { useEffect,useRef } from "react";

function useGetConditionById(id: string,value:string) {
  const { conditions,setFields,fields,thankYouConditions,setThankYouLogicResult } = useAppStore();
  const formId = useSearchParams().get("formId")!;
  const haveCondition = conditions?.length > 0;
  const haveThankYouCondition = Array.isArray(thankYouConditions) && thankYouConditions.length > 0;

  if(haveCondition){
    conditions?.forEach((condition)=>{
        condition?.condition_logic?.forEach((logic)=>{
            if (!globalForCondition[formId]) {
                globalForCondition[formId] = {
                    ...globalForCondition[formId],
                    conditionValue: globalForCondition[formId]?.conditionValue || {},
                    conditionThen: globalForCondition[formId]?.conditionThen || {}
                };
            }

            if (!globalForCondition[formId].conditionValue) {
                globalForCondition[formId].conditionValue = {};
            }

            const conditionObject: Record<string, boolean> = globalForCondition[formId].conditionValue[condition?.condition_id] || {};
            const currentValue = logic?.element_id === id ? evaluateCondition(logic?.operator as ConditionOperator,logic?.value,value) : !!conditionObject[logic?.element_id];
            conditionObject[logic?.element_id] = currentValue;
            globalForCondition[formId].conditionValue[condition?.condition_id] = conditionObject;
        });

        globalForCondition[formId] = {
          ...globalForCondition[formId],
          conditionRule: {
            ...globalForCondition[formId].conditionRule,
            [condition?.condition_id]: condition?.rule
          }
        }

        condition?.condition_then?.forEach((then)=>{
            if (!globalForCondition[formId].conditionThen) {
                globalForCondition[formId].conditionThen = {};
            }

            const conditionObject: Record<string, string> = globalForCondition[formId].conditionThen[condition?.condition_id] || {};
            conditionObject[then?.element_id] = then?.action;
            globalForCondition[formId].conditionThen[condition?.condition_id] = conditionObject;
        });
    })
  }

  if(haveThankYouCondition){
    thankYouConditions.forEach((condition)=>{
      condition?.condition_logic?.forEach((logic)=>{
        if(!globalForCondition[formId]){
          globalForCondition[formId] = {
            ...globalForCondition[formId],
            thankYouConditionValue: {},
            thankYouConditionThen: {}
          }
        }

        if(!globalForCondition[formId].thankYouConditionValue){
          globalForCondition[formId].thankYouConditionValue = {};
        }

        const conditionObject: Record<string, boolean> = globalForCondition[formId].thankYouConditionValue[condition?.condition_id] || {};
        const currentValue = logic?.element_id === id ? evaluateCondition(logic?.operator as ConditionOperator,logic?.value,value) : !!conditionObject[logic?.element_id];
        conditionObject[logic?.element_id] = currentValue;

        globalForCondition[formId].thankYouConditionValue[condition?.condition_id] = conditionObject;

      
        
      })

      globalForCondition[formId] = {
        ...globalForCondition[formId],
        thankYouConditionRule: {
          ...globalForCondition[formId].thankYouConditionRule,
          [condition?.condition_id]: condition?.rule
        }
      }

      globalForCondition[formId] = {
        ...globalForCondition?.[formId],
        thankYouConditionThen: {
          ...globalForCondition?.[formId]?.thankYouConditionThen,
          [condition?.condition_id]: {
            ...globalForCondition?.[formId]?.thankYouConditionThen?.[condition?.condition_id],
            action: condition?.action,
            content:condition?.action ==="Show custom message"? condition?.custom_message:condition?.redirect_url
          }
        }
       }
    })
  }


  useEffect(()=>{
   const conditionValue = globalForCondition?.[formId]?.conditionValue;
   const conditionThen = globalForCondition?.[formId]?.conditionThen;
   const conditionRule = globalForCondition?.[formId]?.conditionRule;

   if(conditionValue && conditions?.length > 0){
     // Initialize field visibility states
     const fieldsToShow = new Set<string>();
     const fieldsToHide = new Set<string>();
     const fieldsWithShowConditions = new Set<string>();
     const fieldsWithHideConditions = new Set<string>();

     // Collect all fields that have conditions
     conditions.forEach(condition => {
       condition.condition_then?.forEach(then => {
         if (then.action === ThenAction.SHOW_FIELD) {
           fieldsWithShowConditions.add(then.element_id);
         } else if (then.action === ThenAction.HIDE_FIELD) {
           fieldsWithHideConditions.add(then.element_id);
         }
       });
     });

     // Evaluate each condition
     Object.keys(conditionValue).forEach((conditionId)=>{
       const condition = conditionValue?.[conditionId];
       const thenAction = conditionThen?.[conditionId];
       const rule = conditionRule?.[conditionId];
       
       if (!condition || !thenAction) return;

       const isAll = rule === "all";
       const conditionKeys = Object.keys(condition);
       
       if (conditionKeys.length === 0) return;

       const hasSatisfyCondition = isAll 
         ? conditionKeys.every((elementId) => condition[elementId])
         : conditionKeys.some((elementId) => condition[elementId]);

       if(hasSatisfyCondition){
         Object.entries(thenAction).forEach(([fieldId, action]) => {
           if (action === ThenAction.HIDE_FIELD) {
             fieldsToHide.add(fieldId);
           } else if (action === ThenAction.SHOW_FIELD) {
             fieldsToShow.add(fieldId);
           }
         });
       }
     });

     // Update field states
     setFields(fields.map((field)=>{
       const fieldId = field.id;
       const hasAnyCondition = fieldsWithShowConditions.has(fieldId) || fieldsWithHideConditions.has(fieldId);

       // Priority 1: If explicitly shown by satisfied condition, show it
       if (fieldsToShow.has(fieldId)) {
         return { ...field, isHide: false, isDisable: false };
       }

       // Priority 2: If explicitly hidden by satisfied condition, hide it
       if (fieldsToHide.has(fieldId)) {
         return { ...field, isHide: true, isDisable: false };
       }

       // Priority 3: If field has show condition but condition not met, hide it
       if (fieldsWithShowConditions.has(fieldId)) {
         return { ...field, isHide: true, isDisable: false };
       }

       // Priority 4: For fields with no conditions, respect their current state (manual hide/show)
       if (!hasAnyCondition) {
         return { 
           ...field, 
           isDisable: false
           // Keep current isHide state - don't override manual changes
         };
       }

       // Priority 5: For other fields with conditions, use their default state
       return { 
         ...field, 
         isDisable: false, 
         isHide: defaultHideFieldState[fieldId] || false 
       };
     }));
   } else {
     // If no conditions, keep fields as they are (respect manual hide/show)
     setFields(fields.map((field) => ({
       ...field,
       isDisable: false
       // Don't override isHide - keep current state
     })));
   }

   const thankYouConditionValue = globalForCondition?.[formId]?.thankYouConditionValue;
   const thankYouConditionThen = globalForCondition?.[formId]?.thankYouConditionThen;
   const thankYouConditionRule = globalForCondition?.[formId]?.thankYouConditionRule;

   if(haveThankYouCondition){
    Object.keys(thankYouConditionValue || {}).forEach((conditionId)=>{
      const condition = thankYouConditionValue?.[conditionId];
      const thenAction = thankYouConditionThen?.[conditionId];
      const rule = thankYouConditionRule?.[conditionId];
      const isAll = rule === "all";

      const hasSatisfyCondition = isAll ? Object.keys(condition).every((elementId)=>{
        return condition[elementId];
      }) : Object.keys(condition).some((elementId)=>{
        return condition[elementId];
      });


      if(hasSatisfyCondition){
        setThankYouLogicResult({
          action: thenAction?.action,
          content: thenAction?.content
        })
      }
    })
   }else{
    setThankYouLogicResult({
      action: "",
      content: ""
    })
   }
  },[value, conditions])
}

export default useGetConditionById;

