import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import React from "react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useFormSettings } from "@/hooks/useFormSettings";
import { useSearchParams } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const FormSettings = () => {
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const {
    accessSetting,
    formVisibility,
    copyAllowed,
    accessOptions,
    visibilityOptions,
    copyOptions,
    setFormVisibility,
    setCopyAllowed,
    handleCopyForm,
    isCopied,
    showPublishDialog,
    setShowPublishDialog,
  } = useFormSettings(formId!);

  // Generate the copy URL
  const copyUrl = `${window.location.origin}/form/${formId}/copy`;

  return (
    <>
      <div className="max-w-4xl w-full border border-app-hero-background bg-app-background shadow-md rounded-xl space-y-5 ">
        <div className="p-4 px-16 max-[540px]:px-4 flex items-start gap-3 border-b border-app-hero-background text-app-text-color">
          <Settings className="pt-1" />
          <div className="">
            <h2 className="text-xl font-semibold">Form Settings</h2>
            <p className="text-sm text-app-text-secondary">
              Change your form settings from here.{" "}
            </p>
          </div>
        </div>
        <div className="p-4 px-16 max-[540px]:px-4 space-y-6 pb-12 text-app-text-color">
          {/* Access Setting */}
          {/* <div>
          <h3 className="font-semibold">Access settings</h3>
          <RadioGroup
            value={accessSetting}
            onValueChange={(value) => setAccessSetting(value)}
            className="mt-4 space-y-3 "
          >
            {accessOptions.map((option) => (
              <div key={option.value} className="flex items-start gap-3">
                <RadioGroupItem
                  id={option.value}
                  value={option.value}
                  className="mt-1 border-app-text-color text-app-text-color"
                />
                <div>
                  <label
                    htmlFor={option.value}
                    className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </label>
                  <p className="text-sm text-app-text-secondary">
                    {option.description}
                  </p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </div> */}

          {/* Form Visibility */}
          <div>
            <h3 className="font-semibold">Form visibility</h3>
            <RadioGroup
              value={formVisibility}
              onValueChange={(value) => setFormVisibility(value)}
              className="mt-4 space-y-3"
            >
              {visibilityOptions.map((option) => (
                <div key={option.value} className="flex items-start gap-3">
                  <RadioGroupItem
                    id={option.value}
                    value={option.value}
                    className="mt-1 border-app-text-color text-app-text-color"
                  />
                  <div>
                    <label
                      htmlFor={option.value}
                      className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option.label}
                    </label>
                    <p className="text-sm text-app-text-secondary">
                      {option.description}
                    </p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Copy Form Settings */}
          <div>
            <h3 className="font-semibold">Copy form settings</h3>
            <RadioGroup
              value={copyAllowed}
              onValueChange={(value) => setCopyAllowed(value)}
              className="mt-4 space-y-3"
            >
              {copyOptions.map((option) => (
                <div key={option.value} className="flex items-start gap-3">
                  <RadioGroupItem
                    id={option.value}
                    value={option.value}
                    className="mt-1 border-app-text-color text-app-text-color"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor={option.value}
                      className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option.label}
                    </label>
                    <p className="text-sm text-app-text-secondary">
                      {option.description}
                    </p>
                    {/* Copy Form Button - Only show under "Enable copy form" option */}
                    {option.value === "enabled" && copyAllowed === "enabled" && (
                      <div className="mt-3">
                        <div className="flex items-center gap-2">
                          <Input
                            value={copyUrl}
                            readOnly
                            className="flex-1 text-sm border-app-text-color bg-app-background text-app-text-color"
                            placeholder="Copy link will appear here"
                          />
                          <Button
                            onClick={handleCopyForm}
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2 border-app-text-color text-app-text-color hover:bg-app-text-color hover:text-app-background whitespace-nowrap"
                          >
                            {isCopied ? (
                              <>
                                <Check className="h-3 w-3" />
                                Copied!
                              </>
                            ) : (
                              <>
                                <Copy className="h-3 w-3" />
                                Copy
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        </div>
      </div>
      <AlertDialog open={showPublishDialog} onOpenChange={setShowPublishDialog}>
        <AlertDialogContent className="bg-app-hero-background">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-app-text-color">
              Publish Form Required
            </AlertDialogTitle>
            <AlertDialogDescription className="text-app-text-secondary">
              Please publish the form to enable the copy form functionality.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => setShowPublishDialog(false)}
              className="bg-app-text-color text-app-background hover:bg-app-background hover:text-app-text-color border border-app-border-primary"
            >
              Ok
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default FormSettings;
