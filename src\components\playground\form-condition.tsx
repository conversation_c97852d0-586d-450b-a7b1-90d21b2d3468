import React, { useState, useEffect } from "react";
import {
  Eye,
  Check,
  Workflow,
  GitBranch,
  Trash2,
  PenSquare,
  Plus,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { ThankYouPage } from "./thank-you-response-select";
import {
  Condition,
  ConditionThen,
  ThankYouCondition,
  useAppStore,
} from "@/state-store/app-state-store";
import getFieldOptions from "@/utils/getFields";
import { useGetFormDetails } from "@/api-services/form";
import { useSearchParams } from "next/navigation";
import { conditionOperators, thenActions } from "@/types/condition";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { Button } from "../ui/button";
import { useUpdateCondition } from "@/api-services/form_fields";
import toast from "react-hot-toast";
import { useUpdateThankYouPage } from "@/api-services/form_setting";
import { useGetFormFields } from "@/api-services/form_fields";
import { useGetThankYouPage } from "@/api-services/form_setting";

interface FieldOption {
  id: string;
  text: string;
}

interface CheckboxOption {
  id: string;
  label: string;
  editable?: boolean;
  isOther?: boolean;
  media?: string | null;
}

interface ConditionLogic {
  element_id: string;
  element_type: string;
  operator: string;
  value: string;
}

interface Field {
  id: string;
  component: string;
  dropdownOptions?: FieldOption[];
  checkboxOptions?: CheckboxOption[];
  radioOptions?: FieldOption[];
}

const thankYouActions = ["Show custom message", "Redirect to URL"];

const FormCondition = () => {
  const [activeSection, setActiveSection] = useState<"conditions" | "thankyou">(
    "conditions"
  );
  const [expandedCondition, setExpandedCondition] = useState<string | null>(
    null
  );
  const [editingConditionId, setEditingConditionId] = useState<string | null>(
    null
  );
  const [expandedThankYouCondition, setExpandedThankYouCondition] = useState<
    string | null
  >(null);
  const [editingThankYouConditionId, setEditingThankYouConditionId] = useState<
    string | null
  >(null);
  const [urlError, setUrlError] = useState("");

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId") as string;

  const {
    fields,
    conditions,
    setConditions,
    thankYouConditions,
    setThankYouConditions,
  } = useAppStore();

  const { data: formDetailsResponse } = useGetFormDetails(formId!);

  const isUpdate = formDetailsResponse?.data?.form?.isFieldCreated;

  const selectElementOptions = getFieldOptions(fields);

  const { mutate: updateCondition } = useUpdateCondition(formId!);

  const { mutate: updateThankYouPage, isPending: isUpdateThankYouPagePending } =
    useUpdateThankYouPage();

  const { data: formFieldsData } = useGetFormFields(formId);
  const { data: thankYouData } = useGetThankYouPage(formId);

  useEffect(() => {
    // Clear previous state
    setConditions([]);
    setThankYouConditions([]);
    // Set new conditions if available
    if (formFieldsData?.data?.condition) {
      setConditions(formFieldsData.data.condition);
    }
    // Set new thank you conditions if available
    if (thankYouData?.data?.data?.thank_you_data) {
      setThankYouConditions(thankYouData.data.data.thank_you_data);
    }
  }, [formId, formFieldsData, thankYouData, setConditions, setThankYouConditions]);

  const addNewCondition = () => {
    const newId = (conditions.length + 1).toString();
    const newCondition: Condition = {
      condition_id: newId,
      condition_name: `Condition ${newId}`,
      condition_logic: [
        {
          element_id: selectElementOptions[0].element_id,
          element_type: selectElementOptions[0].element_type,
          operator: conditionOperators[0],
          value: "",
        },
      ],
      rule: "all",
      condition_then: [
        {
          action: thenActions[0],
          element_id: selectElementOptions[0].element_id,
          element_type: selectElementOptions[0].element_type,
        },
      ],
    };
    setConditions([...conditions, newCondition]);
    setExpandedCondition(newId);
    setEditingConditionId(newId);
  };

  const addNewThankYouCondition = () => {
    const newId = (thankYouConditions.length + 1).toString();
    const defaultElement = selectElementOptions?.[0] || {
      element_id: "",
      element_type: "",
    };
    const defaultOperator = conditionOperators?.[0] || "";

    const newCondition: ThankYouCondition = {
      condition_id: newId,
      condition_name: `Thank You Condition ${newId}`,
      condition_logic: [
        {
          element_id: defaultElement.element_id,
          element_type: defaultElement.element_type,
          operator: defaultOperator,
          value: "",
        },
      ],
      action: thankYouActions[0] || "Show custom message",
      custom_message: "",
      rule: "all",
    };
    setThankYouConditions([...thankYouConditions, newCondition]);
    setExpandedThankYouCondition(newId);
    setEditingThankYouConditionId(newId);
  };

  const addNewConditionLogic = (conditionId: string) => {
    const updatedConditions = conditions.map((condition) => {
      if (condition.condition_id === conditionId) {
        return {
          ...condition,
          condition_logic: [
            ...condition.condition_logic,
            {
              element_id: selectElementOptions[0].element_id,
              element_type: selectElementOptions[0].element_type,
              operator: conditionOperators[0],
              value: "",
            },
          ],
        };
      }
      return condition;
    });
    setConditions(updatedConditions);
  };

  const addNewThenAction = (conditionId: string) => {
    const updatedConditions = conditions.map((condition) => {
      if (condition.condition_id === conditionId) {
        return {
          ...condition,
          condition_then: [
            ...condition.condition_then,
            {
              action: thenActions[0],
              element_id: selectElementOptions[0].element_id,
              element_type: selectElementOptions[0].element_type,
            },
          ],
        };
      }
      return condition;
    });
    setConditions(updatedConditions);
  };

  const deleteCondition = (conditionId: string) => {
    setConditions(
      conditions.filter((condition) => condition.condition_id !== conditionId)
    );
    if (expandedCondition === conditionId) {
      setExpandedCondition(null);
    }
    if (editingConditionId === conditionId) {
      setEditingConditionId(null);
    }
  };

  const deleteThankYouCondition = (conditionId: string) => {
    setThankYouConditions(
      thankYouConditions.filter(
        (condition) => condition.condition_id !== conditionId
      )
    );
    if (expandedThankYouCondition === conditionId) {
      setExpandedThankYouCondition(null);
    }
    if (editingThankYouConditionId === conditionId) {
      setEditingThankYouConditionId(null);
    }
  };

  const deleteConditionLogic = (conditionId: string, index: number) => {
    const updatedConditions = conditions.map((condition) => {
      if (condition.condition_id === conditionId) {
        const newLogic = [...condition.condition_logic];
        newLogic.splice(index, 1);
        return { ...condition, condition_logic: newLogic };
      }
      return condition;
    });
    setConditions(updatedConditions);
  };

  const deleteThenAction = (conditionId: string, index: number) => {
    const updatedConditions = conditions.map((condition) => {
      if (condition.condition_id === conditionId) {
        const newThen = [...condition.condition_then];
        newThen.splice(index, 1);
        return { ...condition, condition_then: newThen };
      }
      return condition;
    });
    setConditions(updatedConditions);
  };

  const toggleConditionExpand = (conditionId: string) => {
    setExpandedCondition(
      expandedCondition === conditionId ? null : conditionId
    );
  };

  const toggleThankYouConditionExpand = (conditionId: string) => {
    setExpandedThankYouCondition(
      expandedThankYouCondition === conditionId ? null : conditionId
    );
  };

  const handleEditCondition = (conditionId: string) => {
    setEditingConditionId(conditionId);
    setExpandedCondition(conditionId);
  };

  const handleEditThankYouCondition = (conditionId: string) => {
    setEditingThankYouConditionId(conditionId);
    setExpandedThankYouCondition(conditionId);
  };

  const handleSaveCondition = () => {
    const currentCondition = conditions.find(
      (condition) => condition.condition_id === editingConditionId
    );
    if (!currentCondition) {
      alert("Please select a condition to save");
      return;
    }

    if (!currentCondition.condition_name) {
      alert("Please provide a condition name");
      return;
    }

    if (currentCondition.condition_logic.length === 0) {
      alert("Please add at least one condition rule");
      return;
    }

    const hasEmptyLogic = currentCondition.condition_logic.some(
      (logic) =>
        !logic.element_id ||
        !logic.operator ||
        (logic.operator !== "is empty" &&
          logic.operator !== "is not empty" &&
          !logic.value)
    );

    if (hasEmptyLogic) {
      alert("Please fill in all condition fields");
      return;
    }

    if (currentCondition.condition_then.length === 0) {
      alert("Please add at least one action");
      return;
    }

    const hasEmptyThen = currentCondition.condition_then.some(
      (then) => !then.action || !then.element_id
    );

    if (hasEmptyThen) {
      alert("Please fill in all action fields");
      return;
    }

    setEditingConditionId(null);
  };

  const handleSaveThankYouCondition = () => {
    const currentCondition = thankYouConditions.find(
      (condition) => condition.condition_id === editingThankYouConditionId
    );
    if (!currentCondition) {
      alert("Please select a condition to save");
      return;
    }

    if (!currentCondition.condition_name) {
      alert("Please provide a condition name");
      return;
    }

    if (currentCondition.condition_logic.length === 0) {
      alert("Please add at least one condition rule");
      return;
    }

    const hasEmptyLogic = currentCondition.condition_logic.some(
      (logic) =>
        !logic.element_id ||
        !logic.operator ||
        (logic.operator !== "is empty" &&
          logic.operator !== "is not empty" &&
          !logic.value)
    );

    if (hasEmptyLogic) {
      alert("Please fill in all condition fields");
      return;
    }

    if (!currentCondition.action) {
      alert("Please select an action");
      return;
    }

    if (
      currentCondition.action === "Show specific page" &&
      !currentCondition.page_id
    ) {
      alert("Please select a page to show");
      return;
    }

    setEditingThankYouConditionId(null);
  };

  function handleSaveButtonThankYouCondition() {
    const hasInvalidUrl = thankYouConditions.some(
      (condition) => 
        condition.action === "Redirect to URL" && 
        condition.redirect_url && 
        !validateUrl(condition.redirect_url)
    );

    if (hasInvalidUrl) {
      toast.error("Please enter valid URLs for all redirect conditions");
      return;
    }

    updateThankYouPage(
      {
        formId,
        data: {
          thank_you_type: "condition",
          thank_you_data: thankYouConditions,
          thank_you_url: "",
        },
      },
      {
        onSuccess: () => {
          toast.success("Thank you page updated successfully");
        },
        onError: () => {
          toast.error("Something went wrong");
        },
      }
    );
  }

  const handleCancelEdit = () => {
    if (!editingConditionId) return;

    const condition = conditions.find(
      (c) => c.condition_id === editingConditionId
    );
    if (condition && !condition.condition_name.trim()) {
      deleteCondition(editingConditionId);
    }

    setEditingConditionId(null);
  };

  const handleCancelThankYouEdit = () => {
    if (!editingThankYouConditionId) return;

    const condition = thankYouConditions.find(
      (c) => c.condition_id === editingThankYouConditionId
    );
    if (condition && !condition.condition_name.trim()) {
      deleteThankYouCondition(editingThankYouConditionId);
    } else {
      // Revert any changes made during editing
      const originalCondition = thankYouConditions.find(
        (c) => c.condition_id === editingThankYouConditionId
      );
      if (originalCondition) {
        setThankYouConditions(
          thankYouConditions.map((c) =>
            c.condition_id === editingThankYouConditionId
              ? { ...originalCondition }
              : c
          )
        );
      }
    }

    setEditingThankYouConditionId(null);
    setUrlError("");
  };

  const updateConditionField = (
    field: keyof Condition,
    value: any,
    conditionId: string
  ) => {
    setConditions(
      conditions.map((condition) =>
        condition.condition_id === conditionId
          ? { ...condition, [field]: value }
          : condition
      )
    );
  };

  const updateThankYouConditionField = (
    field: keyof ThankYouCondition,
    value: any,
    conditionId: string
  ) => {
    setThankYouConditions(
      thankYouConditions.map((condition) =>
        condition.condition_id === conditionId
          ? { ...condition, [field]: value }
          : condition
      )
    );
  };

  const updateConditionLogic = (
    index: number,
    field: keyof ConditionLogic,
    value: string | string[],
    conditionId: string
  ) => {
    setConditions(
      conditions.map((condition) => {
        if (condition.condition_id === conditionId) {
          const updatedLogic = [...condition.condition_logic];
          updatedLogic[index] = {
            ...updatedLogic[index],
            [field]: Array.isArray(value)
              ? value?.filter(Boolean).join(",")
              : value,
          };
          return { ...condition, condition_logic: updatedLogic };
        }
        return condition;
      })
    );
  };

  const updateThenAction = (
    index: number,
    field: keyof ConditionThen,
    value: string,
    conditionId: string
  ) => {
    setConditions(
      conditions.map((condition) => {
        if (condition.condition_id === conditionId) {
          const updatedThen = [...condition.condition_then];
          updatedThen[index] = {
            ...updatedThen[index],
            [field]: value,
            ...(field === "element_id"
              ? {
                  element_type:
                    selectElementOptions.find((opt) => opt.element_id === value)
                      ?.element_type || "",
                }
              : {}),
          };
          return { ...condition, condition_then: updatedThen };
        }
        return condition;
      })
    );
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const addNewThankYouConditionLogic = (conditionId: string) => {
    const updatedConditions = thankYouConditions.map((condition) => {
      if (condition.condition_id === conditionId) {
        return {
          ...condition,
          condition_logic: [
            ...condition.condition_logic,
            {
              element_id: selectElementOptions?.[0]?.element_id,
              element_type: selectElementOptions?.[0]?.element_type,
              operator: conditionOperators?.[0],
              value: "",
            },
          ],
        };
      }
      return condition;
    });
    setThankYouConditions(updatedConditions);
  };

  const deleteThankYouConditionLogic = (conditionId: string, index: number) => {
    const updatedConditions = thankYouConditions.map((condition) => {
      if (condition.condition_id === conditionId) {
        const newLogic = [...condition.condition_logic];
        newLogic.splice(index, 1);
        return { ...condition, condition_logic: newLogic };
      }
      return condition;
    });
    setThankYouConditions(updatedConditions);
  };

  const getFieldOptionsForValue = (elementId: string) => {
    const field = fields.find((f) => f.id === elementId) as Field | undefined;
    if (!field) return [];

    switch (field.component) {
      case "DROPDOWN":
        return field.dropdownOptions || [];
      case "CHECKBOX":
        return (
          field.checkboxOptions?.map((opt) => ({
            id: opt.id,
            text: opt.label,
          })) || []
        );
      case "RADIO_BUTTON":
        return field.radioOptions || [];
      case "PHONE_FIELD":
        // Return country codes for phone field
        return [
          { id: "IN", text: "+91 (India)" },
          { id: "US", text: "+1 (USA)" },
          { id: "GB", text: "+44 (UK)" },
          { id: "CA", text: "+1 (Canada)" },
          { id: "AU", text: "+61 (Australia)" },
          { id: "DE", text: "+49 (Germany)" },
          { id: "FR", text: "+33 (France)" },
          { id: "JP", text: "+81 (Japan)" },
          { id: "CN", text: "+86 (China)" },
          { id: "BR", text: "+55 (Brazil)" },
        ];
      default:
        return [];
    }
  };

  function handleSaveButtonCondition() {
    updateCondition(
      { condition: conditions },
      {
        onSuccess: () => {
          toast.success("Condition saved successfully");
        },
        onError: (error) => {
          toast.error("Error saving condition");
        },
      }
    );
  }

  const validateUrl = (url: string) => {
    if (!url) return true; // Empty URL is valid (optional field)
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  };

  const handleUrlChange = (value: string, conditionId: string) => {
    updateThankYouConditionField("redirect_url", value, conditionId);
    if (value && !validateUrl(value)) {
      setUrlError("Please enter a valid URL (e.g., https://example.com)");
    } else {
      setUrlError("");
    }
  };

  return (
    <div className="max-w-6xl w-full border  bg-app-background shadow-sm rounded-xl overflow-hidden flex">
      <div className="w-64 border-r  bg-app-background p-4">
        <div className="flex items-center gap-3 mb-6 text-app-text-color">
          <GitBranch />
          <div>
            <h2 className="text-lg font-semibold">Conditions</h2>
            <p className="text-xs text-app-text-secondary">
              Add conditional logic to your form
            </p>
          </div>
        </div>

        <div className="space-y-2">
          <button
            onClick={() => setActiveSection("conditions")}
            className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
              activeSection === "conditions"
                ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active"
                : "border-l-app-background hover:bg-app-sidebar-hover"
            }`}
          >
            <div className="flex items-center gap-3">
              <div
                className={`p-2 rounded-lg ${
                  activeSection === "conditions"
                    ? "bg-white text-gray-800 shadow-sm"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                <Eye className="w-4 h-4" />
              </div>
              <div className="flex flex-col gap-1">
                <span className="font-medium text-sm">Show/Hide Field</span>
                <p className="text-xs text-app-text-secondary">
                  Change visibility of form fields
                </p>
              </div>
            </div>
          </button>

          <button
            onClick={() => setActiveSection("thankyou")}
            className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
              activeSection === "thankyou"
                ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active"
                : "border-l-app-background hover:bg-app-sidebar-hover"
            }`}
          >
            <div className="flex items-center gap-3">
              <div
                className={`p-2 rounded-lg ${
                  activeSection === "thankyou"
                    ? "bg-white text-gray-800 shadow-sm"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                <Check className="w-4 h-4" />
              </div>
              <div className="flex flex-col">
                <span className="font-medium text-sm">Thank You Page</span>
                <p className="text-xs text-app-text-secondary">
                  Customize your Thank you page
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>

      <div className="flex-1 p-6 bg-app-main-background overflow-y-auto max-h-[calc(100vh-2rem)]">
        <div className="flex items-center gap-3 mb-6 text-app-text-color">
          <GitBranch />
          <div className="flex items-center gap-3 w-full justify-between">
            <div>
              <h2 className="text-lg font-semibold">Form Conditions</h2>
              <p className="text-sm text-app-text-secondary">
                Use conditional logic to personalize every step
              </p>
            </div>

            {activeSection === "conditions" && !editingConditionId && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveButtonCondition}
              >
                Save Condition
              </Button>
            )}
            {activeSection === "thankyou" && !editingThankYouConditionId && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveButtonThankYouCondition}
                disabled={thankYouConditions.some(
                  (condition) => 
                    condition.action === "Redirect to URL" && 
                    condition.redirect_url && 
                    !validateUrl(condition.redirect_url)
                )}
              >
                Save Thank You Condition
              </Button>
            )}
          </div>
        </div>

        {activeSection === "conditions" && (
          <div className="space-y-4">
            {editingConditionId ? (
              <div className="border  rounded-lg overflow-hidden">
                <div className="p-6 bg-app-main-background border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-app-text-color">
                    {conditions.find(
                      (c) => c.condition_id === editingConditionId
                    )?.condition_name || "New Condition"}
                  </h3>
                  <p className="text-sm text-app-text-secondary">
                    Automatically trigger an action if a condition is met
                  </p>
                </div>

                <div className="p-6 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-app-text-color mb-2">
                      Condition Name
                    </label>
                    <input
                      type="text"
                      className="w-full p-2.5 border bg-app-background rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all text-app-text-color"
                      placeholder="e.g. Hide phone for international"
                      value={
                        conditions.find(
                          (c) => c.condition_id === editingConditionId
                        )?.condition_name || ""
                      }
                      onChange={(e) =>
                        updateConditionField(
                          "condition_name",
                          e.target.value,
                          editingConditionId
                        )
                      }
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-4">
                      <div className=" p-4 border rounded-lg space-y-4">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-app-text-color">
                            IF
                          </span>
                          <span className="text-sm text-app-text-secondary">
                            (add multiple conditions with AND/OR)
                          </span>
                        </div>

                        {conditions
                          .find((c) => c.condition_id === editingConditionId)
                          ?.condition_logic.map((logic, index) => (
                            <div key={index} className="relative group">
                              <div className="grid grid-cols-3 gap-4">
                                <div>
                                  <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                    Field
                                  </label>
                                  <select
                                    value={(logic as ConditionLogic)?.element_id || ""}
                                    onChange={(e) => {
                                      setConditions(
                                        conditions.map((condition) => {
                                          if (condition.condition_id === editingConditionId) {
                                            const updatedLogic = [...condition.condition_logic];
                                            updatedLogic[index] = {
                                              ...updatedLogic[index],
                                              element_id: e.target.value,
                                              value: "",
                                            };
                                            return { ...condition, condition_logic: updatedLogic };
                                          }
                                          return condition;
                                        })
                                      );
                                    }}
                                    className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                  >
                                    {selectElementOptions.map((option) => (
                                      <option
                                        key={option.element_id}
                                        value={option.element_id}
                                      >
                                        {option.element_type}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                    Condition
                                  </label>
                                  <select
                                    value={(logic as ConditionLogic)?.operator || conditionOperators[0]}
                                    onChange={(e) => {
                                      setConditions(
                                        conditions.map((c) => {
                                          if (c.condition_id === editingConditionId) {
                                            return {
                                              ...c,
                                              condition_logic: c.condition_logic.map(
                                                (logic, i) =>
                                                  i === index
                                                    ? {
                                                        ...logic,
                                                        operator: e.target.value,
                                                      }
                                                    : logic
                                              ),
                                            };
                                          }
                                          return c;
                                        })
                                      );
                                    }}
                                    className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                  >
                                    {conditionOperators.map((operator) => (
                                      <option key={operator} value={operator}>
                                        {operator}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                    Value
                                  </label>
                                  {(logic as ConditionLogic)?.operator ===
                                    "is empty" ||
                                  (logic as ConditionLogic)?.operator ===
                                    "is not empty" ? (
                                    <input
                                      type="text"
                                      disabled
                                      className="w-full p-2 border bg-app-background rounded-md text-sm "
                                      placeholder="N/A"
                                    />
                                  ) : (
                                    (() => {
                                      const fieldOptions = getFieldOptionsForValue(
                                        (logic as ConditionLogic)?.element_id || ""
                                      );
                                      const selectedField = fields.find(
                                        (f) => f.id === (logic as ConditionLogic)?.element_id
                                      ) as Field | undefined;

                                      if (selectedField?.component === "PHONE_FIELD") {
                                        return (
                                          <PhoneInput
                                            international
                                            defaultCountry="IN"
                                            value={(logic as ConditionLogic)?.value || ""}
                                            onChange={(value) =>
                                              updateConditionLogic(
                                                index,
                                                "value",
                                                value || "",
                                                editingConditionId || ""
                                              )
                                            }
                                            className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                            inputClassName="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                          />
                                        );
                                      }

                                      if (
                                        fieldOptions.length > 0 &&
                                        selectedField &&
                                        [
                                          "DROPDOWN",
                                          "CHECKBOX",
                                          "RADIO_BUTTON",
                                        ].includes(selectedField.component)
                                      ) {
                                        if (selectedField.component === "CHECKBOX") {
                                          return (
                                            <div className="space-y-2 max-h-32 overflow-y-auto p-2 border bg-app-background rounded-md">
                                              {fieldOptions.map((option) => (
                                                <div
                                                  key={option.id}
                                                  className="flex items-center space-x-2"
                                                >
                                                  <input
                                                    type="checkbox"
                                                    id={`${editingConditionId}-${option.id}`}
                                                    checked={
                                                      (logic as ConditionLogic)?.value
                                                        ?.split(",")
                                                        .includes(option.text) || false
                                                    }
                                                    onChange={(e) => {
                                                      const currentValues =
                                                        (logic as ConditionLogic)?.value?.split(",") || [];
                                                      let newValues: string[];
                                                      if (e.target.checked) {
                                                        newValues = [
                                                          ...currentValues,
                                                          option.text,
                                                        ];
                                                      } else {
                                                        newValues = currentValues.filter(
                                                          (v) => v !== option.text
                                                        );
                                                      }
                                                      updateConditionLogic(
                                                        index,
                                                        "value",
                                                        newValues,
                                                        editingConditionId || ""
                                                      );
                                                    }}
                                                    className="h-4 w-4 rounded bg-app-background text-app-text-color focus:ring-gray-500"
                                                  />
                                                  <label
                                                    htmlFor={`${editingConditionId}-${option.id}`}
                                                    className="text-sm text-gray-700"
                                                  >
                                                    {option.text}
                                                  </label>
                                                </div>
                                              ))}
                                            </div>
                                          );
                                        }
                                        return (
                                          <select
                                            value={(logic as ConditionLogic)?.value || ""}
                                            onChange={(e) =>
                                              updateConditionLogic(
                                                index,
                                                "value",
                                                e.target.value,
                                                editingConditionId || ""
                                              )
                                            }
                                            className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                          >
                                            <option value="">Select an option</option>
                                            {fieldOptions.map((option) => (
                                              <option key={option.id} value={option.text}>
                                                {option.text}
                                              </option>
                                            ))}
                                          </select>
                                        );
                                      }

                                      return (
                                        <input
                                          type="text"
                                          value={(logic as ConditionLogic)?.value || ""}
                                          onChange={(e) =>
                                            updateConditionLogic(
                                              index,
                                              "value",
                                              e.target.value,
                                              editingConditionId || ""
                                            )
                                          }
                                          className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                        />
                                      );
                                    })()
                                  )}
                                </div>
                              </div>
                              {conditions.find(
                                (c) => c.condition_id === editingConditionId
                              )!?.condition_logic?.length > 1 && (
                                <button
                                  onClick={() =>
                                    deleteConditionLogic(
                                      editingConditionId,
                                      index
                                    )
                                  }
                                  className="absolute -right-8 top-1/2 -translate-y-1/2 p-2 hover:bg-gray-100 rounded-md text-gray-500 hover:text-red-600 transition-colors opacity-0 group-hover:opacity-100"
                                  title="Delete condition"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          ))}

                        <button
                          onClick={() =>
                            addNewConditionLogic(editingConditionId)
                          }
                          className="flex items-center gap-2 text-sm text-app-text-color hover:text-app-text-secondary mt-2"
                        >
                          <Plus className="w-4 h-4" />
                          Add another condition
                        </button>
                      </div>

                      <div className="flex items-center justify-center gap-2 p-2  border  rounded-md mb-4">
                        <span className="text-app-text-color font-medium">
                          IF
                        </span>
                        <select
                          value={
                            conditions.find(
                              (c) => c.condition_id === editingConditionId
                            )?.rule || "all"
                          }
                          onChange={(e) =>
                            updateConditionField(
                              "rule",
                              e.target.value as "all" | "any",
                              editingConditionId
                            )
                          }
                          className="text-sm border rounded px-2 py-1 bg-app-background focus:ring-gray-500 focus:border-gray-500 min-w-[80px]"
                        >
                          <option value="any">Any</option>
                          <option value="all">All</option>
                        </select>
                        <span className="text-sm text-app-text-color">
                          of the
                        </span>
                        <span className="text-app-text-color0 font-medium">
                          "IF"
                        </span>
                        <span className="text-sm text-app-text-color">
                          Rules are matched
                        </span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className=" p-4 border  rounded-lg space-y-4">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-app-text-color">
                            THEN
                          </span>
                          <span className="text-sm text-app-text-secondary">
                            (what should happen when conditions are met)
                          </span>
                        </div>

                        {conditions
                          .find((c) => c.condition_id === editingConditionId)
                          ?.condition_then.map((then, index) => (
                            <div key={index} className="relative group">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                    Action
                                  </label>
                                  <select
                                    value={then.action}
                                    onChange={(e) =>
                                      updateThenAction(
                                        index,
                                        "action",
                                        e.target.value,
                                        editingConditionId
                                      )
                                    }
                                    className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                  >
                                    {thenActions.map((action) => (
                                      <option key={action} value={action}>
                                        {action}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                    Target Field
                                  </label>
                                  <select
                                    value={then.element_id}
                                    onChange={(e) =>
                                      updateThenAction(
                                        index,
                                        "element_id",
                                        e.target.value,
                                        editingConditionId
                                      )
                                    }
                                    className={`w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500 ${
                                      then.action === "Show"
                                        ? "bg-gray-100 cursor-not-allowed"
                                        : ""
                                    }`}
                                    disabled={then.action === "Show"}
                                  >
                                    {selectElementOptions.map((option) => (
                                      <option
                                        key={option.element_id}
                                        value={option.element_id}
                                      >
                                        {option.element_type}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                              {conditions.find(
                                (c) => c.condition_id === editingConditionId
                              )!?.condition_then.length > 1 && (
                                <button
                                  onClick={() =>
                                    deleteThenAction(editingConditionId, index)
                                  }
                                  className="absolute top-0 right-0 p-1 hover:bg-gray-100 rounded-md text-gray-500 hover:text-red-600 transition-colors opacity-0 group-hover:opacity-100"
                                  title="Delete action"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          ))}

                        <button
                          onClick={() => addNewThenAction(editingConditionId)}
                          className="flex items-center gap-2 text-sm text-app-text-color hover:text-app-text-secondary mt-2"
                        >
                          <Plus className="w-4 h-4" />
                          Add another action
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 pt-4 border-t">
                    <button
                      onClick={handleCancelEdit}
                      className=" hover:bg-app-background bg-app-text-color border border-[#1F311C] text-app-background hover:text-app-text-color  px-4 py-2 rounded-md transition-colors text-sm font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveCondition}
                      className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color  px-4 py-2 rounded-md transition-colors text-sm font-medium"
                    >
                      Save Condition
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <button
                  onClick={addNewCondition}
                  disabled={!isUpdate}
                  className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-app-text-secondary bg-app-background hover:bg-app-hero-background hover:text-app-text-color transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                  Add New Condition
                </button>

                <div className="space-y-3 overflow-y-auto max-h-[calc(100vh-20rem)]">
                  {conditions.map((condition) => (
                    <div
                      key={condition.condition_id}
                      className="border rounded-lg overflow-hidden transition-all hover:shadow-sm"
                    >
                      <div
                        className="flex items-center gap-3 p-4 hover:bg-app-hero-background cursor-pointer"
                        onClick={() =>
                          toggleConditionExpand(condition.condition_id)
                        }
                      >
                        {expandedCondition === condition.condition_id ? (
                          <ChevronDown className="w-4 h-4 text-app-text-color" />
                        ) : (
                          <ChevronRight className="w-4 h-4 text-app-text-color" />
                        )}
                        <Eye className="w-4 h-4 text-app-text-color" />
                        <h3 className="font-medium text-sm flex-1">
                          {condition.condition_name}
                        </h3>
                        <div className="flex gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteCondition(condition.condition_id);
                            }}
                            className="p-1.5 hover:bg-app-sidebar-hover-active rounded text-app-text-color hover:text-red-500 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditCondition(condition.condition_id);
                            }}
                            className="p-1.5 hover:bg-app-sidebar-hover-active rounded text-app-text-color hover:text-indigo-600 transition-colors"
                          >
                            <PenSquare className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      {expandedCondition === condition.condition_id && (
                        <div className="p-4 border-t">
                          <div className="space-y-3 text-sm">
                            <div className="flex items-start gap-2">
                              <span className="font-medium text-app-text-color mt-0.5">
                                IF
                              </span>
                              <div className="bg-app-background p-3 rounded-md border  flex-1">
                                {condition.condition_logic.map((logic: ConditionLogic, index: number): React.ReactNode => {
                                  // Find the field label by element_id
                                  const fieldObj = fields.find(f => f.id === logic.element_id);
                                  const fieldLabel = fieldObj?.title || logic.element_type;
                                  return (
                                    <p key={index} className="text-app-text-secondary">
                                      <span className="font-medium">{fieldLabel}</span>
                                      <span className="text-purple-600 mx-2">{logic.operator}</span>
                                      {logic.operator !== "is empty" && logic.operator !== "is not empty" && (
                                        <span className="text-app-text-color">"{logic.value}"</span>
                                      )}
                                    </p>
                                  );
                                })}
                                <p className="text-sm text-app-text-secondary mt-2">
                                  ({condition.rule === "all" ? "All" : "Any"} of these conditions must be met)
                                </p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <span className="font-medium text-app-text-color mt-0.5">
                                THEN
                              </span>
                              <div className="bg-app-background p-3 rounded-md border  flex-1">
                                {condition.condition_then.map((then, index) => (
                                  <p
                                    key={index}
                                    className="text-app-text-secondary"
                                  >
                                    <span className="font-medium">
                                      {then.action}
                                    </span>
                                    <span className="mx-2">the field</span>
                                    <span className="text-app-text-color">
                                      "{then.element_type}"
                                    </span>
                                  </p>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        )}

        {activeSection === "thankyou" && (
          <div className="space-y-4">
            {editingThankYouConditionId ? (
              <div className="border rounded-lg overflow-hidden">
                <div className="p-6  border-b ">
                  <h3 className="text-lg font-semibold text-app-text-color">
                    {thankYouConditions.find(
                      (c) => c.condition_id === editingThankYouConditionId
                    )?.condition_name || "New Thank You Condition"}
                  </h3>
                  <p className="text-sm text-app-text-secondary">
                    Customize the thank you page based on form responses
                  </p>
                </div>

                <div className="p-6 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-app-text-color mb-2">
                      Condition Name
                    </label>
                    <input
                      type="text"
                      placeholder="e.g. Premium customer thank you"
                      value={
                        thankYouConditions.find(
                          (c) => c.condition_id === editingThankYouConditionId
                        )?.condition_name || ""
                      }
                      onChange={(e) =>
                        updateThankYouConditionField(
                          "condition_name",
                          e.target.value,
                          editingThankYouConditionId
                        )
                      }
                      className="w-full p-2.5 border bg-app-background text-app-text-color rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                    />
                  </div>

                  <div className=" p-4 border rounded-lg space-y-4">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-app-text-color">
                        IF
                      </span>
                      <span className="text-sm text-app-text-secondary">
                        this field meets the condition
                      </span>
                    </div>
                    {thankYouConditions
                      .find(
                        (c) => c.condition_id === editingThankYouConditionId
                      )
                      ?.condition_logic.map((logic, index) => (
                        <div key={index} className="relative group">
                          <div className="grid grid-cols-3 gap-4">
                            <div>
                              <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                Field
                              </label>
                              <select
                                value={(logic as ConditionLogic)?.element_id || ""}
                                onChange={(e) => {
                                  setThankYouConditions(
                                    thankYouConditions.map((condition) => {
                                      if (condition.condition_id === editingThankYouConditionId) {
                                        const updatedLogic = [...condition.condition_logic];
                                        const selectedOption = selectElementOptions.find(
                                          (opt) => opt.element_id === e.target.value
                                        ) || { element_id: "", element_type: "" };
                                        updatedLogic[index] = {
                                          ...updatedLogic[index],
                                          element_id: selectedOption.element_id,
                                          element_type: selectedOption.element_type,
                                          value: "",
                                        };
                                        return { ...condition, condition_logic: updatedLogic };
                                      }
                                      return condition;
                                    })
                                  );
                                }}
                                className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                              >
                                <option value="">Select a field</option>
                                {selectElementOptions.map((option) => (
                                  <option
                                    key={option.element_id}
                                    value={option.element_id}
                                  >
                                    {option.element_type}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                Condition
                              </label>
                              <select
                                value={
                                  (logic as ConditionLogic)?.operator || ""
                                }
                                onChange={(e) => {
                                  setThankYouConditions(
                                    thankYouConditions.map((condition) => {
                                      if (condition.condition_id === editingThankYouConditionId) {
                                        const updatedLogic = [...condition.condition_logic];
                                        updatedLogic[index] = {
                                          ...updatedLogic[index],
                                          operator: e.target.value,
                                        };
                                        return { ...condition, condition_logic: updatedLogic };
                                      }
                                      return condition;
                                    })
                                  );
                                }}
                                className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                              >
                                <option value="">Select a condition</option>
                                {conditionOperators.map((operator) => (
                                  <option key={operator} value={operator}>
                                    {operator}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                                Value
                              </label>
                              {(logic as ConditionLogic)?.operator ===
                                "Is Empty" ||
                              (logic as ConditionLogic)?.operator ===
                                "Is Not Empty" ? (
                                <input
                                  type="text"
                                  disabled
                                  className="w-full p-2 border bg-app-background rounded-md text-sm bg-gray-100"
                                  placeholder="N/A"
                                />
                              ) : (
                                (() => {
                                  const fieldOptions = getFieldOptionsForValue(
                                    (logic as ConditionLogic)?.element_id || ""
                                  );
                                  const selectedField = fields.find(
                                    (f) =>
                                      f.id ===
                                      (logic as ConditionLogic)?.element_id
                                  ) as Field | undefined;

                                  if (
                                    selectedField?.component === "PHONE_FIELD"
                                  ) {
                                    return (
                                      <PhoneInput
                                        international
                                        defaultCountry="IN"
                                        value={(logic as ConditionLogic)?.value || ""}
                                        onChange={(value) =>
                                          setThankYouConditions(
                                            thankYouConditions.map((condition) => {
                                              if (condition.condition_id === editingThankYouConditionId) {
                                                const updatedLogic = [...condition.condition_logic];
                                                updatedLogic[index] = {
                                                  ...updatedLogic[index],
                                                  value: value || "",
                                                };
                                                return { ...condition, condition_logic: updatedLogic };
                                              }
                                              return condition;
                                            })
                                          )
                                        }
                                        className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                        inputClassName="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                      />
                                    );
                                  }

                                  if (
                                    fieldOptions.length > 0 &&
                                    selectedField &&
                                    [
                                      "DROPDOWN",
                                      "CHECKBOX",
                                      "RADIO_BUTTON",
                                    ].includes(selectedField.component)
                                  ) {
                                    if (
                                      selectedField.component === "CHECKBOX"
                                    ) {
                                      return (
                                        <div className="space-y-2 max-h-32 overflow-y-auto p-2 border  rounded-md">
                                          {fieldOptions.map((option: any) => (
                                            <div
                                              key={option.id}
                                              className="flex items-center space-x-2"
                                            >
                                              <input
                                                type="checkbox"
                                                id={`${editingThankYouConditionId}-${option.id}`}
                                                checked={
                                                  (logic as ConditionLogic)?.value?.split(",").includes(option.text) || false
                                                }
                                                onChange={(e) => {
                                                  const currentValues = (logic as ConditionLogic)?.value?.split(",") || [];
                                                  let newValues: string[];
                                                  if (e.target.checked) {
                                                    newValues = [...currentValues, option.text];
                                                  } else {
                                                    newValues = currentValues.filter((v) => v !== option.text);
                                                  }
                                                  setThankYouConditions(
                                                    thankYouConditions.map((condition) => {
                                                      if (condition.condition_id === editingThankYouConditionId) {
                                                        const updatedLogic = [...condition.condition_logic];
                                                        updatedLogic[index] = {
                                                          ...updatedLogic[index],
                                                          value: newValues.filter(Boolean).join(","),
                                                        };
                                                        return { ...condition, condition_logic: updatedLogic };
                                                      }
                                                      return condition;
                                                    })
                                                  );
                                                }}
                                                className="h-4 w-4 rounded bg-app-background text-app-text-color focus:ring-gray-500"
                                              />
                                              <label htmlFor={`${editingThankYouConditionId}-${option.id}`} className="text-sm text-app-text-color">
                                                {option.text}
                                              </label>
                                            </div>
                                          ))}
                                        </div>
                                      );
                                    }
                                    return (
                                      <select
                                        value={(logic as ConditionLogic)?.value || ""}
                                        onChange={(e) =>
                                          setThankYouConditions(
                                            thankYouConditions.map((condition) => {
                                              if (condition.condition_id === editingThankYouConditionId) {
                                                const updatedLogic = [...condition.condition_logic];
                                                updatedLogic[index] = {
                                                  ...updatedLogic[index],
                                                  value: e.target.value,
                                                };
                                                return { ...condition, condition_logic: updatedLogic };
                                              }
                                              return condition;
                                            })
                                          )
                                        }
                                        className="w-full p-2 border bg-app-background rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                      >
                                        <option value="">Select an option</option>
                                        {fieldOptions.map((option: any) => (
                                          <option key={option.id} value={option.text}>
                                            {option.text}
                                          </option>
                                        ))}
                                      </select>
                                    );
                                  }

                                  return (
                                    <input
                                      type="text"
                                      placeholder="Enter value"
                                      value={(logic as ConditionLogic)?.value || ""}
                                      onChange={(e) =>
                                        setThankYouConditions(
                                          thankYouConditions.map((condition) => {
                                            if (condition.condition_id === editingThankYouConditionId) {
                                              const updatedLogic = [...condition.condition_logic];
                                              updatedLogic[index] = {
                                                ...updatedLogic[index],
                                                value: e.target.value,
                                              };
                                              return { ...condition, condition_logic: updatedLogic };
                                            }
                                            return condition;
                                          })
                                        )
                                      }
                                      className="w-full p-2 border bg-app-background text-app-text-color rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                                    />
                                  );
                                })()
                              )}
                            </div>
                          </div>
                          {(thankYouConditions.find(
                            (c) => c.condition_id === editingThankYouConditionId
                          )?.condition_logic?.length ?? 0) > 1 && (
                            <button
                              onClick={() =>
                                deleteThankYouConditionLogic(
                                  editingThankYouConditionId,
                                  index
                                )
                              }
                              className="absolute -right-8 top-1/2 -translate-y-1/2 p-2 hover:bg-gray-100 rounded-md text-gray-500 hover:text-red-600 transition-colors opacity-0 group-hover:opacity-100"
                              title="Delete condition"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      ))}
                  </div>

                  <div className="flex items-center justify-center gap-2 p-2  border  rounded-md mb-4">
                    <span className="text-app-text-secondary font-medium">
                      IF
                    </span>
                    <select
                      value={
                        thankYouConditions.find(
                          (c) => c.condition_id === editingThankYouConditionId
                        )?.rule || "all"
                      }
                      onChange={(e) => {
                        const updatedConditions = thankYouConditions.map(
                          (c) => {
                            if (c.condition_id === editingThankYouConditionId) {
                              return {
                                ...c,
                                rule: e.target.value as "all" | "any",
                              };
                            }
                            return c;
                          }
                        );
                        setThankYouConditions(updatedConditions);
                      }}
                      className="text-sm border bg-app-background rounded px-2 py-1 focus:ring-gray-500 focus:border-gray-500 min-w-[80px]"
                    >
                      <option value="any">Any</option>
                      <option value="all">All</option>
                    </select>
                    <span className="text-sm text-app-text-secondary">
                      of the
                    </span>
                    <span className="text-app-text-secondary font-medium">
                      "IF"
                    </span>
                    <span className="text-sm text-app-text-secondary">
                      Rules are matched
                    </span>
                  </div>

                  <button
                    onClick={() =>
                      addNewThankYouConditionLogic(editingThankYouConditionId)
                    }
                    className="flex items-center gap-2 text-sm text-app-text-color hover:text-app-text-secondary mt-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add another condition
                  </button>

                  <div className=" p-4 border rounded-lg space-y-4">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-app-text-color">
                        THEN
                      </span>
                      <span className="text-sm text-app-text-secondary">
                        perform this action
                      </span>
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-app-text-secondary mb-1 uppercase tracking-wider">
                          Action
                        </label>
                        <select
                          value={
                            thankYouConditions.find(
                              (c) =>
                                c.condition_id === editingThankYouConditionId
                            )?.action || thankYouActions[0]
                          }
                          onChange={(e) =>
                            updateThankYouConditionField(
                              "action",
                              e.target.value,
                              editingThankYouConditionId
                            )
                          }
                          className="w-full p-2 border bg-app-background text-app-text-color rounded-md text-sm focus:ring-gray-500 focus:border-gray-500"
                        >
                          {thankYouActions.map((action) => (
                            <option key={action} value={action}>
                              {action}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {thankYouConditions.find(
                      (c) => c.condition_id === editingThankYouConditionId
                    )?.action === "Show custom message" && (
                      <div className="mt-4 border-t pt-4">
                        <ThankYouPage
                          onMessageChange={(message) =>
                            updateThankYouConditionField(
                              "custom_message",
                              message,
                              editingThankYouConditionId
                            )
                          }
                        />
                      </div>
                    )}

                    {thankYouConditions.find(
                      (c) => c.condition_id === editingThankYouConditionId
                    )?.action === "Redirect to URL" && (
                      <div className="mt-4">
                        <label className="block text-sm font-medium text-app-text-secondary mb-2">
                          Redirect URL
                        </label>
                        <input
                          type="url"
                          placeholder="https://example.com/thank-you"
                          value={
                            thankYouConditions.find(
                              (c) =>
                                c.condition_id === editingThankYouConditionId
                            )?.redirect_url || ""
                          }
                          onChange={(e) =>
                            handleUrlChange(e.target.value, editingThankYouConditionId)
                          }
                          className="w-full p-2.5 border bg-app-background text-app-text-color placeholder:text-app-text-secondary rounded-md focus:ring-2 focus:ring-gray-500 focus:border-gray-500 transition-all"
                        />
                        {urlError && (
                          <p className="text-xs text-red-500 mt-2">
                            {urlError}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end gap-3 pt-4 border-t">
                    <button
                      onClick={handleCancelThankYouEdit}
                      className="hover:bg-app-background bg-app-text-color border border-[#1F311C] text-app-background hover:text-app-text-color  px-4 py-2 rounded-md transition-colors text-sm font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveThankYouCondition}
                      disabled={thankYouConditions.some(
                        (condition) => 
                          condition.action === "Redirect to URL" && 
                          condition.redirect_url && 
                          !validateUrl(condition.redirect_url)
                      )}
                      className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color  px-4 py-2 rounded-md transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Save Condition
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <button
                  onClick={addNewThankYouCondition}
                  className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-app-text-secondary bg-app-background hover:bg-app-hero-background hover:text-app-text-color transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus className="w-4 h-4" />
                  Add New Thank You Condition
                </button>

                <div className="space-y-3 overflow-y-auto max-h-[calc(100vh-20rem)]">
                  {thankYouConditions.map((condition) => (
                    <div
                      key={condition.condition_id}
                      className="border rounded-lg overflow-hidden transition-all hover:shadow-sm"
                    >
                      <div
                        className="flex items-center gap-3 p-4 bg-app-background hover:bg-app-hero-background cursor-pointer"
                        onClick={() =>
                          toggleThankYouConditionExpand(condition.condition_id)
                        }
                      >
                        {expandedThankYouCondition ===
                        condition.condition_id ? (
                          <ChevronDown className="w-4 h-4 text-app-text-secondary" />
                        ) : (
                          <ChevronRight className="w-4 h-4 text-app-text-secondary" />
                        )}
                        <Check className="w-4 h-4 text-app-text-secondary" />
                        <h3 className="font-medium text-sm flex-1">
                          {condition.condition_name}
                        </h3>
                        <div className="flex gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteThankYouCondition(condition.condition_id);
                            }}
                            className="p-1.5 hover:bg-app-sidebar-hover-active rounded text-app-text-secondary hover:text-red-500 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditThankYouCondition(
                                condition.condition_id
                              );
                            }}
                            className="p-1.5 hover:bg-app-sidebar-hover-active rounded text-app-text-secondary hover:text-indigo-600 transition-colors"
                          >
                            <PenSquare className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      {expandedThankYouCondition === condition.condition_id && (
                        <div className="p-4 border-t ">
                          <div className="space-y-3 text-sm">
                            <div className="flex items-start gap-2">
                              <span className="font-medium text-app-text-color mt-0.5">
                                IF
                              </span>
                              <div className="bg-app-background p-3 rounded-md border  flex-1">
                                {condition.condition_logic.map((logic: ConditionLogic, index: number): React.ReactNode => {
                                  // Find the field label by element_id
                                  const fieldObj = fields.find(f => f.id === logic.element_id);
                                  const fieldLabel = fieldObj?.title || logic.element_type;
                                  return (
                                    <p key={index} className="text-app-text-secondary">
                                      <span className="font-medium">{fieldLabel}</span>
                                      <span className="text-purple-600 mx-2">{logic.operator}</span>
                                      {logic.operator !== "is empty" && logic.operator !== "is not empty" && (
                                        <span className="text-app-text-color">"{logic.value}"</span>
                                      )}
                                    </p>
                                  );
                                })}
                                <p className="text-sm text-app-text-secondary mt-2">
                                  ({condition.rule === "all" ? "All" : "Any"} of these conditions must be met)
                                </p>
                              </div>
                            </div>
                            <div className="flex items-start gap-2">
                              <span className="font-medium text-app-text-color mt-0.5">
                                THEN
                              </span>
                              <div className="p-3 rounded-md border flex-1 bg-app-background">
                                <p className="text-app-text-secondary">
                                  <span className="font-medium">
                                    {condition.action} {""}
                                  </span>
                                  {condition.action ===
                                    "Show custom message" && (
                                    <span className="text-app-text-color">
                                      "{condition.custom_message}"
                                    </span>
                                  )}
                                  {condition.action === "Redirect to URL" && (
                                    <span className="text-app-text-color">
                                      "{condition.redirect_url}"
                                    </span>
                                  )}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FormCondition;
