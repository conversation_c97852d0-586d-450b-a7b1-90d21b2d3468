import React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ThumbsUp, ThumbsDown, CheckCircle, Circle } from "lucide-react";

const WhatsNewPage = () => {
  const features = [
    {
      id: 1,
      title:
        "Introducing Pre-built Header changes feature in Automate forms app",
      description:
        "We're excited to introduce a new Form pre-built Header Customization feature in our form builder app. This functionality allows users to easily change and personalize the header of their forms by uploading images, selecting banners, or applying preset themes. Whether you want to match your brand, set a professional tone, or add a creative touch, the new header customization gives you full control over the first impression your form makes. It's simple, flexible, and designed to enhance both aesthetics and engagement.",
      mainImage: "/change-header.png",
      secondaryImage: "/cards.png",
      tags: ["Web App", "Customization", "New feature"],
      releaseDate: "30 May,2024",
      likes: 250,
      dislikes: 20,
      isRead: true,
    },
    {
      id: 2,
      title: "Introducing new Integration in Automate forms app",
      description:
        "We're thrilled to introduce the New Integration Feature in our form builder application. This update enables users to seamlessly connect their forms with popular third-party tools like Google Sheets, Slack, WhatsApp, email platforms, and more. With just a few clicks, you can automate workflows, sync responses in real-time, and trigger actions across your favorite apps. This integration capability empowers users to streamline data management, boost productivity, and make their forms more powerful than ever before.",
      mainImage: "/integration-new.png",
      secondaryImage: "/integration-group.png",
      tags: ["Web App", "Integration", "New feature", "Gmail Integration"],
      releaseDate: "30 May,2024",
      likes: 250,
      dislikes: 20,
      isRead: false,
    },
  ];

  return (
    <div>
      <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
        <div className="flex flex-col md:flex-row items-center justify-between bg-app-background border rounded-2xl p-8 gap-8">
          {/* Header */}
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-app-text-color">
              What's New in Automate Forms
            </h1>
            <p className="text-app-text-secondary mt-2 text-sm sm:text-base">
              Learn about new features and improved functionalities in Automate
              form app
            </p>
          </div>

          {/* Placeholder for the image, as it's not in the public directory */}
          <Image
            src="/whats-new.png"
            alt="What's New"
            width={100}
            height={100}
            quality={100}
            className="h-full w-auto max-h-56"
          />
        </div>
      </div>
      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-8">
        <Button>New Features</Button>
        <Button variant="outline">Improved Functionalities</Button>
      </div>

      <div>
        {/* Features List */}
        <div className="space-y-6">
          {features.map((feature, index) => (
            <div key={feature.id}>
              <div className="bg-app-background p-6 rounded-lg">
                {/* Images section */}
                <div className="mb-6 grid grid-cols-2 max-[640px]:grid-cols-1 justify-center items-center w-full gap-6 p-8">
                  <Image
                    src={feature.mainImage}
                    alt={feature.title}
                    height={500}
                    width={500}
                    quality={100}
                    className="rounded-lg w-full max-h-40"
                  />
                  <Image
                    src={feature.secondaryImage}
                    alt=""
                    height={500}
                    width={500}
                    quality={100}
                    className="rounded-lg w-full max-h-40"
                  />
                </div>

                {/* Tags and Date */}
                <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-4 gap-4 sm:gap-0">
                  <div className="flex flex-wrap items-center gap-2">
                    {feature.tags.map((tag) => (
                      <Badge
                        variant="outline"
                        key={tag}
                        className="text-xs font-light"
                      >
                        {tag}
                      </Badge>
                    ))}
                    <span className="text-xs sm:text-sm text-app-text-secondary">
                      Release date • {feature.releaseDate}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 cursor-pointer text-sm font-medium text-gray-600">
                    <span className="text-app-text-secondary">Mark as Read</span>
                    {feature.isRead ? (
                      <CheckCircle className="text-green-500 w-5 h-5" />
                    ) : (
                      <Circle className="text-gray-400 w-5 h-5" />
                    )}
                  </div>
                </div>

                {/* Title and Description */}
                <h2 className="text-xl sm:text-2xl font-bold mb-2 text-app-text-color">
                  {feature.title}
                </h2>
                <p className="text-app-text-secondary mb-6 text-sm sm:text-base leading-relaxed">
                  {feature.description}
                </p>

                {/* Actions and Stats */}
                <div className="flex items-center gap-6">
                  <Button
                    size="sm"
                    className="bg-gray-800 text-white hover:bg-gray-700 text-xs px-4 py-2"
                  >
                    EXPLORE NOW <span className="ml-1 font-bold">→</span>
                  </Button>
                  <div className="flex items-center gap-2 text-app-text-secondary">
                    <ThumbsUp className="w-5 h-5 cursor-pointer hover:text-app-text-color" />
                    <span className="text-sm font-medium">{feature.likes}</span>
                  </div>
                  <div className="flex items-center gap-2 text-app-text-secondary">
                    <ThumbsDown className="w-5 h-5 cursor-pointer hover:text-app-text-color" />
                    <span className="text-sm font-medium">
                      {feature.dislikes}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WhatsNewPage;
