import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { QueryProvider } from "@/provider/QueryClientProvider";
import { Toaster } from "react-hot-toast";
import { PostHogProvider } from "@/provider/PostHogProvider";
import { <PERSON>risp<PERSON>rovider } from "@/provider/CrispProvider";
import {
  montserrat,
  raleway,
  courierPrime,
  eb<PERSON><PERSON>mond,
  imprima,
  lexend,
  lora,
  merriweather,
  nunito,
  oswald,
  pacifico,
  playfairDisplay,
  roboto,
  robotoMono,
} from "./fonts";

export const metadata: Metadata = {
  title: "AutomateForms - Forms Made Simple, Results Made Powerful",
  description:
    "AutomateForms lets you design stunning forms, automate responses, integrate with popular apps, and analyze insights effortlessly—all in one platform.",
  openGraph: {
    title: "AutomateForms - AI-Powered Form Builder & Automation Platform",
    description:
      "Create, automate, and analyze forms with AutomateForms. Enjoy advanced features like conditional logic, AI assistance, team collaboration, and seamless integrations. Start for free today!",
    url: "https://automateforms.ai/",
    images: "https://automateforms.ai/assets/logo.svg",
    type: "website",
  },
  keywords: [
    "form builder",
    "online forms",
    "AI form automation",
    "form analytics",
    "conditional logic forms",
    "form templates",
    "team collaboration forms",
    "automate responses",
    "integrate forms with apps",
    "affordable form builder",
  ],
  twitter: {
    card: "summary_large_image",
    title: "AutomateForms - AI-Powered Form Builder & Automation Platform",
    description:
      "Build, automate, and analyze forms with ease using AutomateForms. Access powerful features like AI, conditional logic, and integrations. Try free today!",
    images: "https://automateforms.ai/assets/social.png",
  },
  publisher: "AutomateForms",
  robots: "index, follow",
};


const canonicalUrl = `https://automateforms.ai/`;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${montserrat.variable} ${raleway.variable} ${courierPrime.variable} ${ebGaramond.variable} ${imprima.variable} ${lexend.variable} ${lora.variable} ${merriweather.variable} ${nunito.variable} ${oswald.variable} ${pacifico.variable} ${playfairDisplay.variable} ${roboto.variable} ${robotoMono.variable}`}
    >
      <body className={roboto.className}>
        <PostHogProvider>
          <QueryProvider>
            <CrispProvider />
            {children}
            <Toaster
              position="top-center"
              toastOptions={{
                style: {
                  marginTop: "4rem",
                  zIndex: 1000,
                  pointerEvents: "none",
                  left: "50%",
                  transform: "translateX(-50%)",
                },
                duration: 3000,
              }}
            />
          </QueryProvider>
        </PostHogProvider>
      </body>
    </html>
  );
}
