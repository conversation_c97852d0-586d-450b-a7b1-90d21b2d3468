import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";
import { useSearchParams } from "next/navigation";

const baseEndpoint = `/v1/forms`;

async function getFormElements() {
  return makeRequest({
    endpoint: `${baseEndpoint}/getformfields`,
    method: "GET",
  });
}

const useGetFormElements = () => {
  return useQuery({
    queryKey: QueryKeys.FORM_ELEMENTS,
    queryFn: getFormElements,
  });
};

async function createForm(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/createform`,
    method: "POST",
    data,
  });
}

const useCreateForm = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createForm,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["usageStats"],
      });
    },
  });
};

async function updateForm(id: string, data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/updateform/${id}`,
    method: "PUT",
    data,
  });
}

const useUpdateForm = (id: string) => {
  return useMutation({
    mutationFn: (data: any) => updateForm(id, data),
    mutationKey: QueryKeys.UPDATE_FORM(id),
  });
};

async function getUserForms({
  limit,
  offset,
}: {
  limit: number;
  offset: number;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}?limit=${limit}&offset=${offset}`,
    method: "GET",
  });
}

const useGetUserForms = ({
  limit,
  offset,
}: {
  limit: number;
  offset: number;
}, options?: any) => {
  return useQuery({
    queryKey: QueryKeys.USER_FORMS(limit, offset),
    queryFn: () => getUserForms({ limit, offset }),
    ...options,
  });
};

async function publishForm(id: string, data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/publish/${id}`,
    method: "PUT",
    data,
  });
}

const usePublishForm = (id: string) => {
  return useMutation({
    mutationFn: (data: any) => publishForm(id, data),
  });
};

async function getFormDetails(id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${id}`,
    method: "GET",
  });
}

const useGetFormDetails = (id: string) => {
  const isTemplate = useSearchParams().get("formType") === "template";
  return useQuery({
    queryKey: QueryKeys.FORM_DETAILS(id),
    queryFn: () => getFormDetails(id),
    enabled: !isTemplate,
  });
};

async function formsPublic(id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/public/${id}`,
    method: "GET",
  });
}

const useFormsPublic = (id: string) => {
  return useQuery({
    queryKey: QueryKeys.FORM_PUBLIC(id),
    queryFn: () => formsPublic(id),
  });
};

async function cloneForm(formId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/clone/${formId}`,
    method: "GET",
  });
}

const useCloneForm = (params: { limit: number; offset: number }) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: cloneForm,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.USER_FORMS(params.limit, params.offset),
      });
    },
  });
};

async function deleteForm(id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${id}`,
    method: "DELETE",
  });
}

const useDeleteForm = (params: { limit: number; offset: number }) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteForm,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.USER_FORMS(params.limit, params.offset),
      });
    },
  });
};

async function getThankYouPage(id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/thankyoupage/${id}`,
    method: "GET",
  });
}

const useGetThankYouPage = (id: string) => {
  return useQuery({
    queryKey: QueryKeys.FORM_DETAILS(id), // Using existing FORM_DETAILS key since THANK_YOU_PAGE doesn't exist
    queryFn: () => getThankYouPage(id),
  });
};

async function searchForms(query: string, params: { limit: number; offset: number }) {
  return makeRequest({
    endpoint: `${baseEndpoint}/search?limit=${params.limit}&offset=${params.offset}&q=${query}`,
    method: "GET",
  });
}

const useSearchForms = (query: string, params: { limit: number; offset: number }, options?: any) => {
  return useQuery({
    queryKey: QueryKeys.SEARCH_FORMS(query, params.limit, params.offset), 
    queryFn: () => searchForms(query, params),
    ...options,
  });
};

async function restoreForm(form_id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/restore/${form_id}`,
    method: "POST",
  });
}

const useRestoreForm = () => {
  return useMutation({
    mutationFn: restoreForm,
  });
};

async function getTrashedForms() {
  return makeRequest({
    endpoint: `${baseEndpoint}/trashforms`,
    method: "POST",
  });
}

const useGetTrashedForms = () => {
  return useQuery({
    queryKey: ["trashedForms"],
    queryFn: getTrashedForms,
  });
};

async function moveFormToTrash(form_id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/trash/${form_id}`,
    method: "DELETE",
  });
}

const useMoveFormToTrash = () => {
  return useMutation({
    mutationFn: moveFormToTrash,
  });
};

export {
  useGetFormElements,
  useCreateForm,
  useUpdateForm,
  useGetUserForms,
  usePublishForm,
  useGetFormDetails,
  useFormsPublic,
  useCloneForm,
  useDeleteForm,
  useGetThankYouPage,
  useSearchForms,
  useRestoreForm,
  useGetTrashedForms,
  useMoveFormToTrash
};
