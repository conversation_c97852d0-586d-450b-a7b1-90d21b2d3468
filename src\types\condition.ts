export enum ConditionOperator {
    IS_EQUAL_TO = "is equal to",
    IS_NOT_EQUAL_TO = "is not equal to",
    CONTAINS = "contains",
    DOES_NOT_CONTAIN = "does not contain",
    IS_EMPTY = "is empty",
    IS_NOT_EMPTY = "is not empty",
    STARTS_WITH = "starts with",
    ENDS_WITH = "ends with"
  }

export const conditionOperators = [
    ConditionOperator.IS_EQUAL_TO,
    ConditionOperator.IS_NOT_EQUAL_TO,
    ConditionOperator.CONTAINS,
    ConditionOperator.DOES_NOT_CONTAIN,
    ConditionOperator.STARTS_WITH,
    ConditionOperator.ENDS_WITH,
    ConditionOperator.IS_EMPTY,
    ConditionOperator.IS_NOT_EMPTY,
  ];

  export enum ThenAction {
    HIDE_FIELD = "hide_field",
    SHOW_FIELD = "show_field",

  }

  export const thenActions = [
    ThenAction.HIDE_FIELD,
    ThenAction.SHOW_FIELD,
  ];

 

  export const evaluateCondition = (operator: ConditionOperator, value1: string, value2: string): boolean => {
    // Handle checkbox values - they come as comma-separated strings
    const value1Array = value1.split(',').map(v => v.trim()).filter(Boolean);
    const value2Array = value2.split(',').map(v => v.trim()).filter(Boolean);

    switch (operator) {
      case ConditionOperator.IS_EQUAL_TO:
        // For checkboxes, check if all selected values match
        if (value1Array.length > 1 || value2Array.length > 1) {
          return value1Array.length === value2Array.length && 
                 value1Array.every(v => value2Array.includes(v));
        }
        return value1 === value2;
      case ConditionOperator.IS_NOT_EQUAL_TO:
        // For checkboxes, check if any value is different
        if (value1Array.length > 1 || value2Array.length > 1) {
          return value1Array.length !== value2Array.length || 
                 value1Array.some(v => !value2Array.includes(v));
        }
        return value1 !== value2;
      case ConditionOperator.CONTAINS:
        // For checkboxes, check if all selected values are included
        if (value1Array.length > 1 || value2Array.length > 1) {
          return value1Array.every(v => value2Array.includes(v));
        }
        return value2.toLowerCase().includes(value1.toLowerCase());
      case ConditionOperator.DOES_NOT_CONTAIN:
        // For checkboxes, check if any selected value is not included
        if (value1Array.length > 1 || value2Array.length > 1) {
          return value1Array.some(v => !value2Array.includes(v));
        }
        return !value2.toLowerCase().includes(value1.toLowerCase());
      case ConditionOperator.IS_EMPTY:
        return !value1 || value1.trim() === '';
      case ConditionOperator.IS_NOT_EMPTY:
        return !!value1 && value1.trim() !== '';
      case ConditionOperator.STARTS_WITH:
        if (value1Array.length > 1 || value2Array.length > 1) {
          return value1Array.some(v => value2Array.some(v2 => v2.startsWith(v)));
        }
        return value1.toLowerCase().startsWith(value2.toLowerCase());
      case ConditionOperator.ENDS_WITH:
        if (value1Array.length > 1 || value2Array.length > 1) {
          return value1Array.some(v => value2Array.some(v2 => v2.endsWith(v)));
        }
        return value1.toLowerCase().endsWith(value2.toLowerCase());
      default:
        return false;
    }
  };
