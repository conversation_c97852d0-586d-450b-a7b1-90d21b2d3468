"use client";
import React from "react";
import { Input } from "../ui/input";
import { LayoutGrid, Brush, SearchIcon, X } from "lucide-react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button } from "../ui/button";
import { useAppStore } from "@/state-store/app-state-store";
import { useSearchParams } from "next/navigation";
import { generateUUID } from "@/lib/gernerateuid";
import fieldsData from "@/fields/fieldsData";
import DesignSection from "./design-section";

const ToolContainer = () => {
  const [activeTab, setActiveTab] = React.useState("field");
  const [searchQuery, setSearchQuery] = React.useState("");
  const { addField } = useAppStore();

  const searchParams = useSearchParams();
  const mode = searchParams.get("mode");

  // Filter fields based on search query
  const filteredFieldsData = React.useMemo(() => {
    if (!searchQuery.trim()) return fieldsData;

    const query = searchQuery.toLowerCase().trim();
    return fieldsData.map(section => ({
      ...section,
      items: section.items.filter(field => 
        field.fieldName.toLowerCase().includes(query)
      )
    })).filter(section => section.items.length > 0);
  }, [searchQuery]);

  if (mode === "preview") return null;

  const onFieldClick = (field: any) => {
    const fieldId = generateUUID();
    addField({ ...field, id: fieldId });
  };

  // Handle clearing search query
  const handleClearSearch = () => {
    setSearchQuery("");
  };

  return (
    <div className="flex-1 overflow-hidden min-w-[350px] w-full text-[#1F311C]">
      <div className="h-full flex flex-row items-start gap-0.5 mt-[1px]  w-full">
        {/* Sidebar Navigation */}
        <div className="bg-app-background max-w-[75px] w-full flex flex-col items-start justify-start h-full">
          <div className="flex flex-col items-center gap-2">
            <Button
              onClick={() => setActiveTab("field")}
              className={`group flex flex-col items-center w-full gap-2 h-16 max-w-[75px] rounded-none text-app-text-color bg-app-background  border-l-4   ${
                activeTab === "field"
                  ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active font-semibold"
                  : "border-l-app-background hover:bg-app-sidebar-hover font-medium"
              }`}
            >
              <LayoutGrid className="!w-6 !h-6" />
              <span className="text-xs  ">Fields</span>
            </Button>
            <Button
              onClick={() => setActiveTab("design")}
              className={`group flex flex-col items-center w-full max-w-[75px] gap-2 h-16 rounded-none text-app-text-color bg-app-background border-l-4  ${
                activeTab === "design"
                  ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active font-semibold"
                  : "border-l-app-background hover:bg-app-sidebar-hover font-medium"
              }`}
            >
              <Brush className="!w-6 !h-6  " />
              <span className="text-xs">Design</span>
            </Button>
          </div>
        </div>

        {/* Content Area */}
        <div className="h-full bg-app-background w-full  p-4  ">
          {/* Fields Tab Content */}
          {activeTab === "field" && (
            <div className="h-full  ">
              <div className="relative  ">
                <SearchIcon className="absolute p-[3px] bottom-2 left-2 text-app-text-secondary" />
                <Input
                  placeholder="Search Fields..."
                  className="mb-4 pl-8 pr-10 bg-app-hero-background placeholder:text-app-text-secondary text-app-text-color"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-2 bottom-2 p-1 hover:bg-app-background rounded-full transition-colors"
                    type="button"
                  >
                    <X className="h-4 w-4 text-app-text-secondary hover:text-app-text-color" />
                  </button>
                )}
              </div>
              <div className="overflow-y-scroll scroller h-full pb-28">
                {filteredFieldsData.map((section) => (
                  <div key={section.title} className="mb-4 ">
                    <h3 className="text-sm font-semibold mb-2 text-app-text-color">
                      {section.title}
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      {section.items.map((field) => (
                        <div
                          key={field.name}
                          className="flex flex-col items-center justify-center w-full min-h-20 border border-transparent hover:border-emerald-300 hover:shadow-md rounded-lg bg-app-hero-background cursor-pointer relative font-medium hover:font-semibold text-app-text-color group transition-colors"
                          onClick={() => onFieldClick(field)}
                        >
                          <FontAwesomeIcon
                            icon={field.icon}
                            className="text-xl mt-3 mb-2 h-5 transition-all duration-200 ease-in-out delay-50 group-hover:h-6 group-hover:scale-110  group-hover:animate-out"
                          />
                          <span className="text-xs transition-all duration-300 ease-in-out delay-50  group-hover:scale-110  group-hover:animate-out">
                            {field.fieldName}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
                {filteredFieldsData.length === 0 && (
                  <div className="text-center text-app-text-secondary py-8">
                    No fields found matching "{searchQuery}"
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Design Tab Content */}
          {activeTab === "design" && <DesignSection />}
        </div>
      </div>
    </div>
  );
};

export default ToolContainer;
