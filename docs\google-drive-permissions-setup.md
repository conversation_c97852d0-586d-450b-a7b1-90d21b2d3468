# Google Drive API Permissions Setup Guide

## समस्या: Google Drive Restricted Access

जब आप Google Picker use करते हैं और "restricted access" error आता है, तो इसका मतलब है कि आपके Google Cloud project में proper permissions और scopes configure नहीं हैं।

## Solution Steps

### 1. Google Cloud Console Configuration

#### A. APIs Enable करें:
```
1. Google Cloud Console में जाएं
2. APIs & Services > Library
3. निम्नलिखित APIs enable करें:
   - Google Drive API
   - Google Picker API  
   - Google Sheets API
   - Google Apps Script API (optional)
```

#### B. OAuth 2.0 Credentials Setup:
```
1. APIs & Services > Credentials
2. OAuth 2.0 Client IDs में:
   - Authorized JavaScript origins:
     * http://localhost:3000
     * https://automateforms.ai
     * https://your-domain.com
   
   - Authorized redirect URIs:
     * http://localhost:3000/auth/callback
     * https://automateforms.ai/auth/callback
```

#### C. OAuth Consent Screen:
```
1. OAuth consent screen configure करें
2. <PERSON><PERSON><PERSON> add करें:
   - https://www.googleapis.com/auth/drive.readonly
   - https://www.googleapis.com/auth/drive.file
   - https://www.googleapis.com/auth/spreadsheets
   - https://www.googleapis.com/auth/drive.metadata.readonly
```

### 2. Backend API Configuration

आपके backend में `/v1/google/sheet/picker/config` API में ये scopes include करें:

```javascript
const scopes = [
  'https://www.googleapis.com/auth/drive.readonly',
  'https://www.googleapis.com/auth/drive.file',
  'https://www.googleapis.com/auth/spreadsheets'
];
```

### 3. Frontend Configuration

#### A. Environment Variables (.env):
```env
NEXT_PUBLIC_GOOGLE_API_KEY=your_api_key
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_client_id
```

#### B. Picker Configuration में proper scopes:
```javascript
// Backend से token generate करते समय ये scopes use करें
const REQUIRED_SCOPES = [
  'https://www.googleapis.com/auth/drive.readonly',
  'https://www.googleapis.com/auth/drive.file',
  'https://www.googleapis.com/auth/spreadsheets'
];
```

### 4. Common Issues और Solutions

#### Issue 1: "Access blocked: This app's request is invalid"
**Solution:**
- OAuth consent screen properly configure करें
- App verification complete करें (production के लिए)
- Test users add करें (development के लिए)

#### Issue 2: "insufficient_scope" error
**Solution:**
- Backend में proper scopes add करें
- User को re-authenticate करवाएं
- Token refresh करें

#### Issue 3: "origin_mismatch" error
**Solution:**
- Google Cloud Console में authorized origins check करें
- Exact domain match ensure करें (including protocol)

### 5. Testing Steps

1. **Development Mode में:**
   ```
   - Debug panel check करें
   - Browser console में errors देखें
   - Network tab में API calls monitor करें
   ```

2. **Production Mode में:**
   ```
   - OAuth consent screen verification complete करें
   - All domains properly configured हों
   - SSL certificate valid हो
   ```

### 6. Backend API Response Format

आपका backend API इस format में response भेजे:

```json
{
  "data": {
    "data": {
      "apiKey": "your_api_key",
      "clientId": "your_client_id", 
      "token": "valid_oauth_token_with_drive_scopes"
    }
  }
}
```

### 7. Troubleshooting Commands

#### Browser Console में check करें:
```javascript
// Check if APIs are loaded
console.log('GAPI loaded:', !!window.gapi);
console.log('Picker loaded:', !!window.google?.picker);

// Check token validity
console.log('Token:', yourToken);
```

#### Network Tab में check करें:
```
- picker/config API call successful हो रही है
- Token valid है और proper scopes के साथ generate हुई है
- CORS errors नहीं आ रही हैं
```

### 8. Final Checklist

- [ ] Google Drive API enabled
- [ ] Google Picker API enabled  
- [ ] Google Sheets API enabled
- [ ] OAuth credentials configured
- [ ] Authorized origins added
- [ ] OAuth consent screen configured
- [ ] Proper scopes added
- [ ] Backend API returning valid token
- [ ] Frontend properly handling permissions

## Support

अगर अभी भी issues आ रही हैं तो:
1. Browser console errors share करें
2. Network tab में API responses check करें  
3. Google Cloud Console configuration verify करें
4. Backend logs check करें for token generation

यह setup complete होने के बाद Google Picker properly काम करेगा और users अपनी Google Drive files access कर सकेंगे।
