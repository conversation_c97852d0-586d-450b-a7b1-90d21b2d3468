"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Features from "./Features";
import Testimonials from "./Testimonials";
import WhyChoose from "./WhyChoose";
import FAQ from "./FAQ";
import Pricing from "./Pricing";
import PrebuiltTemplates from "./PrebuiltTemplates";
import Navbar from "./Navbar";
import Hero from "./Hero";
import FooterCTA from "./FooterCTA";

export default function LandingPage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // useEffect(() => {
  //   // Check if user is authenticated using the checkAndRefreshTokenIfNeeded function
  //   const checkAuth = async () => {
  //     try {
  //       const isAuthenticated = await import('@/api-services/utils').then(
  //         module => module.checkAndRefreshTokenIfNeeded()
  //       );
  //       setIsAuthenticated(isAuthenticated);
  //     } catch (error) {
  //       console.error('Error checking authentication:', error);
  //       setIsAuthenticated(false);
  //     }
  //   };

  //   checkAuth();
  // }, []);

  const handleButtonClick = () => {
    if (isAuthenticated) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Bar */}
      <Navbar />

      {/* Hero Section */}
      <Hero
        isAuthenticated={isAuthenticated}
        handleButtonClick={handleButtonClick}
      />

      {/* Features Section */}
      <Features />

      {/* Testimonials Section */}
      <Testimonials />

      {/* Why Choose Section */}
      <WhyChoose />

      {/* Pricing Section */}
      <Pricing />

      {/* Pre-built Templates Section */}
      <PrebuiltTemplates isAuthenticated={isAuthenticated} />

      {/* FAQ Section */}
      <FAQ />

      {/* Footer CTA */}
      <FooterCTA
        isAuthenticated={isAuthenticated}
        handleButtonClick={handleButtonClick}
      />
    </div>
  );
}