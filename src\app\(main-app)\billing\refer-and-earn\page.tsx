"use client";
import React from "react";
import {
  Gift,
  Video,
  Star,
  Users,
  Wallet,
  UserPlus,
  Info,
  Link as LinkIcon,
} from "lucide-react";
import clsx from "clsx";
import { useGetReferralDetails } from "@/api-services/referral";
import { toast } from "react-hot-toast";
import Image from "next/image";
import Link from "next/link";

const ReferAndEarnPage = () => {
  const { data: referralData, isLoading, isError } = useGetReferralDetails();
  const referralCode = referralData?.data?.referral_code;
  const referralLink = referralCode
    ? `https://automateforms.ai/signup?ref=${referralCode}`
    : "";

  // Use actual data from API
  const totalReferrals = referralData?.data?.referral_count || 0;
  const successfulReferrals = 20; // This should come from API when available
  const totalEarnings = 500; // This should come from API when available
  const canAddToWallet = totalEarnings >= 2500;

  const socialMediaLinks = [
    {
      href: `https://wa.me/?text=${encodeURIComponent(referralLink)}`,
      alt: "Whatsapp icon",
      src: "/WhatsApp.png",
      ariaLabel: "Share on WhatsApp",
    },
    {
      href: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Facebook icon",
      src: "/Facebook.png",
      ariaLabel: "Share on Facebook",
    },
    {
      href: `https://twitter.com/intent/tweet?url=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Twitter X icon",
      src: "/X.png",
      ariaLabel: "Share on Twitter",
    },
    {
      href: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Linkedin icon",
      src: "/LinkedIn.png",
      ariaLabel: "Share on LinkedIn",
    },
    {
      href: `mailto:?subject=Join me on Automate Forms&body=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Gmail icon",
      src: "/Gmail.png",
      ariaLabel: "Share via Email",
    },
  ];

  const handleCopyLink = () => {
    if (referralLink) {
      navigator.clipboard.writeText(referralLink);
      toast.success("Referral link copied to clipboard!");
    }
  };

  if (isError) {
    return (
      <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
        <div className="text-red-500 text-center">
          Failed to load referral data. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-3xl font-semibold text-left text-app-text-color">
          Refer and Earn
        </h2>
      </div>

      {/* Offer Cards */}
      <div className="bg-app-background rounded-xl p-6 shadow border">
        <h3 className="text-xl font-semibold mb-1 text-app-text-color">
          Earn exciting offers
        </h3>
        <p className="text-app-text-secondary mb-4">
          Complete the following tasks and win free submissions
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-4 bg-app-main-background rounded-lg p-4 border">
            <Video className="w-10 h-10 text-green-600 bg-green-100 rounded-full p-2" />
            <div>
              <div className="font-semibold text-base text-app-text-color">
                25 extra submissions<span className="text-xs">/Month</span>
              </div>
              <div className="text-xs text-app-text-secondary">
                Make a video about us
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4 bg-app-main-background rounded-lg p-4 border">
            <Star className="w-10 h-10 text-blue-600 bg-blue-100 rounded-full p-2" />
            <div>
              <div className="font-semibold text-base text-app-text-color">
                50 extra submissions<span className="text-xs">/Month</span>
              </div>
              <div className="text-xs text-app-text-secondary">
                Review us on a website
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4 bg-app-main-background rounded-lg p-4 border">
            <Users className="w-10 h-10 text-yellow-600 bg-yellow-100 rounded-full p-2" />
            <div>
              <div className="font-semibold text-base text-app-text-color">
                75 extra submissions<span className="text-xs">/Month</span>
              </div>
              <div className="text-xs text-app-text-secondary">
                Invite 5 friends to Automate forms
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Share the link */}
      <div className="bg-app-background rounded-xl p-6 shadow border flex flex-col gap-2">
        <div className="font-semibold mb-2">
          Share the link with your friend and earn rewards
        </div>
        <div className="flex items-center gap-2 bg-app-main-background border border-dashed border-gray-300 rounded p-2">
          <LinkIcon className="w-5 h-5 text-app-text-secondary" />
          <span className="text-xs font-mono break-all text-app-text-color">
            {isLoading ? "Loading..." : referralLink}
          </span>
          <button
            className="ml-auto px-3 py-1 rounded bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color font-medium text-xs transition disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleCopyLink}
            disabled={isLoading || !referralLink}
          >
            Copy
          </button>
        </div>

        <p className="text-sm mt-4 text-app-text-color">
          Share your referral link on social media and through email
        </p>
        <div className="flex flex-row items-center justify-start gap-4 flex-wrap mt-2">
          {socialMediaLinks.map(({ href, alt, src, ariaLabel }, index) => (
            <Link
              key={index}
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={ariaLabel}
              className="hover:opacity-80 transition-opacity"
            >
              <Image
                src={src}
                height={500}
                width={500}
                quality={100}
                alt={alt}
                className="h-8 w-8"
              />
            </Link>
          ))}
        </div>
      </div>

      {/* Referral Status */}
      <div className="bg-app-background rounded-xl p-6 shadow border">
        <h3 className="text-lg font-semibold mb-4 text-app-text-color">
          Your referral status
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex flex-col items-center bg-app-main-background rounded-lg p-4 border">
            <Users className="w-8 h-8 text-blue-600 mb-2" />
            <div className="text-2xl font-bold text-app-text-color">
              {totalReferrals}
            </div>
            <div className="text-xs text-app-text-secondary">
              Total referrals
            </div>
          </div>
          {/* <div className="flex flex-col items-center bg-gray-50 rounded-lg p-4 border">
            <UserPlus className="w-8 h-8 text-green-600 mb-2" />
            <div className="text-2xl font-bold">{successfulReferrals}</div>
            <div className="text-xs text-gray-500">Successful referral</div>
          </div>
          <div className="flex flex-col items-center bg-gray-50 rounded-lg p-4 border relative">
            <Wallet className="w-8 h-8 text-yellow-600 mb-2" />
            <div className="text-2xl font-bold">₹ {totalEarnings}</div>
            <div className="text-xs text-gray-500">Total Earnings</div>
            <button
              className={clsx(
                "mt-2 px-3 py-1 rounded border font-medium text-sm transition",
                canAddToWallet
                  ? "border-primary-500 text-primary-600 hover:bg-primary-50"
                  : "border-gray-300 text-gray-400 cursor-not-allowed"
              )}
              disabled={!canAddToWallet}
            >
              Add to wallet
            </button>
            {!canAddToWallet && (
              <div className="absolute top-2 right-2 flex items-center gap-1 text-xs text-gray-400">
                <Info className="w-4 h-4" />
                You need atleast ₹ 2500 to add in your wallet
              </div>
            )}
          </div> */}
        </div>
      </div>

      {/* Invite and How referral works */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-app-background rounded-xl p-6 shadow border flex flex-col gap-2">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-6 h-6 text-blue-600" />
            <span className="font-semibold text-app-text-color">
              Invite a friend to earn upto
            </span>
          </div>
          <div className="text-lg font-bold text-app-text-color">
            ₹ 500 reward
          </div>
          <div className="text-sm text-app-text-secondary">
            when your referee purchases a premium plan
          </div>
        </div>
        <div className="bg-app-background rounded-xl p-6 shadow border flex flex-col gap-2">
          <div className="font-semibold mb-2 text-app-text-color">
            How referral works
          </div>
          <ul className="list-disc pl-5 text-sm text-app-text-secondary space-y-1">
            <li>Share your referral link with your friends.</li>
            <li>Referee purchases to premium.</li>
            <li>
              Get a{" "}
              <span className="font-semibold text-app-text-color">₹ 500</span>{" "}
              reward when your referee purchases a premium plan
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ReferAndEarnPage;
