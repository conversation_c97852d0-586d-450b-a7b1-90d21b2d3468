"use client";
import React from "react";
import { But<PERSON> } from "../ui/button";
import WalletBalanceCard from "./WalletBalanceCard";

const WalletSection = () => {
  return (
    <div className="flex flex-row gap-4 max-w-sm">
      {/* Wallet Balance Card */}
      <WalletBalanceCard />

      {/* Contact Sales Team Card */}
      {/* <div className="bg-app-background text-app-text-color rounded-lg p-4 flex flex-col justify-between w-full gap-2">
        <div className="font-semibold">Contact Sales Team</div>
        <div className="flex flex-row text-sm text-gray-700 mt-auto">
          <span className="text-app-text-secondary text-sm">
            Get personalized assistance for your business needs
          </span>
          <Button className="px-4 py-2 hover:text-white hover:bg-[#1F311C] bg-white border border-[#1F311C] text-[#1F311C] rounded-xl">
            Connect
          </Button>
        </div>
      </div> */}
    </div>
  );
};

export default WalletSection;
