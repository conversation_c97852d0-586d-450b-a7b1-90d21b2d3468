import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useState, useRef, useEffect } from "react";
import { useCreateFormWithAI } from "@/api-services/create_form_with_ai";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import {
  Loader2,
  CheckCircle2,
  Mic,
  Stars,
  ChevronRight,
  HelpCircle,
  MessageSquare,
  Briefcase,
  UserPlus,
  X,
  Star,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useCreateFormWithAIVoice } from "@/api-services/create_form_with_ai";

interface AiFormDialogProps {
  onClose: () => void;
}

interface GeneratedForm {
  id: string;
  title: string;
  description: string;
  type: string;
  heading: string;
  automate_form_fields: {
    fields: Array<{
      id: string;
      type: string;
      title: string;
      description: string;
      isRequired: boolean;
      options?: string[];
    }>;
  };
}

const PREBUILT_OPTIONS = [
  {
    id: "customer-support",
    title: "Customer Support",
    description: "Collect customer support requests",
    icon: HelpCircle,
    prompt:
      "Create a comprehensive customer support form with fields for: 1) Contact information (name, email, phone), 2) Issue type dropdown (billing, technical, account, etc.), 3) Priority level (high, medium, low), 4) Detailed description text area, 5) File upload for screenshots, and 6) Preferred contact method.",
  },
  {
    id: "feedback",
    title: "Feedback",
    description: "Collect user feedback",
    icon: MessageSquare,
    prompt:
      "Create a detailed feedback form with: 1) Rating scale (1-5 stars), 2) Likert scale questions about different aspects of the product/service, 3) Open-ended comments section, 4) Contact information (optional), and 5) Would-you-recommend question.",
  },
  {
    id: "job-application",
    title: "Job Application",
    description: "Create job applications",
    icon: Briefcase,
    prompt:
      "Create a professional job application form with: 1) Personal information section, 2) Work history with dates and descriptions, 3) Education background, 4) Skills checklist, 5) Resume/CV upload, 6) Cover letter text area, 7) References, and 8) Equal opportunity employment questions.",
  },
  {
    id: "lead-generation",
    title: "Lead Generation",
    description: "Capture potential leads",
    icon: UserPlus,
    prompt:
      "Create an effective lead generation form with: 1) Contact details (name, email, phone, company), 2) Dropdown for product/service interest, 3) Budget range selector, 4) Timeline for purchase, 5) How they heard about us, and 6) Follow-up preferences.",
  },
];

const fieldTypeIcons = {
  text: "T",
  number: "123",
  email: "@",
  textarea: "¶",
  date: "📅",
  time: "🕒",
  file: "📎",
  dropdown: "▼",
  select: "▼",
  radio: "○",
  checkbox: "☑",
  rating: "⭐",
  voice: "🎤",
  phone: "📞",
  website: "🌐",
};

export default function AiFormDialog({ onClose }: AiFormDialogProps) {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedForm, setGeneratedForm] = useState<GeneratedForm | null>(
    null
  );
  const [progress, setProgress] = useState(0);
  const router = useRouter();
  const { setFormTitle, setFormDescription, theme } = useAppStore();
  const { mutate: createFormWithAI } = useCreateFormWithAI();
  const generateButtonRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const { mutate: createFormWithAIVoice } = useCreateFormWithAIVoice();
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [isVoiceGenerating, setIsVoiceGenerating] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordingTimer, setRecordingTimer] = useState<NodeJS.Timeout | null>(null);

  const folderId = useSearchParams().get("folderId");

  // Helper function to format recording time
  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  // Cleanup recording timer on unmount
  useEffect(() => {
    return () => {
      if (recordingTimer) {
        clearInterval(recordingTimer);
      }
    };
  }, [recordingTimer]);

  const handleGenerate = () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setProgress(0);

    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 90) {
          clearInterval(interval);
          return prev;
        }
        return prev + 10;
      });
    }, 300);

    const payload:{
      prompt: string;
      folder_id?: string;
    } = {
      prompt,
    }

    if (folderId) {
      payload.folder_id = folderId;
    }

    createFormWithAI(
      payload,
      {
        onSuccess: (response) => {
          clearInterval(interval);
          setProgress(100);
          setTimeout(() => {
            const form = response.data.createdForm;
            setGeneratedForm(form);
            setIsGenerating(false);
          }, 500);
        },
        onError: () => {
          clearInterval(interval);
          setIsGenerating(false);
        },
      }
    );
  };

  const handleContinue = () => {
    if (!generatedForm) return;

    setFormTitle(generatedForm?.title || "Untitled Form");
    setFormDescription(
      generatedForm?.description || "Add your form description here"
    );
    router.push(
      `/playground?formId=${generatedForm?.id}&formType=${generatedForm?.type}`
    );
    onClose();
  };

  const handlePrebuiltOptionClick = (optionPrompt: string) => {
    setPrompt(optionPrompt);
    setTimeout(() => {
      generateButtonRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }, 100);
  };

  const renderPreview = () => {
    if (!generatedForm) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="w-full flex justify-center scroller"
      >
        <div
          className={cn(
            "bg-gradient-to-br from-app-hero-background to-app-hero-background/90 rounded-xl p-4 sm:p-6 border border-gray-200 w-full",
            "mx-auto flex flex-col items-center overflow-y-auto shadow-sm scroller-style",
            isMobile ? "max-h-[50vh]" : "max-h-[70vh] max-w-lg"
          )}
        >
          <div className="mb-6 w-full">
            <h2 className="text-lg sm:text-xl font-medium text-app-text-color mb-1 text-left w-full">
              {generatedForm?.heading}
            </h2>
            <p className="text-xs sm:text-sm text-app-text-secondary text-left mb-4 w-full">
              {generatedForm?.description}
            </p>
          </div>
          <div className="w-full flex flex-col gap-4">
            {generatedForm?.automate_form_fields?.fields?.map((field) => (
              <motion.div
                key={field?.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-app-background border border-gray-200 rounded-lg p-4 sm:p-5 shadow-xs flex flex-col gap-2 transition-all hover:shadow-sm"
              >
                <div className="flex items-center gap-2 mb-1">
                  <div className="flex items-center justify-center w-5 h-5 rounded-full bg-emerald-100 text-emerald-600 text-xs font-medium">
                    {fieldTypeIcons[
                      field?.type as keyof typeof fieldTypeIcons
                    ] || "?"}
                  </div>
                  <label className="font-medium text-app-text-color text-sm sm:text-base leading-snug">
                    {field?.title}
                    {field?.isRequired && (
                      <span className="ml-1 text-xs text-red-500 font-medium align-top">
                        *
                      </span>
                    )}
                  </label>
                </div>
                {field?.description && (
                  <p className="text-xs sm:text-sm text-gray-500 mb-2 text-left leading-normal">
                    {field?.description}
                  </p>
                )}
                <div>
                  {[
                    "text",
                    "number",
                    "email",
                    "date",
                    "time",
                    "phone",
                    "website",
                  ].includes(field.type) && (
                    <input
                      type={
                        field?.type === "number"
                          ? "number"
                          : field?.type === "email"
                          ? "email"
                          : field?.type === "date"
                          ? "date"
                          : field?.type === "time"
                          ? "time"
                          : field?.type === "phone"
                          ? "tel"
                          : field?.type === "website"
                          ? "url"
                          : "text"
                      }
                      disabled
                      className="w-full p-2 sm:p-3 border border-gray-200 rounded-lg bg-app-hero-background focus:outline-none text-sm sm:text-base focus:ring-2 focus:ring-emerald-500/30 focus:border-emerald-500 transition-all"
                      placeholder={
                        field?.type === "email"
                          ? "<EMAIL>"
                          : field?.type === "phone"
                          ? "Enter phone number"
                          : field?.type === "website"
                          ? "https://example.com"
                          : field?.type === "number"
                          ? "Enter a number"
                          : "Text input"
                      }
                    />
                  )}
                  {field?.type === "textarea" && (
                    <textarea
                      disabled
                      className="w-full p-2 sm:p-3 border border-gray-200 rounded-lg bg-app-hero-background focus:outline-none text-sm sm:text-base focus:ring-2 focus:ring-emerald-500/30 focus:border-emerald-500 transition-all"
                      placeholder="Long text input"
                      rows={3}
                    />
                  )}
                  {field?.type === "file" && (
                    <div className="flex items-center gap-2 sm:gap-3">
                      <input
                        type="file"
                        disabled
                        className="w-full p-2 sm:p-3 border border-gray-200 rounded-lg bg-app-hero-background focus:outline-none text-xs sm:text-base file:mr-2 sm:file:mr-4 file:py-1 sm:file:py-2 file:px-2 sm:file:px-4 file:rounded-lg file:border-0 file:text-xs sm:file:text-sm file:font-semibold file:bg-emerald-500/10 file:text-emerald-600 transition-all"
                      />
                    </div>
                  )}
                  {(field?.type === "dropdown" || field?.type === "select") && (
                    <select
                      disabled
                      className="w-full p-2 sm:p-3 border border-gray-200 rounded-lg bg-app-hero-background text-app-text-secondary focus:outline-none text-sm sm:text-base focus:ring-2 focus:ring-emerald-500/30 focus:border-emerald-500 transition-all appearance-none bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiAjdjY3YWI4IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9Imx1Y2lkZSBsdWNpZGUtY2hldnJvbi1kb3duIj48cGF0aCBkPSJtNiA5IDYgNiA2LTYiLz48L3N2Zz4=')] bg-no-repeat bg-[center_right_0.75rem] sm:bg-[center_right_1rem]"
                    >
                      <option value="">Select an option</option>
                      {(field.options &&
                      Array.isArray(field.options) &&
                      field.options.length > 0
                        ? field.options
                        : ["Option 1", "Option 2"]
                      ).map((option, idx) => (
                        <option key={option || idx} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  )}
                  {field?.type === "radio" && (
                    <div className="flex flex-wrap gap-2 sm:gap-4 mt-1 sm:mt-2">
                      {(field?.options &&
                      Array.isArray(field?.options) &&
                      field?.options?.length > 0
                        ? field.options
                        : ["Option 1", "Option 2"]
                      ).map((option, idx) => (
                        <label
                          key={option || idx}
                          className="flex items-center gap-1 sm:gap-2 cursor-not-allowed text-sm sm:text-base"
                        >
                          <input
                            type="radio"
                            disabled
                            className="h-3 w-3 sm:h-4 sm:w-4 border-gray-300 text-emerald-500 focus:ring-emerald-500"
                            name={field.id}
                          />
                          <span className="text-gray-700">{option}</span>
                        </label>
                      ))}
                    </div>
                  )}
                  {field?.type === "checkbox" && (
                    <div className="flex flex-wrap gap-2 sm:gap-4 mt-1 sm:mt-2">
                      {(field?.options &&
                      Array.isArray(field?.options) &&
                      field?.options?.length > 0
                        ? field?.options
                        : ["Option 1", "Option 2"]
                      ).map((option, idx) => (
                        <label
                          key={option || idx}
                          className="flex items-center gap-1 sm:gap-2 cursor-not-allowed text-sm sm:text-base"
                        >
                          <input
                            type="checkbox"
                            disabled
                            className="h-3 w-3 sm:h-4 sm:w-4 border-gray-300 text-emerald-500 focus:ring-emerald-500 rounded"
                            name={field.id}
                          />
                          <span className="text-gray-700">{option}</span>
                        </label>
                      ))}
                    </div>
                  )}
                  {field?.type === "rating" && (
                    <div className="flex gap-1 mt-1 sm:mt-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star className="w-5 h-5 sm:w-6 sm:h-6 text-amber-400" />
                      ))}
                    </div>
                  )}
                  {(field?.type === "voice" || field?.type === "audio") && (
                    <button
                      type="button"
                      disabled
                      className="flex items-center gap-1 sm:gap-2 px-3 py-1.5 sm:px-4 sm:py-2 border border-gray-200 rounded-lg bg-gray-50 text-sm sm:text-base text-gray-700 cursor-not-allowed mt-1 sm:mt-2 w-full justify-center hover:bg-gray-100 transition-colors"
                    >
                      <Mic className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-500" />
                      <span>Leave a voice message</span>
                    </button>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    );
  };

  // Voice recording logic
  const startRecording = async () => {
    setVoiceError(null);
    setRecordingTime(0);
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);
      const audioChunks: Blob[] = [];
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };
      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        handleVoiceFormGeneration(audioBlob);
        stream.getTracks().forEach(track => track.stop());
        // Clear timer
        if (recordingTimer) {
          clearInterval(recordingTimer);
          setRecordingTimer(null);
        }
        setRecordingTime(0);
      };
      recorder.start();
      setIsRecording(true);
      
      // Start timer
      const timer = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          if (newTime >= 60) { // 1 minute = 60 seconds
            // Auto stop recording
            if (recorder.state === 'recording') {
              recorder.stop();
            }
            clearInterval(timer);
            setRecordingTimer(null);
            return 60;
          }
          return newTime;
        });
      }, 1000);
      setRecordingTimer(timer);
    } catch (err: any) {
      setVoiceError("Microphone access denied or not available.");
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
      setIsRecording(false);
      // Clear timer
      if (recordingTimer) {
        clearInterval(recordingTimer);
        setRecordingTimer(null);
      }
      setRecordingTime(0);
    }
  };

  const handleVoiceFormGeneration = (audioBlob: Blob) => {
    setIsVoiceGenerating(true);
    setProgress(0);
    const formData = new FormData();
    formData.append('audio', audioBlob, 'voice-input.webm');
    if (folderId) {
      formData.append('folder_id', folderId);
    }
    createFormWithAIVoice(formData, {
      onSuccess: (response: any) => {
        setProgress(100);
        setTimeout(() => {
          const form = response.data.createdForm;
          setGeneratedForm(form);
          setIsVoiceGenerating(false);
        }, 500);
      },
      onError: () => {
        setIsVoiceGenerating(false);
        setVoiceError("Failed to create form from voice input.");
      },
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      {/* Modal Content */}
      <div
        ref={modalRef}
        className={cn(
          "relative text-[#1F311C] bg-app-hero-background shadow-lg rounded-lg overflow-y-auto max-h-screen ",
          isMobile
            ? "w-full h-[95vh] max-w-[98vw] p-4"
            : "max-w-5xl p-6 h-[90%] max-h-[90vh]"
        )}
        style={{ zIndex: 60 }}
      >
        {/* Close (X) Button */}
        <button
          onClick={onClose}
          aria-label="Close dialog"
          className="absolute top-4 right-4 z-10 text-gray-400 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 rounded-full p-1 transition-colors"
        >
          <X className="w-6 h-6" />
        </button>
        <div
          className={cn(
            "flex w-full h-full overflow-y-auto max-h-screen scroller-style",
            isMobile ? "flex-col gap-4" : "flex-row"
          )}
        >
          {/* Left Section */}
          <motion.div
            initial={{ opacity: 0, x: isMobile ? 0 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            className={cn(
              "flex flex-col bg-gradient-to-b from-app-hero-background to-app-hero-background/20",
              isMobile ? "w-full p-4" : "w-2/5 border-r border-gray-100 p-6"
            )}
          >
            <div
              className={cn(
                "font-bold text-gray-900 flex items-center gap-2 mb-2",
                isMobile ? "text-xl" : "text-2xl"
              )}
            >
              <span className="bg-gradient-to-r from-emerald-500 to-emerald-600 bg-clip-text text-transparent">
                AI Form Generator
              </span>
              <motion.div
                animate={{ rotate: 20, scale: [1, 1.1, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
              >
                <Stars className="w-4 h-4 sm:w-5 sm:h-5 text-amber-400" />
              </motion.div>
            </div>

            <AnimatePresence mode="wait">
              {!generatedForm ? (
                <motion.div
                  key="input-form"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex-1 flex flex-col mt-4 sm:mt-6"
                >
                  <div className="flex-1">
                    <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">
                      Describe your form requirements or select a pre-built
                      template below.
                    </p>

                    {/* Prebuilt Options Grid */}
                    <div
                      className={cn(
                        "grid gap-2 sm:gap-3 mb-4 sm:mb-6",
                        isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}
                    >
                      {PREBUILT_OPTIONS.map((option) => (
                        <motion.button
                          key={option.id}
                          whileHover={{ y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() =>
                            handlePrebuiltOptionClick(option.prompt)
                          }
                          className={`p-2 sm:p-3 rounded-lg border transition-all flex flex-col items-center text-center hover:shadow-sm ${
                            prompt === option.prompt
                              ? `border-emerald-500 ${
                                  theme === "dark"
                                    ? "bg-app-background/10"
                                    : "bg-emerald-50"
                                } `
                              : "border-gray-200 bg-app-background hover:border-emerald-300"
                          }`}
                        >
                          <option.icon className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-500 mb-1 sm:mb-2" />
                          <span className="font-medium text-xs sm:text-sm text-app-text-color">
                            {option.title}
                          </span>
                          <span className="text-xs text-app-text-secondary">
                            {option.description}
                          </span>
                        </motion.button>
                      ))}
                    </div>

                    <div className="relative">
                      <textarea
                        className="w-full h-32 sm:h-40 border border-gray-200 rounded-lg p-3 sm:p-4 text-sm text-app-text-color placeholder-gray-400 focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500 transition-all bg-app-background shadow-xs scroller-style"
                        placeholder="Or describe your custom form here..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        disabled={isRecording || isVoiceGenerating}
                      />
                      <button
                        type="button"
                        onClick={isRecording ? stopRecording : startRecording}
                        className={cn(
                          "absolute top-2 right-2 p-2 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500/30",
                          isRecording
                            ? "bg-red-100 text-red-600 animate-pulse"
                            : "bg-emerald-100 text-emerald-600 hover:bg-emerald-200"
                        )}
                        title={isRecording ? "Stop recording" : "Start voice input"}
                        disabled={isGenerating || isVoiceGenerating}
                      >
                        {isRecording ? (
                          <div className="flex items-center gap-1">
                            <Mic className="w-4 h-4" />
                            <span className="text-xs font-medium">
                              {formatRecordingTime(recordingTime)}
                            </span>
                          </div>
                        ) : (
                          <Mic className="w-4 h-4" />
                        )}
                      </button>
                      <div className="absolute bottom-2 left-2 text-xs text-app-text-secondary">
                        {prompt.length}/500
                      </div>
                    </div>
                    {isRecording && (
                      <div className="mt-2 p-2 bg-emerald-50 border border-emerald-200 rounded-lg flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        <span className="text-xs text-emerald-700 font-medium">
                          Recording... {formatRecordingTime(recordingTime)} / 01:00
                        </span>
                        <span className="text-xs text-emerald-600">
                          ({60 - recordingTime}s remaining)
                        </span>
                      </div>
                    )}
                    {isVoiceGenerating && (
                      <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg flex items-center gap-2">
                        <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                        <span className="text-xs text-blue-700 font-medium">Processing your voice input and generating form...</span>
                      </div>
                    )}
                    {voiceError && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-xs text-red-700 font-medium">{voiceError}</span>
                      </div>
                    )}
                    <div className="mt-2 sm:mt-4 text-xs text-app-text-secondary">
                      <p>Examples:</p>
                      <ul className="list-disc list-inside space-y-1 mt-1 pl-2">
                        <li className="truncate">
                          "Event registration form with name, email, and ticket
                          type"
                        </li>
                        <li className="truncate">
                          "Job application form with resume upload"
                        </li>
                        <li className="truncate">
                          "Product survey with rating scale"
                        </li>
                      </ul>
                    </div>
                  </div>
                  <motion.div
                    ref={generateButtonRef}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      id="generate-button"
                      className={cn(
                        "mt-4 sm:mt-6 w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all",
                        isMobile ? "py-2 text-sm" : "py-3"
                      )}
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          Generate Form
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </>
                      )}
                    </Button>
                  </motion.div>
                </motion.div>
              ) : (
                <motion.div
                  key="generated-form"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex-1 flex flex-col mt-4 sm:mt-6"
                >
                  <div className="flex-1">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="flex items-center gap-2 sm:gap-3 text-emerald-600 mb-4 sm:mb-6 p-2 sm:p-3 bg-emerald-50 rounded-lg border border-emerald-100 text-xs sm:text-sm"
                    >
                      <CheckCircle2 className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
                      <span className="font-medium">
                        Your form has been successfully generated! Review the
                        preview and continue to edit.
                      </span>
                    </motion.div>

                    <div className="space-y-3 sm:space-y-4">
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        <h3 className="text-xs font-semibold text-app-text-color uppercase tracking-wider mb-1">
                          Form Title
                        </h3>
                        <p className="text-app-text-secondary font-medium text-sm sm:text-base">
                          {generatedForm.title || "Untitled Form"}
                        </p>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <h3 className="text-xs font-semibold text-app-text-color uppercase tracking-wider mb-1">
                          Description
                        </h3>
                        <p className="text-app-text-secondary text-xs sm:text-sm">
                          {generatedForm?.description ||
                            "No description provided"}
                        </p>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        <h3 className="text-xs font-semibold text-app-text-color uppercase tracking-wider mb-1">
                          Fields Generated
                        </h3>
                        <div className="flex flex-wrap gap-1 sm:gap-2">
                          {generatedForm?.automate_form_fields?.fields
                            ?.slice(0, 5)
                            ?.map((field) => (
                              <motion.span
                                key={field.id}
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="text-xs px-2 py-1 bg-app-background text-app-text-color rounded-full"
                              >
                                {field.type}
                              </motion.span>
                            ))}
                          {generatedForm.automate_form_fields.fields.length >
                            5 && (
                            <motion.span
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="text-xs px-2 py-1 bg-app-background text-app-text-color rounded-full"
                            >
                              +
                              {generatedForm?.automate_form_fields?.fields
                                ?.length - 5}{" "}
                              more
                            </motion.span>
                          )}
                        </div>
                      </motion.div>
                    </div>
                  </div>

                  <div className="space-y-2 sm:space-y-3">
                    <motion.div
                      whileHover={{ scale: 1.01 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg",
                          isMobile ? "py-2 text-sm" : "py-2.5"
                        )}
                        onClick={() => {
                          setGeneratedForm(null);
                          setPrompt("");
                        }}
                      >
                        Generate Another Form
                      </Button>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Right Section */}
          <div
            className={cn(
              "flex flex-col items-center justify-center text-center bg-app-hero-background/30 overflow-y-auto ",
              isMobile ? "w-full h-[50vh] p-4 max-h-[50vh]" : "w-3/5 p-6"
            )}
          >
            <AnimatePresence mode="wait">
              {isGenerating ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col items-center justify-center h-full"
                >
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      rotate: {
                        repeat: Infinity,
                        duration: 2,
                        ease: "linear",
                      },
                      scale: {
                        repeat: Infinity,
                        duration: 1.5,
                        repeatType: "reverse",
                      },
                    }}
                    className="relative mb-6 sm:mb-8"
                  >
                    <Image
                      src="/gemini-color.svg"
                      width={500}
                      height={500}
                      quality={100}
                      alt="AI Generating Form"
                      className="w-16 h-16 sm:w-20 sm:h-20"
                    />
                  </motion.div>
                  <motion.h3
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="text-base sm:text-lg font-medium text-app-text-color mb-1 sm:mb-2"
                  >
                    Crafting your form...
                  </motion.h3>
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-app-text-secondary max-w-md mb-4 sm:mb-6 text-sm sm:text-base"
                  >
                    Our AI is analyzing your requirements and generating a
                    tailored form with all the necessary fields.
                  </motion.p>
                  <motion.div
                    initial={{ opacity: 0, scaleX: 0 }}
                    animate={{ opacity: 1, scaleX: 1 }}
                    transition={{ delay: 0.4 }}
                    className="w-full max-w-xs bg-gray-200 rounded-full h-2 overflow-hidden"
                  >
                    <div
                      className="bg-gradient-to-r from-emerald-400 to-emerald-500 h-2 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    className="mt-2 sm:mt-4 text-xs text-app-text-secondary"
                  >
                    {progress < 50 && "Analyzing your requirements..."}
                    {progress >= 50 &&
                      progress < 80 &&
                      "Designing form structure..."}
                    {progress >= 80 &&
                      progress < 100 &&
                      "Finalizing form fields..."}
                    {progress === 100 && "Almost done!"}
                  </motion.div>
                </motion.div>
              ) : generatedForm ? (
                <div className="w-full h-full overflow-y-auto">
                  {renderPreview()}
                </div>
              ) : (
                <motion.div
                  key="empty-state"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col items-center justify-center h-full text-center p-4 sm:p-6 max-w-md mx-auto"
                >
                  <motion.div
                    animate={{
                      y: [0, -5, 0],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "easeInOut",
                    }}
                    className="relative mb-6 sm:mb-8"
                  >
                    <Image
                      src="/gemini-color.svg"
                      width={500}
                      height={500}
                      quality={100}
                      alt="AI Icon"
                      className="w-20 h-20 sm:w-24 sm:h-24 mx-auto"
                    />
                    <motion.div
                      animate={{
                        rotate: [0, 10, -10, 0],
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        repeatType: "reverse",
                        ease: "easeInOut",
                      }}
                      className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 bg-white p-1.5 sm:p-2 rounded-full shadow-sm border border-gray-100"
                    >
                      <Stars className="w-4 h-4 sm:w-6 sm:h-6 text-amber-400" />
                    </motion.div>
                  </motion.div>
                  <motion.h3
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className={cn(
                      "font-bold text-app-text-color mb-2 sm:mb-3",
                      isMobile ? "text-lg" : "text-xl"
                    )}
                  >
                    AI-Powered Form Creation
                  </motion.h3>
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-app-text-secondary mb-4 sm:mb-6 text-sm sm:text-base"
                  >
                    Select a template or describe your form to get started.
                    Preview will appear here.
                  </motion.p>
                  <div
                    className={cn(
                      "grid gap-2 sm:gap-3 w-full",
                      isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}
                  >
                    {PREBUILT_OPTIONS.map((option, index) => (
                      <motion.button
                        key={option.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + index * 0.1 }}
                        whileHover={{ y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handlePrebuiltOptionClick(option.prompt)}
                        className="p-2 sm:p-3 bg-app-main-background rounded-lg border border-gray-200 hover:border-emerald-300 transition-colors flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
                      >
                        <option.icon className="w-3 h-3 sm:w-4 sm:h-4 text-emerald-500" />
                        <span className="font-medium text-app-text-color">
                          {option.title}
                        </span>
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            {generatedForm && (
              <Button
                className="w-full mt-6 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all"
                onClick={handleContinue}
              >
                Continue to Editor
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
