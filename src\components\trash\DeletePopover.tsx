import React, { useState } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Trash, RotateCcw, EllipsisVertical } from "lucide-react";

interface DeletePopoverProps {
  onDelete: () => void;
  onRestore: () => void;
}

const DeletePopover: React.FC<DeletePopoverProps> = ({ onDelete, onRestore }) => {
  const [popoverOpen, setPopoverOpen] = useState(false);

  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild>
        <div className="p-2 hover:bg-app-hero-background text-app-text-secondary rounded-full cursor-pointer">
          <EllipsisVertical />
        </div>
      </PopoverTrigger>
      <PopoverContent side="left" className="w-40 p-2">
        <div className="w-full flex flex-col gap-2 text-app-text-color">
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
            onClick={() => {
              onRestore();
              setPopoverOpen(false);
            }}
          >
            <RotateCcw className="w-4 h-4" />
            <span>Restore</span>
          </div>
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2 text-red-500"
            onClick={() => {
              onDelete();
              setPopoverOpen(false);
            }}
          >
            <Trash className="w-4 h-4" />
            <span>Delete</span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DeletePopover; 