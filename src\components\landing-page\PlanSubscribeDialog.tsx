import React, { useEffect } from "react";
import { X } from "lucide-react";
import { useCreateSubscriptionOrder } from "@/api-services/wallet";
import { useUserProfile } from "@/api-services/auth";
import { toast } from "react-hot-toast";

interface PlanSubscribeDialogProps {
  open: boolean;
  onClose: () => void;
  planName: string;
  planPrice: number;
  walletBalance: number;
  amountToRecharge: number;
  gst: number;
  total: number;
  onSubscribe: () => void;
  moduleId: number;
  numUsers: number;
  planDuration: string;
}

const PlanSubscribeDialog: React.FC<PlanSubscribeDialogProps> = ({
  open,
  onClose,
  planName,
  planPrice,
  walletBalance,
  amountToRecharge,
  gst,
  total,
  onSubscribe,
  moduleId,
  numUsers,
  planDuration,
}) => {
  const { data: userData } = useUserProfile();
  const user = userData?.data?.user;
  const { mutate: createSubscriptionOrder, isPending } =
    useCreateSubscriptionOrder();

  useEffect(() => {
    // Load Razorpay script only if we might need it (insufficient balance)
    if (amountToRecharge > 0) {
      const script = document.createElement("script");
      script.src = "https://checkout.razorpay.com/v1/checkout.js";
      script.async = true;
      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
      };
    }
  }, [amountToRecharge]);

  const handleRazorpayPayment = (orderData: any) => {
    const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;

    if (!razorpayKey) {
      toast.error("Razorpay configuration is missing");
      return;
    }

    if (!(window as any).Razorpay) {
      toast.error("Razorpay script not loaded");
      return;
    }

    const options = {
      key: razorpayKey,
      amount: amountToRecharge * 100, // Only charge the remaining amount
      currency: "INR",
      name: "Automate Business",
      description: `Additional payment for ${planName} Plan`,
      order_id: orderData.order_id,
      handler: async (response: {
        razorpay_payment_id: string;
        razorpay_order_id: string;
        razorpay_signature: string;
      }) => {
        try {
          toast.success("Payment successful!");
          onClose();
          onSubscribe();
        } catch (error) {
          console.error("Payment verification failed:", error);
          toast.error("Payment verification failed");
        }
      },
      prefill: {
        name: `${user?.first_name} ${user?.last_name}`,
        email: user?.email,
        contact: user?.phone,
      },
      theme: {
        color: "#1F311C",
      },
    };

    try {
      const razorpay = new (window as any).Razorpay(options);
      razorpay.open();
    } catch (error) {
      console.error("Razorpay initialization error:", error);
      toast.error("Failed to initialize payment gateway");
    }
  };

  const handleSubscribe = () => {
    if (!user) {
      toast.error("User information not found");
      return;
    }

    if (!user.phone) {
      toast.error("Phone number is required for subscription");
      return;
    }

    // Check for insufficient wallet balance
    if (walletBalance < planPrice) {
      toast.error("Insufficient wallet balance. Please recharge your wallet.");
      onClose();
      return;
    }

    const orderData = {
      module_id: moduleId,
      num_users: numUsers,
      plan_duration: planDuration,
      currency: "INR",
    };

    console.log("Creating subscription with data:", orderData);

    createSubscriptionOrder(
      {
        workspaceId: user.workspace_id,
        orderData,
      },
      {
        onSuccess: (response) => {
          if (response.success && response.data) {
            if (amountToRecharge > 0) {
              // Only proceed with Razorpay if there's insufficient balance
              toast.success("Order created successfully");
              handleRazorpayPayment(response.data);
            } else {
              // If sufficient balance, just complete the subscription
              toast.success("Subscription successful!");
              onClose();
              onSubscribe();
            }
          } else {
            // Handle insufficient wallet balance error
            if (
              response.error ===
              "Insufficient wallet balance. Please recharge your wallet."
            ) {
              toast.error(response.error);
              onClose();
            } else {
              toast.error(
                response.error || "Failed to create subscription order"
              );
            }
          }
        },
        onError: (error: any) => {
          console.error("Subscription error details:", error);
          const errorMessage =
            error?.response?.data?.error ||
            error?.message ||
            "Failed to create subscription order";

          // Handle insufficient wallet balance error
          if (
            errorMessage ===
            "Insufficient wallet balance. Please recharge your wallet."
          ) {
            toast.error(errorMessage);
            onClose();
          } else {
            toast.error(errorMessage);
          }
        },
      }
    );
  };

  if (!open) return null;
  const isSufficient = walletBalance >= planPrice;
  // Calculate total without GST
  const displayTotal = isSufficient
    ? planPrice
    : walletBalance + amountToRecharge;
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
      onClick={onClose}
    >
      <div
        className="bg-app-background rounded-xl shadow-xl p-6 w-full max-w-md relative"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          className="absolute top-3 right-3 text-app-text-secondary hover:text-app-text-color"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="w-5 h-5" />
        </button>
        {/* Dialog Content */}
        <div className="text-lg font-bold mb-1 text-app-text-color text-start">
          Upgrade plan
        </div>
        <div className="text-base font-semibold text-app-text-color text-start">
          Automate form builder
        </div>
        <div className="text-xs text-app-text-secondary text-start mb-4">
          Build stunning forms with Automate form builder
        </div>
        <div className="space-y-3 mb-4">
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Plan name:</span>
            <span className="font-medium text-app-text-color">{planName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Plan price:</span>
            <span className="font-medium text-app-text-color">
              ₹ {planPrice.toLocaleString()}
              <span className="text-xs font-normal">/{planDuration === "yearly" ? "Year" : "Month"}</span>
            </span>
          </div>
          {!isSufficient && (
            <>
              <div className="flex justify-between items-center">
                <span className="text-app-text-secondary">Wallet balance:</span>
                <span className="font-medium text-app-text-color">
                  ₹ {walletBalance.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-app-text-secondary">
                  Amount to Recharge:
                </span>
                <span className="font-medium text-app-text-color">
                  ₹ {amountToRecharge.toLocaleString()}
                  <span className="text-xs font-normal">+GST</span>
                </span>
              </div>
            </>
          )}
          <div className="flex justify-between items-center border-t pt-2">
            <span className="text-app-text-color font-semibold">Total:</span>
            <span className="font-bold text-app-text-color">
              ₹ {displayTotal.toLocaleString()}
            </span>
          </div>
        </div>
        <div className="flex justify-center">
          <button
            className="px-6 py-2 border border-[#1F311C] text-[#1F311C] bg-white rounded-lg font-medium hover:bg-[#1F311C] hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleSubscribe}
            disabled={isPending}
          >
            {isPending
              ? "Processing..."
              : isSufficient
              ? "Subscribe Now"
              : "Pay Now"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlanSubscribeDialog;
