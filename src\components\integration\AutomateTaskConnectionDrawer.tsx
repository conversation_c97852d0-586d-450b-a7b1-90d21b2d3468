import React, { useState, useEffect } from "react";
import { X, Loader2, Workflow, Network } from "lucide-react";
import {
  useAddCRMConnection,
  useGetIntegrationActions,
  useGetConnections,
  useGetCRMConnectionKey,
  useLinkTaskForm,
  useUpdateTaskForm,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import { fetchTaskUsers, fetchTaskCategories } from "@/api-services/crm-api";
import { useSearchParams } from "next/navigation";
import { useGetFormFields } from "@/api-services/form_fields";
import { toast } from "react-hot-toast";
import SelectInputCombo from "@/components/common/SelectInputCombo";

interface Action {
  id: string;
  name: string;
  description: string;
  created_at: string;
}

interface Connection {
  id: string;
  name: string;
}

interface TaskFormData {
  form_id: string;
  integration_id: string;
  credential_id: string;
  action_id: string;
  category_id: string;
  assignedTo: string;
  assignedby: string;
  column_mapped_data: {
    id: string;
    name: string;
    title: string;
  }[];
  title: string;
  description: string;
  target_date: string;
  priority: string;
}

interface AutomateTaskConnectionDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: {
    formIntegatedId: string;
    credentialId: string;
    credentialName: string;
    enabled: boolean;
    connectedAt: string;
    metadata: {
      category_id: string;
      assignedTo: string;
      assignedby: string;
    };
    actionId: string;
    mappedData: {
      id: string;
      name: string;
      title: string;
      key: string;
    }[];
  }[];
  onRefresh?: () => void;
}

// Helper to build options for SelectInputCombo, handling NAME_INPUT and ADDRESS specially
const buildFieldOptions = (fields: any[]) => {
  const options: {
    id: string;
    name: string;
    title: string;
    index: number;
  }[] = [];
  let logicalIndex = 1;
  fields.forEach((f) => {
    if (f.component === "NAME_INPUT") {
      options.push({
        id: f.id,
        name: "firstName",
        title: f.firstNameTitle || "First Name",
        index: logicalIndex,
      });
      options.push({
        id: f.id,
        name: "lastName",
        title: f.lastNameTitle || "Last Name",
        index: logicalIndex,
      });
      logicalIndex++;
    } else if (f.component === "ADDRESS") {
      options.push({
        id: f.id,
        name: "address",
        title: "Address",
        index: logicalIndex,
      });
      const allowed = f.allowedAddressFields || {
        country: true,
        city: true,
        pincode: true,
        state: true,
      };
      if (allowed.city) {
        options.push({
          id: f.id,
          name: "city",
          title: "City",
          index: logicalIndex,
        });
      }
      if (allowed.state) {
        options.push({
          id: f.id,
          name: "state",
          title: "State",
          index: logicalIndex,
        });
      }
      if (allowed.country) {
        options.push({
          id: f.id,
          name: "country",
          title: "Country",
          index: logicalIndex,
        });
      }
      if (allowed.pincode) {
        options.push({
          id: f.id,
          name: "pincode",
          title: "Pincode",
          index: logicalIndex,
        });
      }
      logicalIndex++;
    } else {
      options.push({
        id: f.id,
        name: f.name,
        title: f.title || f.name,
        index: logicalIndex,
      });
      logicalIndex++;
    }
  });
  return options;
};

// Converts backend key to template (for prefill in SelectInputCombo)
const convertMappedToTemplate = (key: string, options: any[]): string => {
  if (!key) return "";
  // Handles keys like {{fieldId.name.firstName}}, {{fieldId.address.city}}, {{fieldId}}, etc.
  // 1. Handle keys with dot notation
  let result = key.replace(
    /\{\{([^\.\}]+)(?:\.[^\.\}]+)*\.([^\.\}]+)\}\}/g,
    (match, fieldId, fieldName) => {
      const opt = options.find(
        (opt) => opt.id === fieldId && opt.name === fieldName
      );
      if (!opt) return match;
      return `{{${opt.index}.${fieldName}}}`;
    }
  );
  // 2. Handle keys with only fieldId (no dot)
  result = result.replace(/\{\{([a-f0-9-]+)\}\}/gi, (match, fieldId) => {
    const opt = options.find((opt) => opt.id === fieldId);
    if (!opt) return match;
    return `{{${opt.index}.${opt.name}}}`;
  });
  return result;
};

// Converts template to backend key (for saving to API)
const convertTemplateToMapped = (
  template: string,
  options: any[],
  fields: any[]
): string => {
  // Replace all {{index.Name}} with correct backend key
  return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
    // Find the option with matching index and name
    const opt = options.find(
      (opt) => String(opt.index) === idx && opt.name === name
    );
    if (!opt) return match;
    // For NAME_INPUT, use the new format {{fieldId.name.firstName}} or {{fieldId.name.lastName}}
    const field = fields.find((f) => f.id === opt.id);
    if (field && field.component === "NAME_INPUT") {
      return `{{${opt.id}.name.${opt.name}}}`;
    }
    // For ADDRESS, use {{fieldId.address.address}}, etc.
    if (field && field.component === "ADDRESS") {
      return `{{${opt.id}.address.${opt.name}}}`;
    }
    // For other fields, just use {{fieldId}}
    return `{{${opt.id}}}`;
  });
};

// Utility to check if a template references only valid fields
function isTemplateValid(template: string, fields: any[]): boolean {
  // Extract all field IDs from the template
  const regex = /\{\{([a-f0-9-]+)(?:\.[^\}]+)?\}\}/gi;
  let match;
  while ((match = regex.exec(template)) !== null) {
    const fieldId = match[1];
    if (!fields.some((f) => f.id === fieldId)) {
      return false; // At least one referenced field is missing
    }
  }
  return true;
}

export default function AutomateTaskConnectionDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: AutomateTaskConnectionDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [formData, setFormData] = useState<
    TaskFormData & {
      titleTemplate?: string;
      descriptionTemplate?: string;
      targetDateTemplate?: string;
    }
  >({
    form_id: "",
    integration_id: "",
    credential_id: "",
    action_id: "",
    category_id: "",
    assignedTo: "",
    assignedby: "",
    column_mapped_data: [],
    title: "",
    description: "",
    target_date: "",
    priority: "",
    titleTemplate: "",
    descriptionTemplate: "",
    targetDateTemplate: "",
  });
  const [users, setUsers] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");

  const { data: formFields } = useGetFormFields(formId!);

  const addCRMConnectionMutation = useAddCRMConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { data: connectionKeyData } =
    useGetCRMConnectionKey(selectedConnection);
  const { mutate: linkTaskForm, isPending: isLinking } = useLinkTaskForm();
  const { mutate: updateTaskForm, isPending: isUpdating } = useUpdateTaskForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;

  const { mutate: disconnectIntegration, isPending: isDisconnecting } =
    useDisconnectIntegration();

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
    }
  }, [initialActionId]);

  useEffect(() => {
    const fetchTaskData = async () => {
      if (connectionKeyData?.data?.key) {
        const apiKey = connectionKeyData.data.key;
        try {
          const [usersResponse, categoriesResponse] = await Promise.all([
            fetchTaskUsers(apiKey),
            fetchTaskCategories(apiKey),
          ]);
          console.log(usersResponse);
          setUsers(usersResponse?.data?.data || []);
          console.log(users);
          setCategories(categoriesResponse?.data?.data || []);
        } catch (error) {
          console.error("Error fetching task data:", error);
        }
      }
    };

    if (selectedConnection) {
      fetchTaskData();
    }
  }, [selectedConnection, connectionKeyData]);

  useEffect(() => {
    if (saveSuccess && connectionName && connections.length > 0) {
      const newConnection = connections.find(
        (conn: Connection) => conn.name === connectionName
      );
      if (newConnection) {
        setConnectionType("existing");
        setSelectedConnection(newConnection.id);
      }
    }
  }, [connections, connectionName, saveSuccess]);

  useEffect(() => {
    if (existingConnections.length > 0 && formFields?.data?.fields) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      setSelectedConnection(connection.credentialId);
      setConnectionName(connection.credentialName);
      setSelectedAction(connection.actionId);

      // Set form data from metadata
      setFormData((prev) => ({
        ...prev,
        category_id: connection.metadata.category_id,
        assignedTo: connection.metadata.assignedTo,
        assignedby: connection.metadata.assignedby,
      }));

      // Get the field options for conversion
      const fields = formFields.data.fields;
      const options = buildFieldOptions(fields);
      const dateOptions = buildFieldOptions(
        fields.filter((f: any) => f.component === "DATE")
      );

      // Set mapped fields from mappedData
      connection.mappedData.forEach((mappedField) => {
        switch (mappedField.name) {
          case "title": {
            const template = convertMappedToTemplate(mappedField.key, options);
            setFormData((prev) => ({
              ...prev,
              titleTemplate: isTemplateValid(mappedField.key, fields)
                ? template
                : "",
            }));
            break;
          }
          case "description": {
            const template = convertMappedToTemplate(mappedField.key, options);
            setFormData((prev) => ({
              ...prev,
              descriptionTemplate: isTemplateValid(mappedField.key, fields)
                ? template
                : "",
            }));
            break;
          }
          case "target_date": {
            const template = convertMappedToTemplate(mappedField.key, options);
            setFormData((prev) => ({
              ...prev,
              targetDateTemplate: isTemplateValid(mappedField.key, fields)
                ? template
                : "",
            }));
            break;
          }
          case "priority":
            setFormData((prev) => ({ ...prev, priority: mappedField.title }));
            break;
        }
      });
    }
  }, [existingConnections, formFields?.data?.fields]);

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: () => {
          toast.success("Task integration disconnected successfully!");
          onRefresh?.();
          onClose();
        },
        onError: (error: Error) => {
          console.error("Error disconnecting:", error);
          toast.error(
            "Failed to disconnect task integration. Please try again."
          );
        },
      }
    );
  };

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
  };

  const handleFormDataChange = async (
    field: keyof typeof formData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddConnection = async () => {
    if (!connectionName || !apiKey) return;

    setIsAddingConnection(true);
    try {
      const response = await addCRMConnectionMutation.mutateAsync({
        integrationId,
        name: connectionName,
        key: apiKey,
      });

      if (response?.success) {
        setSaveSuccess(true);
        setSuccessMessage(
          `Connection "${connectionName}" added successfully! You can now configure the task settings.`
        );
        await refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleSave = async () => {
    if (connectionType === "existing") {
      setIsSaving(true);
      try {
        // Create column mapped data array
        const columnMappedData: any[] = [];
        const fields = formFields?.data?.fields || [];
        const options = buildFieldOptions(fields);
        const dateOptions = buildFieldOptions(
          fields.filter((f: any) => f.component === "DATE")
        );

        // For each template field, push mappedData with key
        if (formData.titleTemplate) {
          columnMappedData.push({
            id: "title",
            name: "title",
            title: "Title",
            key: convertTemplateToMapped(
              formData.titleTemplate,
              options,
              fields
            ),
          });
        }
        if (formData.descriptionTemplate) {
          columnMappedData.push({
            id: "description",
            name: "description",
            title: "Description",
            key: convertTemplateToMapped(
              formData.descriptionTemplate,
              options,
              fields
            ),
          });
        }
        if (formData.targetDateTemplate) {
          const allOptions = buildFieldOptions(fields);
          columnMappedData.push({
            id: "target_date",
            name: "target_date",
            title: "Target Date",
            key: convertTemplateToMapped(
              formData.targetDateTemplate,
              allOptions,
              fields
            ),
          });
        }
        if (formData.priority) {
          columnMappedData.push({
            id: "priority",
            name: "priority",
            title: formData.priority,
            key: formData.priority,
          });
        }

        if (existingConnections.length > 0) {
          // Update existing connection
          const updateData = {
            form_integration_id: existingConnections[0].formIntegatedId,
            action_id: selectedAction,
            category_id: formData.category_id,
            assignedTo: formData.assignedTo,
            assignedby: formData.assignedby,
            column_mapped_data: columnMappedData,
          };

          updateTaskForm(updateData, {
            onSuccess: () => {
              toast.success("Task integration updated successfully!");
              onRefresh?.();
              onClose();
            },
            onError: (error: Error) => {
              console.error("Error updating task integration:", error);
              toast.error(
                "Failed to update task integration. Please try again."
              );
            },
          });
        } else {
          // Create new connection
          const linkFormData = {
            form_id: formId || "",
            integration_id: integrationId,
            credential_id: selectedConnection,
            action_id: selectedAction,
            category_id: formData.category_id,
            assignedTo: formData.assignedTo,
            assignedby: formData.assignedby,
            column_mapped_data: columnMappedData,
          };

          linkTaskForm(linkFormData, {
            onSuccess: (response: any) => {
              if (response?.success) {
                setSaveSuccess(true);
                setSuccessMessage("Task integration linked successfully!");
                onRefresh?.();
                onClose();
              }
            },
            onError: (error: Error) => {
              console.error("Error linking task form:", error);
              toast.error("Failed to link task integration. Please try again.");
            },
          });
        }
      } catch (error) {
        console.error("Error preparing form data:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSaving(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div className="fixed inset-0 bg-black/50 z-[199] transition-opacity" />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect Automate Task
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">Automate Task</h3>

              {saveSuccess && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center justify-between">
                  <p className="text-sm text-green-800">{successMessage}</p>
                  <button
                    onClick={() => setSaveSuccess(false)}
                    className="text-green-600 hover:text-green-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action) => (
                      <option key={action.id} value={action.id}>
                        {action.name.charAt(0).toUpperCase() +
                          action.name.slice(1)}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>

                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>

                    {connectionType === "new" ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Connection Name
                          </label>
                          <input
                            type="text"
                            value={connectionName}
                            onChange={(e) => setConnectionName(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter connection name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            API Key
                          </label>
                          <input
                            type="text"
                            value={apiKey}
                            onChange={(e) => setApiKey(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your API key"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter the API Key here. To obtain the API Key, log
                            in to your Automate Task account.
                          </p>
                        </div>
                        <button
                          onClick={handleAddConnection}
                          disabled={
                            !connectionName || !apiKey || isAddingConnection
                          }
                          className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                            !connectionName || !apiKey || isAddingConnection
                              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                              : "bg-app-text-color text-app-background hover:bg-opacity-90"
                          }`}
                        >
                          {isAddingConnection ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <Workflow className="h-4 w-4" />
                              Add New Connection
                            </>
                          )}
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Select Connection
                          </label>
                          <select
                            value={selectedConnection}
                            onChange={handleConnectionSelect}
                            disabled={!hasValidConnections}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                          >
                            <option value="">Select a connection</option>
                            {connections.map((connection) => (
                              <option key={connection.id} value={connection.id}>
                                {connection.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {selectedConnection && (
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Task Category{" "}
                                <span className="text-red-500">*</span>
                              </label>
                              <select
                                value={formData.category_id}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "category_id",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select category</option>
                                {categories.map((category) => (
                                  <option key={category.id} value={category.id}>
                                    {category.name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Assign To{" "}
                                <span className="text-red-500">*</span>
                              </label>
                              <select
                                value={formData.assignedTo}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "assignedTo",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select user</option>
                                {users.map((user) => (
                                  <option key={user.id} value={user.id}>
                                    {user.full_name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Assigned By{" "}
                                <span className="text-red-500">*</span>
                              </label>
                              <select
                                value={formData.assignedby}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "assignedby",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select user</option>
                                {users.map((user) => (
                                  <option key={user.id} value={user.id}>
                                    {user.full_name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {formFields?.data?.fields && (
                              <>
                                <div>
                                  <label className="block text-sm font-medium mb-1">
                                    Title{" "}
                                    <span className="text-red-500">*</span>
                                  </label>
                                  <SelectInputCombo
                                    options={buildFieldOptions(
                                      formFields?.data?.fields || []
                                    )}
                                    value={convertMappedToTemplate(
                                      formData.titleTemplate || "",
                                      buildFieldOptions(
                                        formFields?.data?.fields || []
                                      )
                                    )}
                                    onChange={(val) =>
                                      handleFormDataChange("titleTemplate", val)
                                    }
                                    placeholder="Type or insert fields for title"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm font-medium mb-1">
                                    Description
                                  </label>
                                  <SelectInputCombo
                                    options={buildFieldOptions(
                                      formFields?.data?.fields || []
                                    )}
                                    value={convertMappedToTemplate(
                                      formData.descriptionTemplate || "",
                                      buildFieldOptions(
                                        formFields?.data?.fields || []
                                      )
                                    )}
                                    onChange={(val) =>
                                      handleFormDataChange(
                                        "descriptionTemplate",
                                        val
                                      )
                                    }
                                    placeholder="Type or insert fields for description"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm font-medium mb-1">
                                    Target Date{" "}
                                    <span className="text-red-500">*</span>
                                  </label>
                                  <SelectInputCombo
                                    options={buildFieldOptions(
                                      formFields?.data?.fields || []
                                    ).filter((opt) => {
                                      const field = (
                                        formFields?.data?.fields || []
                                      ).find((f: any) => f.id === opt.id);
                                      return (
                                        field && field.component === "DATE"
                                      );
                                    })}
                                    value={convertMappedToTemplate(
                                      formData.targetDateTemplate || "",
                                      buildFieldOptions(
                                        formFields?.data?.fields || []
                                      )
                                    )}
                                    onChange={(val) =>
                                      handleFormDataChange(
                                        "targetDateTemplate",
                                        val
                                      )
                                    }
                                    placeholder="Type or insert fields for target date"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm font-medium mb-1">
                                    Priority
                                  </label>
                                  <select
                                    value={formData.priority}
                                    onChange={(e) =>
                                      handleFormDataChange(
                                        "priority",
                                        e.target.value
                                      )
                                    }
                                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                  >
                                    <option value="">Select priority</option>
                                    <option value="High">High</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Low">Low</option>
                                  </select>
                                </div>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {existingConnections.length > 0 ? (
                  <>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        isDisconnecting
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-red-500 text-white hover:bg-red-600"
                      }`}
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        "Disconnect"
                      )}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedConnection ||
                        !formData.category_id ||
                        !formData.assignedTo ||
                        !formData.assignedby ||
                        !formData.targetDateTemplate ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedConnection ||
                        !formData.category_id ||
                        !formData.assignedTo ||
                        !formData.assignedby ||
                        !formData.targetDateTemplate ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Update"
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleSave}
                    disabled={
                      connectionType === "existing"
                        ? !selectedConnection ||
                          !formData.category_id ||
                          !formData.assignedTo ||
                          !formData.assignedby ||
                          !formData.targetDateTemplate ||
                          isSaving
                        : false
                    }
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      connectionType === "existing" &&
                      (!selectedConnection ||
                        !formData.category_id ||
                        !formData.assignedTo ||
                        !formData.assignedby ||
                        !formData.targetDateTemplate ||
                        isSaving)
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
