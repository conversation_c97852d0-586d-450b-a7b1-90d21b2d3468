import { useSidebar } from "@/components/ui/sidebar";
import { ArrowLeft, Gift, ReceiptIndianRupee, Wallet } from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

const useBillingSidebar = () => {
  const { state, toggleSidebar } = useSidebar();

  const pathname = usePathname();

  const sidebarLinks = useMemo(
    () => [
      { label: "Billing Plan", href: "/home", icon: ArrowLeft },
      {
        label: "Billing",
        href: "/billing",
        icon: ReceiptIndianRupee,
      },
      { label: "Wallet Logs", href: "/billing/wallet", icon: Wallet },
      {
        label: "Refer & Earn",
        href: "/billing/refer-and-earn",
        icon: Gift,
      },
    ],
    []
  );

  const isActive = (href: string) => {
    return (
      pathname === href ||
      (pathname.startsWith(`${href}/`) && href !== "/billing")
    );
  };

  return { state, toggleSidebar, sidebarLinks, isActive };
};

export default useBillingSidebar;
