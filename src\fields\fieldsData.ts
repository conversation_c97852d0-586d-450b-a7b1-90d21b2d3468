import AddressInput from "@/components/formInputs/AddressInput";
import ButtonInput from "@/components/formInputs/ButtonInput";
import CheckboxInput from "@/components/formInputs/CheckboxInput";
import DateInput from "@/components/formInputs/DateInput";
import Divider from "@/components/formInputs/Divider";
import DropdownInput from "@/components/formInputs/DropdownInput";
import EmailInput from "@/components/formInputs/EmailInput";
import NameInput from "@/components/formInputs/NameInput";
import NumberInput from "@/components/formInputs/NumberInput";
import PhoneFieldInput from "@/components/formInputs/PhoneInput";
import RadioButtonInput from "@/components/formInputs/RadioButtonInput";
import RatingsInput from "@/components/formInputs/RatingsInput";
import SignatureFormInput from "@/components/formInputs/SignatureFormInput";
import TextAreaInput from "@/components/formInputs/TextAreaInput";
import TextInput from "@/components/formInputs/TextInput";
import TimeInput from "@/components/formInputs/TimeInput";
import UploadInput from "@/components/formInputs/UploadInput";
import WebsiteInput from "@/components/formInputs/WebsiteInput";
import VoiceNoteInput from "@/components/formInputs/VoiceNoteInput";
import {
  faUser,
  faFileLines,
  faHouse,
  faCheckSquare,
  faSortDown,
  faDotCircle,
  faHashtag,
  faUpload,
  faCalendar,
  faClock,
  faPhone,
  faEnvelope,
  faSignature,
  faGlobe,
  faMinus,
  faSquare,
  faStar,
  faFont,
  faMicrophone,
} from "@fortawesome/free-solid-svg-icons";
import { fieldsConstants } from "./constants";
import { generateUUID } from "@/lib/gernerateuid";

const fieldsData = [
  {
    title: "Contacts Fields",
    items: [
      {
        name: "name",
        fieldName: "Name",
        type: "text",
        icon: faUser,
        component: "NAME_INPUT",
        firstNameTitle: fieldsConstants.NAME_FIELD.FIRST_NAME_TITLE,
        lastNameTitle: fieldsConstants.NAME_FIELD.LAST_NAME_TITLE,
        firstNamePlaceholder: fieldsConstants.NAME_FIELD.FIRST_NAME_PLACEHOLDER,
        lastNamePlaceholder: fieldsConstants.NAME_FIELD.SECOND_NAME_PLACEHOLDER,
        isFirstNameRequired: fieldsConstants.NAME_FIELD.IS_FIRSTNAME_REQUIRED,
        isLastNameRequired: fieldsConstants.NAME_FIELD.IS_LASTNAME_REQUIRED,
        titleMedia: fieldsConstants.NAME_FIELD.TITLE_MEDIA,
        isHide: false,
        isRequired: false,
        isDisable: false,
      },
      {
        name: "phone",
        fieldName: "Phone Number",
        type: "tel",
        icon: faPhone,
        component: "PHONE_FIELD",
        isRequired: fieldsConstants.CONTACT_FIELD.ISREQUIRED,
        description: fieldsConstants.CONTACT_FIELD.DESCRIPTION,
        title: fieldsConstants.CONTACT_FIELD.TITLE,
        placeholder: fieldsConstants.CONTACT_FIELD.PLACEHOLDER,
        titleMedia: fieldsConstants.CONTACT_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },
      {
        name: "email",
        fieldName: "Email",
        type: "email",
        icon: faEnvelope,
        component: "EMAIL",
        isRequired: fieldsConstants.EMAIL_FIELD.ISREQUIRED,
        description: fieldsConstants.EMAIL_FIELD.DESCRIPTION,
        title: fieldsConstants.EMAIL_FIELD.TITLE,
        placeholder: fieldsConstants.EMAIL_FIELD.PLACEHOLDER,
        titleMedia: fieldsConstants.EMAIL_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },
      {
        name: "address",
        fieldName: "Address",
        type: "text",
        icon: faHouse,
        component: "ADDRESS",
        description: fieldsConstants.ADDRESS_FIELD.DESCRIPTION,
        title: fieldsConstants.ADDRESS_FIELD.TITLE,
        allowedAddressFields: {
          country: true,
          state: true,
          city: true,
          pincode: true,
        },
        placeholder: fieldsConstants.ADDRESS_FIELD.PLACEHOLDER,
        titleMedia: fieldsConstants.ADDRESS_FIELD.TITLE_MEDIA,
        isRequired: fieldsConstants.ADDRESS_FIELD.ISREQUIRED,
        // isAddressRequired: fieldsConstants.ADDRESS_FIELD.IS_ADDRESS_REQUIRED,
        // isCountryREquired: fieldsConstants.ADDRESS_FIELD.IS_COUNTRY_REQUIRED,
        // isCityRequired: fieldsConstants.ADDRESS_FIELD.IS_CITY_REQUIRED,
        // isPincodeRequired: fieldsConstants.ADDRESS_FIELD.IS_PINCODE_REQUIRED,
        isHide: false,
        isDisable: false,
      },
      {
        name: "website",
        fieldName: "Website",
        type: "url",
        icon: faGlobe,
        component: "WEBSITE",
        isRequired: fieldsConstants.WEBSITE_FIELD.ISREQUIRED,
        description: fieldsConstants.WEBSITE_FIELD.DESCRIPTION,
        title: fieldsConstants.WEBSITE_FIELD.TITLE,
        placeholder: fieldsConstants.WEBSITE_FIELD.PLACEHOLDER,
        titleMedia: fieldsConstants.WEBSITE_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },
    ],
  },
  {
    title: "Basic Fields",
    items: [
      {
        name: "text",
        fieldName: "Text",
        type: "text",
        icon: faFont,
        component: "TEXT_INPUT",
        placeholder: fieldsConstants.TEXT_FIELD.PLACEHOLDER,
        isRequired: fieldsConstants.TEXT_FIELD.ISREQUIRED,
        description: fieldsConstants.TEXT_FIELD.DESCRIPTION,
        title: fieldsConstants.TEXT_FIELD.TITLE,
        titleMedia: fieldsConstants.TEXT_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },

      {
        name: "textarea",
        fieldName: "Text Area",
        type: "textarea",
        icon: faFileLines,
        component: "TEXT_AREA",
        placeholder: fieldsConstants?.TEXT_AREA_FIELD?.PLACEHOLDER,
        isRequired: fieldsConstants?.TEXT_AREA_FIELD?.ISREQUIRED,
        description: fieldsConstants.TEXT_AREA_FIELD.DESCRIPTION,
        title: fieldsConstants.TEXT_AREA_FIELD.TITLE,
        titleMedia: fieldsConstants.TEXT_AREA_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },

      {
        name: "checkbox",
        fieldName: "Checkbox",
        type: "checkbox",
        icon: faCheckSquare,
        component: "CHECKBOX",
        isRequired: fieldsConstants.CHECKBOX_FIELD.ISREQUIRED,
        description: fieldsConstants.CHECKBOX_FIELD.DESCRIPTION,
        title: fieldsConstants.CHECKBOX_FIELD.TITLE,
        titleMedia: fieldsConstants.CHECKBOX_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
        checkboxOptions: [
          {
            id: generateUUID(),
            label: "Option 1",
            editable: true,
            media: null,
          },
          {
            id: generateUUID(),
            label: "Option 2",
            editable: true,
            media: null,
          },
        ],
      },
      {
        name: "dropdown",
        fieldName: "Dropdown",
        type: "select",
        icon: faSortDown,
        component: "DROPDOWN",
        isRequired: fieldsConstants.DROPDOWN_FIELD.ISREQUIRED,
        description: fieldsConstants.DROPDOWN_FIELD.DESCRIPTION,
        title: fieldsConstants.DROPDOWN_FIELD.TITLE,
        titleMedia: fieldsConstants.DROPDOWN_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
        dropdownOptions: [
          {
            id: generateUUID(),
            text: "Option 1",
          },
          {
            id: generateUUID(),
            text: "Option 2",
          },
        ],
      },
      {
        name: "radio",
        fieldName: "Radio Button",
        type: "radio",
        icon: faDotCircle,
        component: "RADIO_BUTTON",
        isRequired: fieldsConstants.RADIO_FIELD.ISREQUIRED,
        description: fieldsConstants.RADIO_FIELD.DESCRIPTION,
        title: fieldsConstants.RADIO_FIELD.TITLE,
        titleMedia: fieldsConstants.RADIO_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
        radioOptions: [
          {
            id: generateUUID(),
            text: "Option 1",
            editable: true,
            media: null,
            isOther: false,
          },
          {
            id: generateUUID(),
            text: "Option 2",
            editable: true,
            media: null,
            isOther: false,
          },
        ],
      },
      {
        name: "number",
        fieldName: "Number",
        type: "number",
        icon: faHashtag,
        component: "NUMBER",
        isRequired: fieldsConstants.NUMBER_FIELD.ISREQUIRED,
        description: fieldsConstants.NUMBER_FIELD.DESCRIPTION,
        title: fieldsConstants.NUMBER_FIELD.TITLE,
        placeholder: fieldsConstants.NUMBER_FIELD.PLACEHOLDER,
        titleMedia: fieldsConstants.NUMBER_FIELD.TITLE_MEDIA,
        validationType: fieldsConstants.NUMBER_FIELD.VALIDATION_TYPE,
        validationValue: "",
        validationValue2: "",
        isHide: false,
        isDisable: false,
      },
      {
        name: "upload",
        fieldName: "Upload",
        type: "file",
        icon: faUpload,
        component: "UPLOAD",
        isRequired: fieldsConstants.UPLOAD_FIELD.ISREQUIRED,
        description: fieldsConstants.UPLOAD_FIELD.DESCRIPTION,
        title: fieldsConstants.UPLOAD_FIELD.TITLE,
        placeholder: fieldsConstants.UPLOAD_FIELD.PLACEHOLDER,
        titleMedia: fieldsConstants.UPLOAD_FIELD.TITLE_MEDIA,
        minSize: fieldsConstants.UPLOAD_FIELD.MIN_SIZE,
        maxSize: fieldsConstants.UPLOAD_FIELD.MAX_SIZE,
        isHide: false,
        isDisable: false,
      },
      {
        name: "date",
        fieldName: "Date",
        type: "date",
        icon: faCalendar,
        component: "DATE",
        isRequired: fieldsConstants.DATE_FIELD.ISREQUIRED,
        description: fieldsConstants.DATE_FIELD.DESCRIPTION,
        placeholder: fieldsConstants.DATE_FIELD.PLACEHOLDER,
        title: fieldsConstants.DATE_FIELD.TITLE,
        titleMedia: fieldsConstants.DATE_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },
      {
        name: "time",
        fieldName: "Time",
        type: "time",
        icon: faClock,
        component: "TIME",
        isRequired: fieldsConstants.TIME_FIELD.ISREQUIRED,
        description: fieldsConstants.TIME_FIELD.DESCRIPTION,
        title: fieldsConstants.TIME_FIELD.TITLE,
        titleMedia: fieldsConstants.TIME_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },
    ],
  },
  {
    title: "Other",
    items: [
      {
        name: "ratings",
        fieldName: "Ratings",
        type: "rating",
        icon: faStar,
        component: "RATINGS",
        isRequired: fieldsConstants.RATING_FIELD.ISREQUIRED,
        description: fieldsConstants.RATING_FIELD.DESCRIPTION,
        title: fieldsConstants.RATING_FIELD.TITLE,
        rating: fieldsConstants.RATING_FIELD.RATING,
        titleMedia: fieldsConstants.RATING_FIELD.TITLE_MEDIA,
        isHide: false,
        isDisable: false,
      },
      {
        name: "voicenote",
        fieldName: "Voice Note",
        type: "voicenote",
        icon: faMicrophone,
        component: "VOICE_NOTE",
        isRequired: fieldsConstants.VOICE_NOTE_FIELD.ISREQUIRED,
        description: fieldsConstants.VOICE_NOTE_FIELD.DESCRIPTION,
        title: fieldsConstants.VOICE_NOTE_FIELD.TITLE,
        titleMedia: fieldsConstants.VOICE_NOTE_FIELD.TITLE_MEDIA,
        maxDuration: fieldsConstants.VOICE_NOTE_FIELD.MAX_DURATION,
        isHide: false,
        isDisable: false,
      },
    ],
  },
];

export const toolContainersElement = {
  TEXT_INPUT: TextInput,
  NAME_INPUT: NameInput,
  TEXT_AREA: TextAreaInput,
  ADDRESS: AddressInput,
  CHECKBOX: CheckboxInput,
  DROPDOWN: DropdownInput,
  RADIO_BUTTON: RadioButtonInput,
  NUMBER: NumberInput,
  UPLOAD: UploadInput,
  DATE: DateInput,
  TIME: TimeInput,
  PHONE_FIELD: PhoneFieldInput,
  EMAIL: EmailInput,
  SIGNATURE: SignatureFormInput,
  WEBSITE: WebsiteInput,
  RATINGS: RatingsInput,
  VOICE_NOTE: VoiceNoteInput,
};

export default fieldsData;
