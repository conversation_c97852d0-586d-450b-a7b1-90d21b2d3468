"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Lock, FolderOpen } from "lucide-react";
import RolesList from "@/components/members/RolesList";
import RolePermissions from "@/components/members/RolePermissions";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "react-hot-toast";
import {
  useCreateRole,
  useGetAllRoleBasedOnWorkspace,
  useDeleteRole,
} from "@/api-services/role";
import { useUserProfile } from "@/api-services/auth";
import { useQueryClient } from "@tanstack/react-query";
import { useGetPermission } from "@/api-services/permission";
import Loader from "@/components/common/loader";

interface ApiRole {
  role_id: string;
  role_name: string;
  role_description: string;
  member_count: number;
}

interface Role {
  id: string;
  name: string;
  description: string;
  usersCount: number;
}

const initialRoles = [
  {
    name: "Admin",
    description: "Full system access",
    usersCount: 3,
  },
  {
    name: "Editor",
    description: "Can edit content and manage users",
    usersCount: 8,
  },
  {
    name: "Viewer",
    description: "Read-only access to content",
    usersCount: 15,
  },
];

const formBuilderPermissions = [
  {
    id: "98dca612-46c5-48e6-ba91-723c4f3beca5",
    name: "Create Forms",
    description: "Can create new forms",
    defaultCheckedFor: ["Admin", "Editor"],
  },
  {
    id: "d7ae13c3-ae83-44db-9781-9285b8c88967",
    name: "Edit Forms",
    description: "Can modify existing forms",
    defaultCheckedFor: ["Admin", "Editor"],
  },
  {
    id: "a8b9c0d1-e2f3-4g5h-6i7j-8k9l0m1n2o3p",
    name: "Delete Forms",
    description: "Can remove forms from the system",
    defaultCheckedFor: ["Admin"],
  },
  {
    id: "b1c2d3e4-f5g6-7h8i-9j0k-l1m2n3o4p5q6",
    name: "View Form Submissions",
    description: "Can view data submitted through forms",
    defaultCheckedFor: ["Admin", "Editor", "Viewer"],
  },
  {
    id: "c2d3e4f5-g6h7-i8j9-k0l1-m2n3o4p5q6r7",
    name: "Export Form Data",
    description: "Can export form submission data",
    defaultCheckedFor: ["Admin", "Editor"],
  },
];

const folderPermissions = [
  {
    id: "d3e4f5g6-h7i8-j9k0-l1m2-n3o4p5q6r7s8",
    name: "Create Folders",
    description: "Can create new folders",
    defaultCheckedFor: ["Admin", "Editor"],
  },
  {
    id: "e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9",
    name: "Edit Folders",
    description: "Can modify existing folders",
    defaultCheckedFor: ["Admin", "Editor"],
  },
  {
    id: "f5g6h7i8-j9k0-l1m2-n3o4-p5q6r7s8t9u0",
    name: "Delete Folders",
    description: "Can remove folders from the system",
    defaultCheckedFor: ["Admin"],
  },
  {
    id: "g6h7i8j9-k0l1-m2n3-o4p5-q6r7s8t9u0v1",
    name: "Share Folders",
    description: "Can share folders with other users",
    defaultCheckedFor: ["Admin", "Editor"],
  },
  {
    id: "h7i8j9k0-l1m2-n3o4-p5q6-r7s8t9u0v1w2",
    name: "View Folders",
    description: "Can view folder contents",
    defaultCheckedFor: ["Admin", "Editor", "Viewer"],
  },
];

const Page = () => {
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isAddRoleOpen, setIsAddRoleOpen] = useState(false);
  const [newRoleName, setNewRoleName] = useState("");
  const [newRoleDescription, setNewRoleDescription] = useState("");

  const queryClient = useQueryClient();
  const { data: userProfile } = useUserProfile();
  const workspaceId = userProfile?.data?.user?.workspace_id;

  const { data: rolesData, isLoading: isLoadingRoles } =
    useGetAllRoleBasedOnWorkspace(workspaceId);
  const { mutate: createRole } = useCreateRole();
  const { mutate: deleteRole } = useDeleteRole();

  // Transform API roles data to match our component's format
  const roles: Role[] =
    rolesData?.data?.roles?.map((role: ApiRole) => ({
      id: role.role_id,
      name: role.role_name,
      description: role.role_description || "No description provided",
      usersCount: role.member_count,
    })) || [];

  const selectedRoleData = selectedRole || roles[0];

  const { data: permissionData, isLoading: isLoadingPermission } =
    useGetPermission(selectedRoleData?.id);

  console.log(permissionData, "permissionData");
  console.log("Form permissions:", permissionData?.data?.data?.form);
  console.log("Integration permission:", permissionData?.data?.data?.form?.FORM_INTEGRATION);

  const permissions = permissionData?.data?.data;
  const getPermissionGroups = (roleName: string) => [
    {
      name: "Form Permission",
      icon: <Lock className="h-4 w-4" />,
      permissions: [
        {
          id: permissions?.form?.CREATE_FORM?.permission_id,
          name: "Create Form",
          description: "Can create new forms",
          isChecked: permissions?.form?.CREATE_FORM?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.form?.EDIT_FORM?.permission_id,
          name: "Edit Form",
          description: "Can modify existing forms",
          isChecked: permissions?.form?.EDIT_FORM?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.form?.DELETE_FORM?.permission_id,
          name: "Delete Form",
          description: "Can remove forms from the system",
          isChecked: permissions?.form?.DELETE_FORM?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.form?.VIEW_FORM_SUNMISSION?.permission_id,
          name: "View Form Submissions",
          description: "Can view data submitted through forms",
          isChecked:
            permissions?.form?.VIEW_FORM_SUNMISSION?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.form?.EXPORT_FROM_DATA?.permission_id,
          name: "Export Form Data",
          description: "Can export form submission data",
          isChecked: permissions?.form?.EXPORT_FROM_DATA?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.form?.FORM_INTEGRATION?.permission_id,
          name: "Integration",
          description: "Can configure and manage form integrations",
          isChecked: permissions?.form?.FORM_INTEGRATION?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
      ],
      isOpen: true,
    },
    {
      name: "Folder Permission",
      icon: <FolderOpen className="h-4 w-4" />,
      permissions: [
        {
          id: permissions?.folder?.CREATE_FOLDER?.permission_id,
          name: "Create Folders",
          description: "Can create new folders",
          isChecked: permissions?.folder?.CREATE_FOLDER?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.folder?.EDIT_FOLDER?.permission_id,
          name: "Edit Folders",
          description: "Can edit folders",
          isChecked: permissions?.folder?.EDIT_FOLDER?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.folder?.DELETE_FOLDER?.permission_id,
          name: "Delete Folders",
          description: "Can delete folders",
          isChecked: permissions?.folder?.DELETE_FOLDER?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
        {
          id: permissions?.folder?.VIEW_FOLDER?.permission_id,
          name: "View Folders",
          description: "Can view folders",
          isChecked: permissions?.folder?.VIEW_FOLDER?.is_assigned || false,
          isDisabled: roleName === "Admin",
        },
      ],
      isOpen: true,
    },
  ];

  const handleCreateRole = () => {
    if (!newRoleName.trim()) {
      toast.error("Please enter a role name");
      return;
    }

    const newRole = {
      name: newRoleName.trim(),
      description: newRoleDescription.trim() || "No description provided",
      workspaceId: workspaceId,
    };

    createRole(newRole, {
      onSuccess: () => {
        // Invalidate and refetch roles query
        queryClient.invalidateQueries({ queryKey: ["roles", workspaceId] });
        setNewRoleName("");
        setNewRoleDescription("");
        setIsAddRoleOpen(false);
        toast.success("New role created successfully");
      },
      onError: () => {
        toast.error("Failed to create role");
      },
    });
  };

  const handleDeleteRole = async (role: Role) => {
    deleteRole(role.id, {
      onSuccess: () => {
        // Invalidate and refetch roles query
        queryClient.invalidateQueries({ queryKey: ["roles", workspaceId] });
        // If the deleted role was selected, clear the selection
        if (selectedRole?.id === role.id) {
          setSelectedRole(null);
        }
      },
      onError: () => {
        toast.error("Failed to delete role");
      },
    });
  };

  if (isLoadingRoles) {
    return <Loader />;
  }

  return (
    <div className="p-6  min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2 text-app-text-color">
          <Lock className="h-6 w-6" />
          <h1 className="text-2xl font-semibold">Roles & Permissions</h1>
        </div>
        <Button
          className="flex items-center gap-2 h-8 bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color rounded-xl"
          onClick={() => setIsAddRoleOpen(true)}
        >
          <Plus className="h-4 w-4" />
          Add New Role
        </Button>
      </div>

      {/* <div className="mb-6">
        <Input
          type="text"
          placeholder="Search roles..."
          className="max-w-sm border border-app-border-primary bg-app-background placeholder:text-app-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        />
      </div> */}

      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-4">
          <RolesList
            roles={roles}
            selectedRole={selectedRole}
            onRoleSelect={(role) => setSelectedRole(role as Role)}
            onDeleteRole={handleDeleteRole}
          />
        </div>
        {selectedRoleData && (
          <div className="col-span-8">
            {isLoadingPermission ? (
              <div className="text-gray-500">Loading permissions...</div>
            ) : (
              <RolePermissions
                roleId={selectedRoleData.id}
                roleName={selectedRoleData.name}
                description={selectedRoleData.description}
                usersCount={selectedRoleData.usersCount}
                permissionGroups={getPermissionGroups(selectedRoleData.name)}
                isAdmin={selectedRoleData.name === "Admin"}
              />
            )}
          </div>
        )}
      </div>

      <Dialog open={isAddRoleOpen} onOpenChange={setIsAddRoleOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">Add New Role</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-app-text-color">
                Role Name
              </label>
              <Input
                type="text"
                placeholder="Enter role name"
                value={newRoleName}
                onChange={(e) => setNewRoleName(e.target.value)}
                className="w-full border bg-app-background focus:border-[#1f311c] focus:ring-[#1f311c]"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-app-text-color">
                Description
              </label>
              <textarea
                placeholder="Enter role description"
                value={newRoleDescription}
                onChange={(e) => setNewRoleDescription(e.target.value)}
                className="w-full min-h-[100px] rounded-md border bg-app-background  p-2"
              />
            </div>
            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="outline"
                className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color"
                onClick={() => {
                  setIsAddRoleOpen(false);
                  setNewRoleName("");
                  setNewRoleDescription("");
                }}
              >
                Cancel
              </Button>
              <Button
                className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color"
                onClick={handleCreateRole}
              >
                Create Role
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Page;
