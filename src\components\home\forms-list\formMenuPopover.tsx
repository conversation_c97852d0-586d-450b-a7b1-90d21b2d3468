import React from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  File,
  Edit,
  Files,
  Trash,
  Share2,
  MessageSquare,
  EllipsisVertical,
} from "lucide-react";
import { usePermission } from "@/hooks/usePersmission";
interface FormActionsPopoverProps {
  form: {
    id: string;
    published: boolean;
  };
  handleAction: (action: string) => void;
  popoverOpen: boolean;
  setPopoverOpen: (open: boolean) => void;
}

const FormMenuPopover: React.FC<FormActionsPopoverProps> = ({
  form,
  handleAction,
  popoverOpen,
  setPopoverOpen,
}) => {
  const {PermissionProtected} = usePermission();
  const handleClick = (action: string) => {
    handleAction(action);
    setPopoverOpen(false);
  };
  return (
    <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
      <PopoverTrigger asChild>
        <div className="p-2 hover:bg-app-hero-background text-app-text-secondary rounded-full cursor-pointer">
          <EllipsisVertical />
        </div>
      </PopoverTrigger>
      <PopoverContent side="top" align="end" className="w-40 p-2">
        <div className="w-full flex flex-col gap-2 text-app-text-color">
          {form?.published && (
            <div
              className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
              onClick={() => handleAction("open")}
            >
              <File className="w-4 h-4" />
              <span>Open Form</span>
            </div>
          )}
          <PermissionProtected permissionKey="edit_form">
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
            onClick={() => handleAction("edit")}
          >
            <Edit className="w-4 h-4" />
            <span>Edit</span>
          </div>
          </PermissionProtected>
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
            onClick={() => handleAction("duplicate")}
          >
            <Files className="w-4 h-4" />
            <span>Duplicate</span>
          </div>
          <PermissionProtected permissionKey="delete_form" >
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
            onClick={() => handleAction("delete")}
          >
            <Trash className="w-4 h-4" />
            <span>Delete</span>
          </div>
          </PermissionProtected>
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
            onClick={() => handleAction("share")}
          >
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </div>
          {/* <div
            className="flex items-center gap-2 cursor-pointer hover:bg-app-main-background rounded-lg p-2"
            onClick={() => handleAction("responses")}
          >
            <MessageSquare className="w-4 h-4" />
            <span>Responses</span>
          </div> */}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default FormMenuPopover;
