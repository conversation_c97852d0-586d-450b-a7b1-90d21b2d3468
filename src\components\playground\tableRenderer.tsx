"use client";
import { useState, useRef, useImperative<PERSON><PERSON><PERSON>, forwardRef, useCallback } from "react";
import { AgGridReact } from "ag-grid-react";

import {
  AllCommunityModule,
  ModuleRegistry,
  themeMaterial,
} from "ag-grid-community";
import { DeleteResponse } from "./delete-responses/delete-responses";
import { ParamsType } from "@/api-services/form_response";
import { useGetFormFields } from "@/api-services/form_fields";

// Register all community features
ModuleRegistry.registerModules([AllCommunityModule]);

// Define the interface for methods exposed to parent component
export interface TableRendererRef {
  getAgGridFilterModel: () => any;
  setAgGridFilterModel: (model: any) => void;
  resetAgGridFilters: () => void;
}

const TableRenderer = forwardRef<TableRendererRef, {
  data: Record<string, any>[];
  id: string;
  params: ParamsType;
  isLoading: boolean;
  error: any;
  onFilterChanged?: (model: any) => void;
}>(({ data, id, params, error, isLoading, onFilterChanged }, ref) => {
  const rowData = data || [];
  const [selectedRowsId, setSelectedRowsId] = useState<string[]>([]);
  const { data: formFieldsData } = useGetFormFields(id);
  const gridRef = useRef<AgGridReact>(null);

  // Track which columns have been resized
  const [autoHeightCols, setAutoHeightCols] = useState<string[]>([]);

  // Handler for column resize
  const handleColumnResized = useCallback((event: any) => {
    if (event.column && event.finished) {
      const colId = event.column.getColId();
      if (!autoHeightCols.includes(colId)) {
        setAutoHeightCols((prev) => [...prev, colId]);
      }
    }
  }, [autoHeightCols]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    getAgGridFilterModel: () => {
      if (gridRef.current && gridRef.current.api) {
        const filterModel = gridRef.current.api.getFilterModel();
        console.log('AG Grid filter model:', filterModel);
        return filterModel;
      }
      return null;
    },
    setAgGridFilterModel: (model: any) => {
      if (gridRef.current && gridRef.current.api) {
        console.log('Setting AG Grid filter model:', model);
        gridRef.current.api.setFilterModel(model);
        gridRef.current.api.onFilterChanged();
      }
    },
    resetAgGridFilters: () => {
      if (gridRef.current && gridRef.current.api) {
        console.log('Resetting AG Grid filters');
        gridRef.current.api.setFilterModel(null);
        gridRef.current.api.onFilterChanged();
      }
    }
  }));

  if (isLoading) return <p>Loading...</p>;
  if (error) return <p className="text-red-500">Error loading responses.</p>;

  const checkboxColumn = {
    headerName: "",
    field: "checkbox",
    width: 50,
    checkboxSelection: true,
    headerCheckboxSelection: true,
  };

  // Get all fields from form definition
  const fields = formFieldsData?.data?.fields || [];

  // Helper to find the best matching field for a data key
  function getFieldTitleForKey(key: string) {
    // 1. Try to match by id
    let field = fields.find((f: any) => f.id === key);
    if (field) return field.title || field.name;

    // 2. Try to match by name
    field = fields.find((f: any) => f.name === key);
    if (field) return field.title || field.name;

    // 3. Try to match by type/component (for generic keys like 'phone', 'checkbox', 'radio')
    field = fields.find(
      (f: any) =>
        (f.type && f.type.toLowerCase() === key.toLowerCase()) ||
        (f.component && f.component.toLowerCase() === key.toLowerCase())
    );
    if (field) return field.title || field.name;

    // 4. Fallback to the key itself
    return key;
  }

  // Helper to check if a field is a date field
  function isDateField(key: string) {
    // You can add more keys or check field type from form definition if needed
    return (
      key.toLowerCase().includes("date") ||
      key.toLowerCase().includes("submittedat")
    );
  }

  // Helper to check if a field is a number field
  function isNumberField(key: string) {
    const field = fields.find((f: any) => f.title === key || f.name === key);
    return field && (field.component === 'NUMBER' || field.type === 'NUMBER');
  }

  // Helper to truncate text and show tooltip, with special handling for date fields
  const truncateCellRenderer = (params: any) => {
    const value = params.value || "";
    const key = params.colDef.field;

    // Special handling for date fields
    if (isDateField(key) && value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return <span title={value}>{date.toLocaleDateString()}</span>;
      }
      return <span title={value}>{value}</span>;
    }

    // Always show full value, and wrap text
    return (
      <span
        title={value}
        style={{ whiteSpace: "pre-line", wordBreak: "break-word" }}
        className="whitespace-pre-line break-words"
      >
        {value}
      </span>
    );
  };

  const dataCols = Object.keys(data?.[0] || {})
    .filter((item) => item !== "responseId") // Remove responseId column
    .map((item) => {
      const isNumber = isNumberField(item);
      const isAutoHeight = autoHeightCols.includes(item);
      return {
        field: item,
        headerName: getFieldTitleForKey(item),
        headerTooltip: getFieldTitleForKey(item), // Show full title on hover
        filter: isNumber ? 'agNumberColumnFilter' : true,
        filterParams: isNumber
          ? {
              buttons: ['apply', 'reset'],
              filterOptions: [
                'equals',
                'notEqual',
                'lessThan',
                'lessThanOrEqual',
                'greaterThan',
                'greaterThanOrEqual',
                'inRange',
              ],
            }
          : undefined,
        cellRenderer: truncateCellRenderer,
        valueGetter: isNumber
          ? (params: any) => {
              const val = params.data[item];
              return typeof val === 'number' ? val : Number(val);
            }
          : undefined,
        autoHeight: isAutoHeight, // Only enable autoHeight if column was resized
        cellClass: isAutoHeight ? 'whitespace-pre-line break-words' : 'ag-nowrap',
      };
    });

  // Only include checkbox column if there are rows to display
  const colDefs = rowData.length > 0 ? [checkboxColumn, ...dataCols] : dataCols;

  return (
    <div className="ag-theme-material h-full w-full">
      {selectedRowsId?.length > 0 && rowData.length > 0 && (
        <DeleteResponse selectedIds={selectedRowsId} id={id} params={params} />
      )}
      <AgGridReact
        ref={gridRef}
        rowData={rowData}
        columnDefs={colDefs}
        theme={themeMaterial}
        rowSelection="multiple"
        onSelectionChanged={(event) => {
          const selectedNodes = event.api.getSelectedNodes();
          const selectedData = selectedNodes.map((node) => node.data);
          setSelectedRowsId(selectedData?.map((data) => data?.responseId));
        }}
        onGridReady={(params) => {
          console.log('AG Grid is ready');
        }}
        onFilterChanged={(event) => {
          console.log('AG Grid filters changed');
          if (onFilterChanged && gridRef.current && gridRef.current.api) {
            onFilterChanged(gridRef.current.api.getFilterModel());
          }
        }}
        onColumnResized={handleColumnResized}
      />
    </div>
  );
});

TableRenderer.displayName = 'TableRenderer';

export default TableRenderer;
