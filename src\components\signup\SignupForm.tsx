"use client";

import React, { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useCreateWorkspace } from "@/api-services/onboarding";

interface FormData {
  organizationName: string;
  businessCategory: string;
  teamSize: string;
  designation: string;
}

const SignupForm = () => {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    organizationName: "",
    businessCategory: "",
    teamSize: "",
    designation: "",
  });
  const [isCustomCategory, setIsCustomCategory] = useState(false);
  const [isCustomTeamSize, setIsCustomTeamSize] = useState(false);
  const [isCustomDesignation, setIsCustomDesignation] = useState(false);

  // Refs for input fields
  const organizationInputRef = useRef<HTMLInputElement>(null);
  const businessCategoryInputRef = useRef<HTMLInputElement>(null);
  const teamSizeInputRef = useRef<HTMLInputElement>(null);
  const designationInputRef = useRef<HTMLInputElement>(null);
  const { mutate: createWorkspace, isPending } = useCreateWorkspace();

  const handleInputChange = (field: keyof FormData, value: string) => {
    if (field === "businessCategory") {
      if (value === "Others") {
        setIsCustomCategory(true);
        setFormData((prev) => ({ ...prev, [field]: "" }));
      } else {
        setIsCustomCategory(false);
        setFormData((prev) => ({ ...prev, [field]: value }));
      }
    } else if (field === "teamSize") {
      if (value === "Others") {
        setIsCustomTeamSize(true);
        setFormData((prev) => ({ ...prev, [field]: "" }));
      } else {
        setIsCustomTeamSize(false);
        setFormData((prev) => ({ ...prev, [field]: value }));
      }
    } else if (field === "designation") {
      if (value === "Others") {
        setIsCustomDesignation(true);
        setFormData((prev) => ({ ...prev, [field]: "" }));
      } else {
        setIsCustomDesignation(false);
        setFormData((prev) => ({ ...prev, [field]: value }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [field]: value }));
    }

    // Update input field value when option is selected
    switch (field) {
      case "businessCategory":
        if (businessCategoryInputRef.current && value !== "Others") {
          businessCategoryInputRef.current.value = value;
          businessCategoryInputRef.current.focus();
        }
        break;
      case "teamSize":
        if (teamSizeInputRef.current && value !== "Others") {
          teamSizeInputRef.current.value = value;
          teamSizeInputRef.current.focus();
        }
        break;
      case "designation":
        if (designationInputRef.current && value !== "Others") {
          designationInputRef.current.value = value;
          designationInputRef.current.focus();
        }
        break;
    }
  };

  const handleNext = () => {
    setStep((prev) => prev + 1);
  };

  const handleBack = () => {
    setStep((prev) => prev - 1);
  };

  const handleSubmit = async () => {
    console.log("Form submitted:", formData);
    const refferal= localStorage.getItem("referral")|| undefined;
    createWorkspace(
      {
        name: formData.organizationName,
        industry: formData.businessCategory,
        team_size: formData.teamSize,
        tags: [formData.designation],
        description: "description",
        referral_code: refferal
      },
      {
        onSuccess: async (data) => {
          localStorage.removeItem("referral");
          router.push("/home");
        },
        onError: (error) => {
          console.error("Error creating workspace:", error);
        },
      }
    );
  };

  const handleKeyPress = (
    e: React.KeyboardEvent<HTMLInputElement>,
    field: keyof FormData
  ) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (step < 4) {
        handleNext();
      } else {
        handleSubmit();
      }
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-4 min-h-[500px]  h-full">
            <div className="mb-2">
              <div className="h-2 w-full bg-gray-200 rounded-full">
                <div
                  className="h-2 bg-[#0F2B1B] rounded-full"
                  style={{ width: "25%" }}
                />
              </div>
              <p className="text-sm text-gray-600 mt-1">Step {step}/4</p>
            </div>
            <h2 className="text-2xl font-semibold">
              What is your Organization Name?
            </h2>
            <input
              ref={organizationInputRef}
              type="text"
              value={formData.organizationName}
              onChange={(e) =>
                handleInputChange("organizationName", e.target.value)
              }
              onKeyPress={(e) => handleKeyPress(e, "organizationName")}
              className="w-full border-b border-gray-300 focus:border-[#0F2B1B] outline-none py-2"
              placeholder="Enter organization name here"
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-4 min-h-[500px] h-full">
            <div className="mb-2">
              <div className="h-2 w-full bg-gray-200 rounded-full">
                <div
                  className="h-2 bg-[#0F2B1B] rounded-full"
                  style={{ width: "50%" }}
                />
              </div>
              <p className="text-sm text-gray-600 mt-1">Step {step}/4</p>
            </div>
            <h2 className="text-2xl font-semibold">
              What is your business category?
            </h2>
            <div className="flex flex-wrap gap-2 mb-4">
              {[
                "Manufacturing",
                "IT",
                "Sales",
                "Marketing",
                "Customer Support",
                "Operations",
                "HR/Admin",
                "General",
                "Automation",
                "Others",
              ].map((option) => (
                <button
                  key={option}
                  type="button"
                  className={`px-4 py-2 rounded-full ${
                    (isCustomCategory && option === "Others") ||
                    (!isCustomCategory && formData.businessCategory === option)
                      ? "bg-[#0F2B1B] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => handleInputChange("businessCategory", option)}
                >
                  {option}
                </button>
              ))}
            </div>
            {isCustomCategory && (
              <input
                ref={businessCategoryInputRef}
                type="text"
                value={formData.businessCategory}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    businessCategory: e.target.value,
                  }));
                }}
                onKeyPress={(e) => handleKeyPress(e, "businessCategory")}
                className="w-full border-b border-gray-300 focus:border-[#0F2B1B] outline-none py-2"
                placeholder="Enter your business category"
                autoFocus
              />
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-4 min-h-[500px] h-full">
            <div className="mb-2">
              <div className="h-2 w-full bg-gray-200 rounded-full">
                <div
                  className="h-2 bg-[#0F2B1B] rounded-full"
                  style={{ width: "75%" }}
                />
              </div>
              <p className="text-sm text-gray-600 mt-1">Step {step}/4</p>
            </div>
            <h2 className="text-2xl font-semibold">What is your Team size?</h2>
            <div className="flex flex-col w-full rounded overflow-hidden shadow-sm">
              {["1-10", "11-20", "21-30", "31-50", "51 & Above", "Others"].map(
                (option, idx) => (
                  <button
                    key={option}
                    type="button"
                    className={`w-full text-left px-6 py-4 focus:outline-none transition-colors duration-150
                    ${
                      (isCustomTeamSize && option === "Others") ||
                      (!isCustomTeamSize && formData.teamSize === option)
                        ? "bg-[#0F2B1B] text-white"
                        : idx % 2 === 0
                        ? "bg-gray-100"
                        : "bg-gray-200"
                    }
                  `}
                    onClick={() => handleInputChange("teamSize", option)}
                  >
                    {option}
                  </button>
                )
              )}
            </div>
            {isCustomTeamSize && (
              <input
                ref={teamSizeInputRef}
                type="text"
                value={formData.teamSize}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    teamSize: e.target.value,
                  }));
                }}
                onKeyPress={(e) => handleKeyPress(e, "teamSize")}
                className="w-full border-b border-gray-300 focus:border-[#0F2B1B] outline-none py-2 mt-4"
                placeholder="Enter your team size"
                autoFocus
              />
            )}
          </div>
        );

      case 4:
        return (
          <div className="space-y-4 min-h-[500px] h-full">
            <div className="mb-2">
              <div className="h-2 w-full bg-gray-200 rounded-full">
                <div
                  className="h-2 bg-[#0F2B1B] rounded-full"
                  style={{ width: "100%" }}
                />
              </div>
              <p className="text-sm text-gray-600 mt-1">Step {step}/4</p>
            </div>
            <h2 className="text-2xl font-semibold">
              What is your designation?
            </h2>
            <div className="flex flex-wrap gap-2 mb-4">
              {[
                "Product Designer",
                "Developer",
                "Software Engineer",
                "QA Tester",
                "Team Lead",
                "Chief Technology Officer (CTO)",
                "Director",
                "Others",
              ].map((option) => (
                <button
                  key={option}
                  type="button"
                  className={`px-4 py-2 rounded-full ${
                    (isCustomDesignation && option === "Others") ||
                    (!isCustomDesignation && formData.designation === option)
                      ? "bg-[#0F2B1B] text-white"
                      : "bg-gray-100"
                  }`}
                  onClick={() => handleInputChange("designation", option)}
                >
                  {option}
                </button>
              ))}
            </div>
            {isCustomDesignation && (
              <input
                ref={designationInputRef}
                type="text"
                value={formData.designation}
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    designation: e.target.value,
                  }));
                }}
                onKeyPress={(e) => handleKeyPress(e, "designation")}
                className="w-full border-b border-gray-300 focus:border-[#0F2B1B] outline-none py-2"
                placeholder="Enter your designation"
                autoFocus
              />
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center w-full overflow-auto py-8">
      <div className="flex flex-col gap-y-4 p-4 w-full min-[601px]:max-w-md max-[600px]:w-full">
        {/* Form Content */}
        <div>{renderStep()}</div>

        {/* Bottom Navigation and Login Link */}
        <div className="space-y-4 w-full">
          <div className="flex justify-between items-center ">
            {step > 1 && (
              <button
                type="button"
                onClick={handleBack}
                className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                BACK
              </button>
            )}
            {step < 4
              ? formData[
                  step === 1
                    ? "organizationName"
                    : step === 2
                    ? "businessCategory"
                    : step === 3
                    ? "teamSize"
                    : "designation"
                ].trim() !== "" && (
                  <button
                    type="button"
                    onClick={handleNext}
                    className="px-6 py-2 bg-[#0F2B1B] text-white rounded-md ml-auto hover:bg-[#1A3B29]"
                  >
                    NEXT
                  </button>
                )
              : formData.designation.trim() !== "" && (
                  <button
                    type="button"
                    onClick={handleSubmit}
                    className="px-6 py-2 bg-[#0F2B1B] text-white rounded-md ml-auto hover:bg-[#1A3B29]"
                  >
                    DONE
                  </button>
                )}
          </div>
          {/* <div className="text-center border-t border-gray-100 pt-4">
            <p className="text-sm text-gray-600">
              Already have an Account?{" "}
              <button
                onClick={() => router.push("/login")}
                className="text-[#0F2B1B] font-medium hover:text-[#1A3B29]"
              >
                Login
              </button>
            </p>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default SignupForm;
