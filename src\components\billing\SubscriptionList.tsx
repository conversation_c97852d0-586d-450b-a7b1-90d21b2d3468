import { useGetSubscription } from "@/api-services/subscription";
import React from "react";
import clsx from "clsx";
import Loader from "../common/loader";

type Subscription = {
  id: number;
  validity: string;
  status: string;
  module: {
    name?: string;
    code?: string;
  };
  created_at: string;
};

const SubscriptionList = () => {
  const { data: subscriptionData, isLoading: isLoadingSubscription } =
    useGetSubscription();

  if (isLoadingSubscription) {
    return <Loader />;
  }

  const subscriptions: Subscription[] =
    subscriptionData?.data?.subscriptions || [];

  return (
    <div className="flex flex-col gap-2">
      <h2 className="text-xl font-semibold">Subscriptions</h2>
      <div className="bg-app-background rounded-xl p-6 w-full shadow border">
        <table className="w-full text-sm">
          <thead>
            <tr>
              <th className="py-3 px-4 text-left font-semibold text-app-text-secondary border-b">
                Date
              </th>
              <th className="py-3 px-4 text-left font-semibold text-app-text-secondary border-b">
                Plan
              </th>
              <th className="py-3 px-4 text-left font-semibold text-app-text-secondary border-b">
                Valid Till
              </th>
              <th className="py-3 px-4 text-left font-semibold text-app-text-secondary border-b">
                Status/Action
              </th>
            </tr>
          </thead>
          <tbody>
            {subscriptions.length === 0 ? (
              <tr>
                <td
                  colSpan={4}
                  className="py-8 text-center text-app-text-secondary"
                >
                  No subscriptions found.
                </td>
              </tr>
            ) : (
              subscriptions.map((sub: Subscription, idx: number) => (
                <tr
                  key={sub.id}
                  className={clsx(
                    "transition-colors ",
                    idx % 2 === 0
                      ? "bg-app-main-background"
                      : "bg-app-background",
                    "hover:bg-app-sidebar-hover "
                  )}
                >
                  <td className="py-4 px-4 text-app-text-color">
                    {new Date(sub.created_at).toLocaleDateString("en-GB", {
                      day: "2-digit",
                      month: "short",
                      year: "numeric",
                    })}
                  </td>
                  <td className="py-4 px-4 text-app-text-color">
                    {sub.module?.name || sub.module?.code}
                  </td>
                  <td className="py-4 px-4 text-app-text-color">
                    {new Date(sub.validity).toLocaleDateString("en-GB", {
                      day: "2-digit",
                      month: "short",
                      year: "numeric",
                    })}
                  </td>
                  <td className="py-4 px-4 text-app-text-color">
                    <div className="flex items-center gap-3">
                      <span
                        className={clsx(
                          "px-2 py-1 rounded text-xs font-medium",
                          sub.status === "active"
                            ? "bg-green-100 text-green-700"
                            : "bg-gray-100 text-gray-500"
                        )}
                      >
                        {sub.status.charAt(0).toUpperCase() +
                          sub.status.slice(1)}
                      </span>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SubscriptionList;
