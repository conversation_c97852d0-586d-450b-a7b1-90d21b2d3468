import { useState, useEffect } from "react";
import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { Field } from "@/types/types";
import { useAppStore } from "@/state-store/app-state-store";

const AddressInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id) as Field;
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);
  const [allowedAddressFields, setAllowedAddressFields] = useState(
    currentField?.allowedAddressFields || {
      country: true,
      state: true,
      city: true,
      pincode: true,
    }
  );

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setPlaceholder(currentField?.placeholder);
    setAllowedAddressFields(
      currentField?.allowedAddressFields || {
        country: true,
        state: true,
        city: true,
        pincode: true,
      }
    );
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired: isRequired,
      placeholder: placeholder,
      allowedAddressFields: allowedAddressFields,
    });
    setActiveComponent(null);
  };

  const handleAddressFieldChange = (
    type: keyof typeof allowedAddressFields,
    checked: boolean
  ) => {
    setAllowedAddressFields((prev) => ({
      ...prev,
      [type]: checked,
    }));
  };

  return (
    <SettingsCard
      title="Address Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      {/* Add settings form elements here */}
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>
        {/* Placeholder Input */}
        <input
          type="text"
          id="placeholder"
          className="border w-full p-2 mt-1 rounded bg-app-hero-background text-app-text-color border-app-border-primary"
          placeholder="Enter placeholder text"
          defaultValue={placeholder}
          onChange={(e) => setPlaceholder(e.target.value)}
        />

        {/* Address Field Selection */}
        <div className="mt-4">
          <p className="text-sm font-medium">Address fields</p>
          <div className="flex flex-col gap-2 mt-2">
            {(["country", "state", "city", "pincode"] as const).map((type) => (
              <label key={type} className="flex items-center justify-between">
                <span className="ml-2 capitalize text-sm">{type}</span>
                <Switch
                  onCheckedChange={(checked) =>
                    handleAddressFieldChange(type, checked)
                  }
                  checked={allowedAddressFields[type]}
                />
              </label>
            ))}
          </div>
        </div>
      </div>
    </SettingsCard>
  );
};

export default AddressInputSettings;
