import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>ci<PERSON>, Trash2, Check } from "lucide-react";
import {
  useGetWebhooks,
  useCreateWebhook,
  useUpdateWebhook,
  useDeleteWebhook,
} from "@/api-services/webhook";
import { useSearchParams } from "next/navigation";
import { QueryKeys } from "@/api-services/utils";
import { useQueryClient } from "@tanstack/react-query";

interface WebhooksDrawerProps {
  integrationId: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections: any[];
  onRefresh: () => void;
}

interface Webhook {
  id: number;
  created_at: string;
  form_id: string;
  webhook_url: string;
  status: boolean;
  workspace_id: number;
  integration_id: string;
}

const WebhooksDrawer: React.FC<WebhooksDrawerProps> = ({
  integrationId,
  isOpen,
  onClose,
  onRefresh,
}) => {
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [editingWebhook, setEditingWebhook] = useState<Webhook | null>(null);
  const [editingWebhookId, setEditingWebhookId] = useState<number | null>(null);
  const [editingWebhookUrl, setEditingWebhookUrl] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [webhookToDelete, setWebhookToDelete] = useState<Webhook | null>(null);

  const queryClient = useQueryClient();
  const {
    data: webhooksData,
    isLoading,
    refetch,
  } = useGetWebhooks(formId || "");
  const { mutate: createWebhook } = useCreateWebhook();
  const { mutate: updateWebhook } = useUpdateWebhook();
  const { mutate: deleteWebhook } = useDeleteWebhook();

  // Effect to refetch webhooks when drawer opens
  useEffect(() => {
    if (isOpen && formId) {
      refetch();
    }
  }, [isOpen, formId, refetch]);

  // Effect to invalidate queries after mutations
  useEffect(() => {
    if (webhooksData?.data) {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.WEBHOOKS(formId || ""),
      });
    }
  }, [webhooksData, formId, queryClient]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!webhookUrl || !formId) return;

    try {
      if (editingWebhook) {
        updateWebhook(
          {
            id: editingWebhook.id.toString(),
            data: {
              webhook_url: webhookUrl,
              status: true,
            },
          },
          {
            onSuccess: () => {
              setWebhookUrl("");
              setEditingWebhook(null);
              refetch();
              queryClient.invalidateQueries({
                queryKey: QueryKeys.WEBHOOKS(formId),
              });
              onRefresh();
            },
            onError: (error) => {
              console.error("Error updating webhook:", error);
            },
          }
        );
      } else {
        createWebhook(
          {
            form_id: formId,
            webhook_url: webhookUrl,
            integration_id: integrationId,
          },
          {
            onSuccess: () => {
              setWebhookUrl("");
              refetch();
              queryClient.invalidateQueries({
                queryKey: QueryKeys.WEBHOOKS(formId),
              });
              onRefresh();
            },
            onError: (error) => {
              console.error("Error creating webhook:", error);
            },
          }
        );
      }
    } catch (error) {
      console.error("Error saving webhook:", error);
    }
  };

  const handleEdit = (webhook: Webhook) => {
    setEditingWebhookId(webhook.id);
    setEditingWebhookUrl(webhook.webhook_url);
  };

  const handleEditSave = (webhook: Webhook) => {
    updateWebhook(
      {
        id: webhook.id.toString(),
        data: {
          webhook_url: editingWebhookUrl,
          status: true,
        },
      },
      {
        onSuccess: () => {
          setEditingWebhookId(null);
          setEditingWebhookUrl("");
          refetch();
          queryClient.invalidateQueries({
            queryKey: QueryKeys.WEBHOOKS(formId || ""),
          });
          onRefresh();
        },
        onError: (error) => {
          console.error("Error updating webhook:", error);
        },
      }
    );
  };

  const handleEditCancel = () => {
    setEditingWebhookId(null);
    setEditingWebhookUrl("");
  };

  const handleDeleteClick = (webhook: Webhook) => {
    setWebhookToDelete(webhook);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (webhookToDelete) {
      deleteWebhook(
        {
          id: webhookToDelete.id.toString(),
          data: {
            webhook_url: "",
            status: false,
          },
        },
        {
          onSuccess: () => {
            refetch();
            queryClient.invalidateQueries({
              queryKey: QueryKeys.WEBHOOKS(formId || ""),
            });
            onRefresh();
            setShowDeleteDialog(false);
            setWebhookToDelete(null);
          },
          onError: (error) => {
            console.error("Error deleting webhook:", error);
          },
        }
      );
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setWebhookToDelete(null);
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end"
    >
      <div
        className="bg-app-background w-full max-w-2xl h-full overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-4 border-b border-app-hero-background flex justify-between items-center">
          <h2 className="text-xl font-semibold text-app-text-color">
            Webhook Integration
          </h2>
          <button onClick={onClose} className="text-app-text-color">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-4">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-app-text-color mb-2">
                Add Webhook URL
              </label>
              <div className="flex gap-2">
                <input
                  type="url"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                  placeholder="Enter webhook URL"
                  className="flex-1 p-2 border border-app-hero-background rounded-md bg-app-background text-app-text-color"
                  required
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-app-text-color text-app-background rounded-md"
                >
                  {editingWebhook ? "Update" : "Add"}
                </button>
              </div>
            </div>
          </form>

          <div className="mt-6">
            <h3 className="text-lg font-medium text-app-text-color mb-4">
              Connected Webhooks
            </h3>
            {isLoading ? (
              <div className="text-center text-app-text-color">Loading...</div>
            ) : !webhooksData?.data?.length ? (
              <div className="text-center text-app-text-color">
                No webhooks connected
              </div>
            ) : (
              <div className="space-y-3">
                {webhooksData.data.map((webhook: Webhook) => (
                  <div
                    key={webhook.id}
                    className="flex items-center justify-between p-3 bg-app-hero-background rounded-md hover:bg-opacity-80 transition-all w-full"
                  >
                    <div className="flex flex-row items-center w-full gap-2">
                      <div className="flex-1 min-w-0">
                        {editingWebhookId === webhook.id ? (
                          <div className="flex items-center gap-2 w-full">
                            <input
                              type="url"
                              value={editingWebhookUrl}
                              onChange={(e) =>
                                setEditingWebhookUrl(e.target.value)
                              }
                              className="flex-1 p-2 border border-app-hero-background rounded-md bg-app-background text-app-text-color"
                              required
                              autoFocus
                            />
                            <button
                              onClick={() => handleEditSave(webhook)}
                              className="p-2 text-green-600 hover:bg-green-100 rounded transition-colors"
                              title="Save"
                            >
                              <Check className="w-5 h-5" />
                            </button>
                            <button
                              onClick={handleEditCancel}
                              className="p-2 text-gray-500 hover:bg-gray-200 rounded transition-colors"
                              title="Cancel"
                            >
                              <X className="w-5 h-5" />
                            </button>
                          </div>
                        ) : (
                          <>
                            <span className="text-app-text-color block truncate overflow-hidden text-ellipsis whitespace-nowrap max-w-full">
                              {webhook.webhook_url}
                            </span>
                            <span className="text-xs text-gray-400 mt-1 block">
                              Created:{" "}
                              {new Date(
                                webhook.created_at
                              ).toLocaleDateString()}
                            </span>
                          </>
                        )}
                      </div>
                      <div className="flex gap-2 flex-shrink-0">
                        {editingWebhookId !== webhook.id && (
                          <>
                            <button
                              onClick={() => handleEdit(webhook)}
                              className="p-2 text-app-text-color hover:text-blue-500 transition-colors"
                              title="Edit webhook"
                            >
                              <Pencil className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteClick(webhook)}
                              className="p-2 text-app-text-color hover:text-red-500 transition-colors"
                              title="Delete webhook"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
              onClick={handleDeleteCancel}
              aria-label="Close"
            >
              <X className="w-5 h-5" />
            </button>
            <h2 className="text-xl font-semibold mb-2">Are you sure?</h2>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete this webhook? This action cannot
              be undone.
            </p>
            <div className="flex gap-4 justify-end">
              <button
                onClick={handleDeleteCancel}
                className="px-4 py-2 rounded bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                No, Keep Webhook
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600"
              >
                Yes, Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebhooksDrawer;
