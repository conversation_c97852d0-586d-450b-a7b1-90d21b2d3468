import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import AddToFormDialog from "./AddToFormDialog";
import DeleteConfirmDialog from "./DeleteConfirmDialog";
import InviteMemberDialog from "./InviteMemberDialog";
import { useWorkspaceList } from "@/api-services/workspace";
import { toast } from "react-hot-toast";
import { useUserProfile } from "@/api-services/auth";
import { useGetAllRoleBasedOnWorkspace } from "@/api-services/role";
import AppPagination from "@/components/common/app-pagination";

interface WorkspaceMember {
  id: number;
  user_id: string;
  role: string;
  status: string;
  form_access: boolean;
  user_profile: {
    id: string;
    email: string;
    phone: number;
    last_name: string;
    first_name: string;
    profile_image: string | null;
  };
}

interface Member {
  id: string;
  fullName: string;
  email: string;
  role: string;
  status: "Active" | "Inactive";
  profile_image: string | null;
  form_access: boolean;
}

const AllMembersTable = () => {
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [isInviteOpen, setIsInviteOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const { data: profile } = useUserProfile();
  const workspaceId = profile?.data?.user?.workspace_id;

  // Fetch workspace members with ID
  const { data, isLoading, error } = useWorkspaceList(workspaceId);
  const { data: rolesData } = useGetAllRoleBasedOnWorkspace(workspaceId || "");

  // Transform API data to match our Member interface
  const allMembers: Member[] =
    data?.data?.members?.map((member: WorkspaceMember) => ({
      id: member.id.toString(),
      fullName: `${member.user_profile.first_name} ${member.user_profile.last_name}`,
      email: member.user_profile.email,
      role: member.role,
      status: member.status,
      profile_image: member.user_profile.profile_image,
      form_access: member.form_access,
    })) || [];

  // Apply pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const members = allMembers.slice(startIndex, startIndex + itemsPerPage);

  const handleDelete = () => {
    if (memberToDelete) {
      setMemberToDelete(null);
      toast.success("Member deleted successfully");
    }
  };

  const handleRoleChange = (memberId: string, newRole: string) => {
    // TODO: Implement role change logic
    toast.success(`Role updated to ${newRole}`);
  };

  if (isLoading) {
    return (
      <div className="w-full h-48 flex items-center justify-center">
        <div className="text-gray-500">Loading members...</div>
      </div>
    );
  }

  if (error) {
    console.error("Error details:", error);
    return (
      <div className="w-full h-48 flex items-center justify-center">
        <div className="text-red-500">
          Error loading members. Please try again later.
        </div>
      </div>
    );
  }

  if (!allMembers.length) {
    return (
      <div className="w-full h-48 flex items-center justify-center">
        <div className="text-gray-500">No members found.</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-medium">All members</h2>
            <span className="bg-app-text-color text-app-background px-2 py-0.5 rounded-full text-sm">
              {allMembers.length}
            </span>
          </div>
          <Button
            variant="outline"
            className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color rounded-xl"
            size="default"
            onClick={() => setIsInviteOpen(true)}
          >
            Invite member
          </Button>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-app-text-secondary">
                Full name
              </TableHead>
              <TableHead className="text-app-text-secondary">
                Email address
              </TableHead>
              <TableHead className="text-app-text-secondary">Role</TableHead>
              <TableHead className="text-app-text-secondary">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member) => (
              <TableRow key={member.id}>
                <TableCell className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gray-200 overflow-hidden">
                    {member.profile_image ? (
                      <img
                        src={member.profile_image}
                        alt={member.fullName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-500">
                        {member.fullName[0]}
                      </div>
                    )}
                  </div>
                  {member.fullName}
                </TableCell>
                <TableCell>{member.email}</TableCell>
                <TableCell>{member.role}</TableCell>
                <TableCell className="space-x-2">
                  {!member.form_access && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-blue-500 hover:text-blue-700"
                      onClick={() => setSelectedMember(member)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => setMemberToDelete(member)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <AppPagination
        currentPage={currentPage}
        totalItems={allMembers.length}
        itemsPerPage={itemsPerPage}
        onPageChange={setCurrentPage}
        showInfo={true}
      />

      <AddToFormDialog
        isOpen={!!selectedMember}
        onClose={() => setSelectedMember(null)}
        memberName={selectedMember?.fullName || ""}
        memberId={selectedMember?.id || ""}
      />

      <DeleteConfirmDialog
        isOpen={!!memberToDelete}
        onClose={() => setMemberToDelete(null)}
        onConfirm={handleDelete}
        memberName={memberToDelete?.fullName || ""}
      />

      <InviteMemberDialog
        isOpen={isInviteOpen}
        onClose={() => setIsInviteOpen(false)}
      />
    </div>
  );
};

export default AllMembersTable;
