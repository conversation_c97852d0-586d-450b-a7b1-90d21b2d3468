import { useQuery, useMutation } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1/google/sheet`;
const crmBaseEndpoint = `/v1/crm`;
const taskBaseEndpoint = `/v1/task`;

async function checkGoogleAuth(id: string, type: string): Promise<any> {
  if (!id) {
    throw new Error("Form ID is required");
  }
  const redirectUri = encodeURIComponent(
    process.env.NEXT_PUBLIC_REDIRECT_URL || "http://localhost:3000/playground"
  );
  return makeRequest({
    endpoint: `/auth/google?redirect_uri=${redirectUri}&integration_id=96e706d1-a0b6-491c-9eb2-fc97603edcab&formId=${id}&formType=${type}`,
    method: "GET",
  });
}

// Hook for Checking Google Authentication
const useCheckGoogleAuth = (id: string, type: string) => {
  return useQuery({
    queryKey: [QueryKeys.GOOGLE_SHEET_AUTH, id, type],
    queryFn: () => checkGoogleAuth(id, type),
    enabled: !!id,
    retry: false,
  });
};

// Create a New Sheet
async function createNewSheet({
  formId,
  credentialId,
  actionId,
}: {
  formId: string;
  credentialId: string;
  actionId: string;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/create`,
    method: "POST",
    data: { formId, credentialId, actionId },
  });
}

const useCreateNewSheet = () => {
  return useMutation({
    mutationFn: createNewSheet,
  });
};

// Link Form to Google Sheet
async function linkSheet({
  formId,
  credentialId,
  spreadsheetId,
  actionId,
  sheetId,
  column_mapped_data,
}: {
  formId: string;
  credentialId: string;
  spreadsheetId: string;
  actionId: string;
  sheetId: string;
  column_mapped_data?: {
    id: string;
    name: string;
    title: string;
    key: string;
  }[];
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/link`,
    method: "POST",
    data: {
      formId,
      credentialId,
      spreadsheetId,
      actionId,
      sheetId,
      column_mapped_data,
    },
  });
}

const useLinkSheet = () => {
  return useMutation({
    mutationFn: linkSheet,
  });
};

// Get Integration Actions
async function getIntegrationActions(integrationId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/action/${integrationId}`,
    method: "GET",
  });
}

const useGetIntegrationActions = (integrationId: string) => {
  return useQuery({
    queryKey: QueryKeys.GOOGLE_SHEET_ACTIONS(integrationId),
    queryFn: () => getIntegrationActions(integrationId),
  });
};

// Get Google Sheet Connections
async function getConnections(integrationId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/connection/${integrationId}`,
    method: "GET",
  });
}

const useGetConnections = (integrationId: string) => {
  return useQuery({
    queryKey: QueryKeys.GOOGLE_SHEET_CONNECTIONS(integrationId),
    queryFn: () => getConnections(integrationId),
  });
};

// Add New Connection
async function addConnection({
  integrationId,
  formId,
  formType,
  name,
  actionId,
}: {
  integrationId: string;
  formId: string;
  formType: string;
  name: string;
  actionId: string;
}) {
  const redirectUri =
    process.env.NEXT_PUBLIC_REDIRECT_URL || "https://automateforms.ai";

  const response = await makeRequest({
    endpoint: `${baseEndpoint}/addconnection?integration_id=${integrationId}&redirect_uri=${redirectUri}&formId=${formId}&formType=${formType}&name=${name}&action_id=${actionId}`,
    method: "GET",
  });
  console.log("addConnection response", response);
  if (response?.data?.authUrl) {
    window.location.href = response.data.authUrl;
  }

  return response;
}

const useAddConnection = () => {
  return useMutation({
    mutationFn: addConnection,
  });
};

// Add CRM Connection
async function addCRMConnection({
  integrationId,
  key,
  name,
}: {
  integrationId: string;
  key: string;
  name: string;
}) {
  return makeRequest({
    endpoint: `${crmBaseEndpoint}/addconnection`,
    method: "POST",
    data: { integration_id: integrationId, key, name },
  });
}

const useAddCRMConnection = () => {
  return useMutation({
    mutationFn: addCRMConnection,
  });
};

// Get CRM Connection Key
async function getCRMConnectionKey(connectionId: string) {
  return makeRequest({
    endpoint: `${crmBaseEndpoint}/getkey/${connectionId}`,
    method: "GET",
  });
}

const useGetCRMConnectionKey = (connectionId: string) => {
  return useQuery({
    queryKey: ["crm-connection-key", connectionId],
    queryFn: () => getCRMConnectionKey(connectionId),
    enabled: !!connectionId,
  });
};

async function linkCRMForm(data: any) {
  return makeRequest({
    endpoint: `${crmBaseEndpoint}/linkform`,
    method: "POST",
    data,
  });
}

const useLinkCRMForm = () => {
  return useMutation({
    mutationFn: linkCRMForm,
  });
};

async function updateCRMForm(data: any) {
  return makeRequest({
    endpoint: `${crmBaseEndpoint}/updateformintegration`,
    method: "PUT",
    data,
  });
}

const useUpdateCRMForm = () => {
  return useMutation({
    mutationFn: updateCRMForm,
  });
};

// Link Task Form
async function linkTaskForm(data: any) {
  return makeRequest({
    endpoint: `${taskBaseEndpoint}/link`,
    method: "POST",
    data,
  });
}

const useLinkTaskForm = () => {
  return useMutation({
    mutationFn: linkTaskForm,
  });
};

// Update Task Form Integration
async function updateTaskForm(data: any) {
  return makeRequest({
    endpoint: `${crmBaseEndpoint}/updateformintegration`,
    method: "PUT",
    data,
  });
}

const useUpdateTaskForm = () => {
  return useMutation({
    mutationFn: updateTaskForm,
  });
};

// Get Sheet Details
async function getSheetDetails({
  credentialId,
  spreadsheetId,
}: {
  credentialId: string;
  spreadsheetId: string;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/sheetdetails`,
    method: "POST",
    data: { credentialId, spreadsheetId },
  });
}

const useGetSheetDetails = () => {
  return useMutation({
    mutationFn: getSheetDetails,
  });
};

// Get Google Picker Configuration
async function getGooglePickerConfig(credentialId: string) {
  if (!credentialId) {
    throw new Error("Credential ID is required for Google Picker configuration");
  }

  try {
    const response = await makeRequest({
      endpoint: `${baseEndpoint}/picker/config`,
      method: "POST",
      data: { credentialId },
    });

    // Validate the response structure
    if (!response?.data?.data) {
      throw new Error("Invalid picker configuration response");
    }

    const { apiKey, token } = response.data.data;
    if (!apiKey || !token) {
      throw new Error("Missing required authentication data in picker config");
    }

    return response;
  } catch (error: any) {
    console.error("Error fetching Google Picker config:", error);
    throw new Error(error?.message || "Failed to fetch Google Picker configuration");
  }
}

const useGetGooglePickerConfig = () => {
  return useMutation({
    mutationFn: getGooglePickerConfig,
  });
};

export {
  useCheckGoogleAuth,
  useCreateNewSheet,
  useLinkSheet,
  useGetIntegrationActions,
  useGetConnections,
  useAddConnection,
  useAddCRMConnection,
  useGetCRMConnectionKey,
  useLinkCRMForm,
  useUpdateCRMForm,
  useLinkTaskForm,
  useUpdateTaskForm,
  useGetSheetDetails,
  useGetGooglePickerConfig,
};
