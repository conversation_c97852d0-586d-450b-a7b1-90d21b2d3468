'use client';
import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";

type ThankYouPayload = {
  thank_you_type: "redirect" | "custom" | "condition";
  thank_you_data: string | any;
  thank_you_url: string;
};

function updateThankyouPage(formId: string, data: ThankYouPayload) {
  return makeRequest({
    endpoint: "/v1/formsetting/thankyoupage/" + formId,
    method: "PUT",
    data,
  });
}

const useUpdateThankYouPage = () => {
  return useMutation({
    mutationFn: ({ formId, data }: { formId: string; data: ThankYouPayload }) =>
      updateThankyouPage(formId, data),
  });
};

function getThankYouPage(formId: string) {
  return makeRequest({
    endpoint: "/v1/formsetting/thankyoupage/" + formId,
    method: "GET",
  });
}

const useGetThankYouPage = (formId: string) => {
  return useQuery({
    queryKey: ["thankyoupage", formId],
    queryFn: () => getThankYouPage(formId),
  });
};

type FormSettingsPayload = {
  is_public: boolean;
  accept_responses: boolean;
  copy_allowed: boolean;
};

function updateFormSettings(formId: string, data: FormSettingsPayload) {
  return makeRequest({
    endpoint: `/v1/formsetting/${formId}`,
    method: "PUT",
    data,
  });
}

const useUpdateFormSettings = () => {
  return useMutation({
    mutationFn: ({ formId, data }: { formId: string; data: FormSettingsPayload }) =>
      updateFormSettings(formId, data),
  });
};

export { useUpdateThankYouPage, useGetThankYouPage, useUpdateFormSettings };
