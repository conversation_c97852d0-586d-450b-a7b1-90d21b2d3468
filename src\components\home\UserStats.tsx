import React from "react";
import { Co<PERSON>, FileText, HardDrive, Send, Star } from "lucide-react";
import { useGetUsageStats } from "@/api-services/usage";
import { useUserProfile } from "@/api-services/auth";
import { useAppStore } from "@/state-store/app-state-store";
import { useRouter } from "next/navigation";

const UserStats = () => {
  const router = useRouter();
  const { data: userData } = useUserProfile();
  const { user: globalUser } = useAppStore();
  const user = globalUser || userData?.data?.user;
  const workspaceId = user?.workspace_id;
  const { data: usageData, isLoading } = useGetUsageStats(workspaceId);
  
  // Check if user is admin
  const isAdmin = user?.custom_role === "admin";

  const handleUpgrade = () => {
    // Only allow navigation to billing if user IS admin
    if (isAdmin) {
      router.push("/billing");
    }
  };

  const convertMBtoGB = (mb: number) => {
    return (mb / 1024).toFixed(1);
  };

  const items = [
    // {
    //   icon: <FileText className="text-green-600 w-6 h-6" />,
    //   label: "Total Forms",
    //   value: isLoading
    //     ? "Loading..."
    //     : `${usageData?.data?.currentUsage.forms.used}`,
    // },
    {
      icon: <Send className="text-blue-500 w-6 h-6" />,
      label: "Total Submissions",
      value: isLoading
        ? "Loading..."
        : `${usageData?.data?.currentUsage.submissions.used}/${usageData?.data?.currentUsage.submissions.limit}`,
    },
    {
      icon: <HardDrive className="text-blue-600 w-6 h-6" />,
      label: "Storage",
      value: isLoading
        ? "Loading..."
        : `${usageData?.data?.currentUsage.storage.used || 0} MB/${
            usageData?.data?.currentUsage.storage.limit || 0
          } MB`,
      bar: true,
      percentage: parseFloat(
        usageData?.data?.currentUsage.storage.percentage || "0"
      ),
    },
    {
      icon: <Coins className="text-yellow-600 w-6 h-6" />,
      label: "AI Credits",
      value: isLoading
        ? "Loading..."
        : `${usageData?.data?.currentUsage.aiCredits.used}/${usageData?.data?.currentUsage.aiCredits.limit}`,
    },
    // {
    //   icon: <Star className="text-orange-500 w-6 h-6" />,
    //   label: "Days Remaining",
    //   value: isLoading
    //     ? "Loading..."
    //     : `${usageData?.data?.billingCycle.daysRemaining} Days`,
    //   upgrade: usageData?.data?.planType === "bronze",
    // },
  ];

  return (
    <div className="grid grid-cols-3 max-[1200px]:grid-cols-3 max-[992px]:grid-cols-3 max-[640px]:grid-cols-2 max-[380px]:grid-cols-1 items-center justify-center gap-3 bg-app-hero-background rounded-2xl p-4 ">
      {items.map((item, idx) => (
        <div
          key={idx}
          className="bg-app-background rounded-xl shadow-sm p-4 flex flex-col gap-2 justify-between min-h-[120px] max-w-72 justify-self-center self-center w-full"
        >
          <div className="flex items-center gap-2 justify-between">
            <span className="text-sm text-app-text-color font-medium">
              {item.label}
            </span>
            {item.icon}
          </div>
          <div className="text-lg font-semibold text-app-text-secondary">
            {item.value}
          </div>

          {item.bar && (
            <div className="h-2 w-full bg-app-hero-background rounded-full overflow-hidden">
              <div
                className="bg-app-text-color h-full transition-all duration-300"
                style={{ width: `${item.percentage}%` }}
              />
            </div>
          )}

          {/* {item.upgrade && isAdmin && (
            <button
              className="text-sm text-orange-500 font-medium hover:underline mt-1 w-fit"
              onClick={handleUpgrade}
            >
              Upgrade Now
            </button>
          )} */}
        </div>
      ))}
    </div>
  );
};

export default UserStats;
