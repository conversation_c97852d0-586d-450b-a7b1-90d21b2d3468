import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AlertCircle, Newspaper, Sparkles } from "lucide-react";
import { formatDate } from "@/utils/dateFormat";
import { usePathname, useRouter } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import { useCloneForm, useMoveFormToTrash } from "@/api-services/form";
import toast from "react-hot-toast";
import FormMenuPopover from "./formMenuPopover";
import FormDeleteModal from "./FormDeleteModal";
import ShareFormDialog from "./ShareFormDialog";
import { truncateTextByWords } from "@/utils/textFormat";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useQueryClient } from "@tanstack/react-query";
import { QueryKeys } from "@/api-services/utils";

const ListCard = ({
  params,
  form,
}: {
  params: { limit: number; offset: number };
  form: {
    id: string;
    title: string;
    created_at: string;
    updated_at: string;
    type: string;
    description: string;
    formheading: string;
    published: boolean;
    response_count: number;
    created_by_name?: string;
    p_image?: string;
    ai_creation?: boolean;
  };
}) => {
  // Early return if form is undefined or null
  if (!form) {
    return null;
  }

  const createdAt = formatDate(form?.created_at);
  const updatedAt = formatDate(form?.updated_at);
  const router = useRouter();
  const { setFormTitle, setFormDescription, addOrRemoveForm, selectedForms } =
    useAppStore();
  const { mutate: cloneForm, isPending } = useCloneForm(params);
  const { mutate: moveFormToTrash, isPending: isDeleting } = useMoveFormToTrash();
  const [isModalOpen, setModalOpen] = useState(false);
  const [isShareModalOpen, setShareModalOpen] = useState(false);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const queryClient = useQueryClient();

  const pathname = usePathname();
  const showMessage = pathname.includes("all-forms");

  const handleDelete = () => {
    moveFormToTrash(form?.id, {
      onSuccess: () => {
        toast.success("Form moved to trash successfully");
        setModalOpen(false);
        // Invalidate forms query to update the UI immediately
        queryClient.invalidateQueries({
          queryKey: QueryKeys.USER_FORMS(params.limit, params.offset),
        });
      },
      onError: () => {
        toast.error("Failed to move form to trash");
      },
    });
  };

  const handleAction = (action: string) => {
    switch (action) {
      case "edit":
        setFormTitle(form?.title || "Untitled Form");
        setFormDescription(
          form?.description || "Add your form description here"
        );
        router.push(`/playground?formId=${form?.id}&formType=${form?.type}`);
        break;
      case "delete":
        setModalOpen(true);
        break;
      case "duplicate":
        cloneForm(form?.id, {
          onSuccess: () => {
            toast.success("Form duplicated successfully");
          },
          onError: () => {
            toast.error("Failed to duplicate form");
          },
        });
        break;
      case "share":
        setShareModalOpen(true);
        break;
      case "responses":
        alert("Responses action triggered!");
        break;
      case "open":
        router.push(`/form/${form?.id}`);
        break;
      default:
        break;
    }
    setPopoverOpen(false);
  };

  const formTitle = form?.title || form?.formheading || "Untitled Form";
  const truncatedTitle = truncateTextByWords(formTitle, 5);

  return (
    <div className="w-full flex flex-row items-center justify-between px-4 py-2 rounded-2xl shadow-sm bg-app-background border border-transparent hover:border-emerald-300 transition-colors">
      {/* Title and icon */}
      {/* Prevent checkbox from triggering parent click */}
      <div className="flex flex-row items-center gap-4 flex-1">
        {showMessage && (
          <input
            type="checkbox"
            checked={!!selectedForms?.find((f) => f.id === form.id)}
            onClick={(e) => {
              e.stopPropagation();
              addOrRemoveForm(form);
            }}
          />
        )}
        <div
          className="flex flex-row items-center gap-4 cursor-pointer flex-[2]"
          onClick={() => handleAction("edit")}
        >
          <Newspaper className="text-app-text-color" />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <h3 className="text-left font-semibold text-app-text-color truncate max-w-[300px]">
                  {truncatedTitle}
                  {form.ai_creation && (
                    <Sparkles
                      className="inline-block ml-2 text-yellow-500"
                      aria-label="AI Generated"
                    />
                  )}
                </h3>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs whitespace-pre-line break-words p-3 shadow-lg bg-app-hero-background text-app-text-secondary border border-app-border-primary rounded-md">
                {formTitle}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Created by */}
      <div className="flex items-center gap-2 ml-4 flex-1 min-w-[120px] justify-center">
        <span className="text-xs text-app-text-secondary">Created by</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Avatar className="h-6 w-6 border border-gray-200">
                <AvatarImage src={form.p_image && form.p_image.trim() !== "" ? form.p_image : undefined} alt={form.created_by_name || 'Unknown'} />
                <AvatarFallback className="bg-gray-200 text-gray-600 uppercase text-sm">
                  {(form.created_by_name || 'U').trim().charAt(0)}
                </AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent className="p-2 bg-app-hero-background text-app-text-secondary">
              {(form.created_by_name || "Unknown").replace(
                /([a-z])([A-Z])/g,
                "$1 $2"
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Responses and actions */}
      <div className="flex items-center gap-4 flex-1 min-w-[120px] justify-end">
        <div className="text-app-text-secondary text-sm">
          Responses:{" "}
          <span className="text-app-text-color ">{form.response_count}</span>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="p-2 hover:bg-app-hero-background rounded-full cursor-pointer">
                <AlertCircle className="w-5 h-5 text-app-text-secondary" />
              </div>
            </TooltipTrigger>
            <TooltipContent className="p-3 bg-app-hero-background text-app-text-secondary">
              <p>Created at: {createdAt}</p>
              <p>Updated at: {updatedAt}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {/* Popover */}
        <FormMenuPopover
          form={form}
          handleAction={handleAction}
          popoverOpen={popoverOpen}
          setPopoverOpen={setPopoverOpen}
        />
      </div>

      <FormDeleteModal
        isOpen={isModalOpen}
        title="Are you sure?"
        message={form?.title}
        onClose={() => setModalOpen(false)}
        onConfirm={handleDelete}
      />

      <ShareFormDialog
        isOpen={isShareModalOpen}
        onClose={() => setShareModalOpen(false)}
        formId={form.id}
      />
    </div>
  );
};

export default ListCard;
