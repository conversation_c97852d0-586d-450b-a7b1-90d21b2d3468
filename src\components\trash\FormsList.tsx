import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { formatDate } from "@/utils/dateFormat";
import { truncateTextByWords } from "@/utils/textFormat";
import { AlertCircle, Newspaper } from "lucide-react";
import DeletePopover from "./DeletePopover";

interface FormItem {
  id: string;
  name: string;
  deletedBy: string;
  deletedOn: string;
  selected: boolean;
  deletedByImage?: string;
}

interface FormsListProps {
  forms: FormItem[];
  onSelect: (id: string) => void;
  onRestore: (formId: string) => void;
  onDelete: (formId: string) => void;
}

const FormsList: React.FC<FormsListProps> = ({ forms, onSelect, onRestore, onDelete }) => {
  return (
    <div className="space-y-2">
      {forms.map((form) => (
        <div
          key={form.id}
          className={`w-full flex flex-row items-center justify-between px-4 py-2 rounded-2xl shadow-sm bg-app-background border ${
            form.selected ? "border-app-border-primary" : ""
          }`}
        >
          {/* Checkbox and title */}
          <div className="flex flex-row items-center gap-4 flex-1">
            <input
              type="checkbox"
              checked={form.selected}
              onChange={() => onSelect(form.id)}
              className="accent-app-primary"
            />
            <div className="flex flex-row items-center gap-4 flex-[2]">
              <Newspaper className="text-app-text-color" />
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <h3 className="text-left font-semibold text-app-text-color truncate max-w-[300px] cursor-default">
                      {truncateTextByWords(form.name, 5)}
                    </h3>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs whitespace-pre-line break-words p-3 shadow-lg bg-app-hero-background text-app-text-secondary border border-app-border-primary rounded-md">
                    {form.name}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          {/* Deleted by */}
          {/* <div className="flex items-center gap-2 ml-4 flex-1 min-w-[120px] justify-center">
            <span className="text-xs text-app-text-secondary">Deleted by</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Avatar className="h-6 w-6 border border-gray-200">
                    <AvatarImage
                      src={form.deletedByImage}
                      alt={form.deletedBy || "Unknown"}
                    />
                    <AvatarFallback className="bg-gray-200 text-gray-600 uppercase text-sm">
                      {(form.deletedBy || "U").trim().charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                </TooltipTrigger>
                <TooltipContent className="p-2 bg-app-hero-background text-app-text-secondary">
                  {form.deletedBy || "Unknown"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div> */}
          {/* Deleted on */}
          <div className="flex items-center gap-4 flex-1 min-w-[120px] justify-end">
            {/* <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="p-2 hover:bg-app-hero-background rounded-full cursor-pointer">
                    <AlertCircle className="w-5 h-5 text-app-text-secondary" />
                  </div>
                </TooltipTrigger>
                <TooltipContent className="p-3 bg-app-hero-background text-app-text-secondary">
                  <p>Deleted at: {formatDate(form.deletedOn)}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider> */}
            <DeletePopover onDelete={() => onDelete(form.id)} onRestore={() => onRestore(form.id)} />
          </div>
        </div>
      ))}
    </div>
  );
};

export default FormsList;
