interface FormField {
  id: string;
  name: string;
  title?: string;
  type: string;
  component: string;
  isRequired?: boolean;
  isFirstNameRequired?: boolean;
  isLastNameRequired?: boolean;
  isHide?: boolean;
  allowedAddressFields?: {
    city?: boolean;
    state?: boolean;
    country?: boolean;
    pincode?: boolean;
  };
}

interface FormValues {
  [key: string]: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: {
    fieldId: string;
    fieldName: string;
    message: string;
  }[];
}

export const validateFormFields = (
  formFields: FormField[],
  formValues: FormValues
): ValidationResult => {
  const errors: { fieldId: string; fieldName: string; message: string }[] = [];

  formFields.forEach((field) => {
    // Skip validation if field is hidden
    if (field.isHide) {
      return; // Skip this field entirely if it's hidden
    }

    // Skip validation if field is not required
    if (
      !field.isRequired &&
      !field.isFirstNameRequired &&
      !field.isLastNameRequired
    )
      return;

    // Special validation for different field types
    switch (field.component) {
      case "NAME_INPUT":
        const firstName = formValues[`${field.id}_firstname`];
        const lastName = formValues[`${field.id}_lastname`];

        if (field.isFirstNameRequired && !firstName?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: "First name is required",
          });
        }
        if (field.isLastNameRequired && !lastName?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: "Last name is required",
          });
        }
        break;

      case "ADDRESS":
        // Get values from individual field keys (now stored directly in multipage forms)
        const address = formValues[`${field.id}_address`] || "";
        const state = formValues[`${field.id}_state`] || "";
        const city = formValues[`${field.id}_city`] || "";
        const country = formValues[`${field.id}_country`] || "";
        const pincode = formValues[`${field.id}_pincode`] || "";

        // If the field is required, validate all allowed fields
        if (field.isRequired) {
          // Always validate address
          if (!address || !address.trim()) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Address is required",
            });
          }
          // Validate other fields only if they are allowed
          if (field.allowedAddressFields?.city && (!city || !city.trim())) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "City is required",
            });
          }
          if (field.allowedAddressFields?.state && (!state || !state.trim())) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "State is required",
            });
          }
          if (
            field.allowedAddressFields?.country &&
            (!country || !country.trim())
          ) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Country is required",
            });
          }
          if (
            field.allowedAddressFields?.pincode &&
            (!pincode || !pincode.trim())
          ) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Pincode is required",
            });
          }
        }
        break;

      case "CHECKBOX":
      case "RADIO_BUTTON":
      case "DROPDOWN":
        const selectedValue =
          formValues[
            `${field.id}_${
              field.component === "RADIO_BUTTON"
                ? "radio"
                : field.component.toLowerCase()
            }`
          ];
        if (!selectedValue?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `Please select an option for ${field.name}`,
          });
        }
        break;

      case "UPLOAD":
        const uploadValue =
          formValues[`${field.id}_${field.component.toLowerCase()}`];
        if (!uploadValue || uploadValue.trim() === "") {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `Please upload a file for ${field.name}`,
          });
        }
        break;

      case "VOICE_NOTE":
        const fileValue = formValues[`${field.id}_voicenote`];
        if (!fileValue || fileValue.trim() === "") {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `Please upload a file for ${field.name}`,
          });
        }
        break;

      case "EMAIL":
        const email = formValues[`${field.id}_email`];
        if (!email?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        } else {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Please enter a valid email address",
            });
          }
        }
        break;

      case "PHONE_FIELD":
        const phone = formValues[`${field.id}_phone`];
        if (!phone?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        } else {
          // Remove all non-digit characters for length check
          const digitsOnly = phone.replace(/\D/g, "");

          // Check if total length exceeds 17 digits
          if (digitsOnly.length > 17) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Phone number cannot exceed 17 digits",
            });
            break;
          }

          // Remove country code and spaces for validation
          const phoneWithoutCountry = phone
            .replace(/^\+?\d{1,3}\s*/, "")
            .trim();

          // Check if phone number is empty after removing country code
          if (!phoneWithoutCountry) {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Please enter a valid phone number",
            });
          } else {
            // Validate phone number format and length (assuming 10 digits for Indian numbers)
            const phoneRegex = /^[\d\s-()]{9,}$/;
            if (!phoneRegex.test(phoneWithoutCountry)) {
              errors.push({
                fieldId: field.id,
                fieldName: field.name,
                message:
                  "Please enter a valid phone number with at least 10 digits",
              });
            }
          }
        }
        break;

      case "WEBSITE":
        const website = formValues[`${field.id}_website`];
        if (!website?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        } else {
          try {
            new URL(website);
          } catch {
            errors.push({
              fieldId: field.id,
              fieldName: field.name,
              message: "Please enter a valid website URL",
            });
          }
        }
        break;

      case "NUMBER":
        const number = formValues[`${field.id}_number`];
        if (!number?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        } else if (isNaN(Number(number))) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: "Please enter a valid number",
          });
        }
        break;

      case "TEXT_INPUT":
        const textValue = formValues[`${field.id}_text`];
        if (!textValue?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        }
        break;

      case "TEXT_AREA":
        const textareaValue = formValues[`${field.id}_textarea`];
        if (!textareaValue?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        }
        break;

      case "DATE":
        const dateValue = formValues[`${field.id}_date`];
        if (field.isRequired && !dateValue?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        }
        break;

      case "TIME":
        const timeValue = formValues[`${field.id}_time`];
        if (field.isRequired && !timeValue?.trim()) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.name} is required`,
          });
        }
        break;

      case "RATINGS":
        const ratingValue = formValues[`${field.id}_ratings`];
        if (field.isRequired && (!ratingValue?.trim() || ratingValue === "0")) {
          errors.push({
            fieldId: field.id,
            fieldName: field.name,
            message: `${field.title || field.name} is required`,
          });
        }
        break;
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};
