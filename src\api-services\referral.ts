import {  useQuery } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1/workspace`;
async function getReferralDetails() {
  return makeRequest({
    endpoint: `${baseEndpoint}/referral-details`,
    method: "GET",
  });
}

const useGetReferralDetails = () => {
  return useQuery({
    queryKey: [QueryKeys.REFERRAL],
    queryFn: () => getReferralDetails(),
  });
};

export { useGetReferralDetails };

