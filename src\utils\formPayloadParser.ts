function formPayloadParser(
  payload: Record<string, string>,
  formFields: any[] = []
) {
  // console.log("fileds", payload);
  const groupedFields = Object.entries(payload).reduce((acc, [key, value]) => {
    const [id, field] = key.split("_");

    if (!acc[id]) {
      acc[id] = { values: {}, id };
    }

    acc[id].values[field] = value;
    return acc;
  }, {} as Record<string, { id: string; values: Record<string, string> }>);

  return Object.values(groupedFields).map(({ id, values }) => {
    // Find the field definition to get its title
    const fieldDef = formFields.find((field) => field.id === id);
    const fieldTitle = fieldDef?.title || "Full Name";

    // Handle name fields
    if ("firstname" in values || "lastname" in values) {
      return {
        id,
        name: "name",
        title: fieldTitle,
        value: {
          firstName: values.firstname || "",
          lastName: values.lastname || "",
        },
        firstNameTitle: fieldDef?.firstNameTitle || "First Name",
        lastNameTitle: fieldDef?.lastNameTitle || "Last Name",
      };
    }

    // Handle phone fields
    if ("phone" in values) {
      return {
        id,
        name: "phone",
        title: fieldTitle,
        value: values.phone,
      };
    }

    // Handle email field
    if ("email" in values) {
      return {
        id,
        name: "email",
        title: fieldTitle,
        value: values.email,
      };
    }

    // Handle address fields
    if ("address" in values) {
      return {
        id,
        name: "address",
        title: fieldTitle,
        value: {
          address: values.address,
          state: values.state,
          country: values.country,
          city: values.city,
          pincode: values.pincode,
        },
      };
    }

    // Handle website fields
    if ("website" in values) {
      return {
        id,
        name: "website",
        title: fieldTitle,
        value: values.website,
      };
    }

    //Handle text fields
    if ("text" in values) {
      return {
        id,
        name: "text",
        title: fieldTitle,
        value: values.text,
      };
    }

    if ("textarea" in values) {
      return {
        id,
        name: "textarea",
        title: fieldTitle,
        value: values.textarea,
      };
    }

    if ("date" in values) {
      return {
        id,
        name: "date",
        title: fieldTitle,
        value: values.date,
      };
    }

    if ("time" in values) {
      return {
        id,
        name: "time",
        title: fieldTitle,
        value: values.time,
      };
    }

    if ("ratings" in values) {
      return {
        id,
        name: "ratings",
        title: fieldTitle,
        value: values.ratings,
      };
    }

    if ("signature" in values) {
      return {
        id,
        name: "signature",
        title: fieldTitle,
        value: values.signature,
      };
    }

    if ("radio" in values) {
      return {
        id,
        name: "radio",
        title: fieldTitle,
        value: values.radio,
      };
    }

    if ("checkbox" in values) {
      return {
        id,
        name: "checkbox",
        title: fieldTitle,
        value: values.checkbox,
      };
    }

    // Handle dropdown fields
    if ("dropdown" in values) {
      return {
        id,
        name: "dropdown",
        title: fieldTitle,
        value: values.dropdown,
      };
    }

    //Handle number fields
    if ("number" in values) {
      return {
        id,
        name: "number",
        title: fieldTitle,
        value: values.number,
      };
    }

    if ("upload" in values) {
      return {
        id,
        name: "upload",
        title: fieldTitle,
        value: values.upload,
      };
    }

    if ("voicenote" in values) {
      return {
        id,
        name: "voicenote",
        title: fieldTitle,
        value: values.voicenote,
      };
    }

    // Default case
    return {
      id,
      name: "Field",
      title: fieldTitle,
      value: Object.values(values)[0],
    };
  });
}

function formPayloadParserForMultiPage(
  fieldValues: Record<string, string>,
  fieldIds: (string | string[])[]
) {
  const values: Record<string, string> = {};
  fieldIds.forEach((mapId) => {
    if (typeof mapId === "string") {
      const [id, _] = mapId.split("_");
      values[mapId] = fieldValues?.[id] || "";
    }
    if (Array.isArray(mapId)) {
      const [id, _] = mapId?.[0]?.split("_");
      const value = fieldValues?.[id]?.split(" ");

      mapId.forEach((id, index) => {
        values[id] = value?.[index] || "";
      });
    }
  });

  return values;
}

export { formPayloadParser, formPayloadParserForMultiPage };
