"use client";
import React, { useState } from "react";
import {
  useGetWalletBalance,
  useGetWalletTransactions,
} from "@/api-services/wallet";
import { useUserProfile } from "@/api-services/auth";
import { useAppStore } from "@/state-store/app-state-store";
import WalletBalanceCard from "@/components/billing/WalletBalanceCard";
import AppPagination from "@/components/common/app-pagination";

const PAGE_SIZE = 9;

const statusColor = {
  failed: "bg-red-100 text-red-500",
  successful: "bg-green-100 text-green-600",
  pending: "bg-yellow-100 text-yellow-600",
};

const WalletPage = () => {
  const [page, setPage] = useState(1);
  const {
    data: userData,
    isLoading: userLoading,
    isError: userError,
  } = useUserProfile();
  const { user: globalUser } = useAppStore();
  const user = globalUser || userData?.data?.user;
  const workspaceId = user?.workspace_id;
  const { data, isLoading } = useGetWalletTransactions(
    workspaceId,
    page,
    PAGE_SIZE
  );
  const transactions = data?.data?.transactions || [];
  const total = data?.data?.total_count || 0;
  const {
    data: walletData,
    isLoading: walletLoading,
    isError: walletError,
  } = useGetWalletBalance(workspaceId);
  const balance = walletData?.data?.balance ?? 0;

  // Dummy values for header cards
  const totalDeposit = 10000;
  const totalDeduction = 5000;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const capitalizeFirstLetter = (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1);
  };

  return (
    <div className="py-4">
      {/* Header Cards */}
      <div className="grid grid-cols-3 max-[768px]:grid-cols-2 max-[480px]:grid-cols-1 gap-4 mb-6">
        <WalletBalanceCard />
        {/* <div className="bg-white rounded-lg p-4 flex flex-col items-start ">
          <div className="flex items-center mb-2">
            <span className="font-semibold text-gray-700 mr-2">
              Total Deposit
            </span>
            <span role="img" aria-label="deposit">
              🪙
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ₹ {totalDeposit.toLocaleString()}
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 flex flex-col items-start ">
          <div className="flex items-center mb-2">
            <span className="font-semibold text-gray-700 mr-2">
              Total Deduction
            </span>
            <span role="img" aria-label="deduct">
              ➖
            </span>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ₹ {totalDeduction.toLocaleString()}
          </div>
        </div> */}
      </div>

      {/* Transactions Table */}
      <div className="bg-app-background rounded-lg shadow p-4">
        <table className="w-full text-left">
          <thead>
            <tr className="border-b text-app-text-color">
              <th className="py-2 px-2">Created At</th>
              <th className="py-2 px-2">Type</th>
              <th className="py-2 px-2">Status</th>
              <th className="py-2 px-2">Amount</th>
              <th className="py-2 px-2">Remarks</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td
                  colSpan={5}
                  className="text-center py-8 text-app-text-secondary"
                >
                  Loading...
                </td>
              </tr>
            ) : transactions.length === 0 ? (
              <tr>
                <td
                  colSpan={5}
                  className="text-center py-8 text-app-text-secondary"
                >
                  No transactions found.
                </td>
              </tr>
            ) : (
              transactions.map((txn: any, idx: number) => (
                <tr
                  key={idx}
                  className="border-b last:border-0 text-app-text-secondary"
                >
                  <td className="py-2 px-2">{formatDate(txn.created_at)}</td>
                  <td className="py-2 px-2 uppercase">{txn.type}</td>
                  <td className="py-2 px-2">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-semibold ${
                        statusColor[txn.status as keyof typeof statusColor] ||
                        "bg-gray-100 text-gray-500"
                      }`}
                    >
                      {capitalizeFirstLetter(txn.status)}
                    </span>
                  </td>
                  <td className="py-2 px-2">₹ {txn.amount.toLocaleString()}</td>
                  <td className="py-2 px-2">{txn.remarks}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        {/* Pagination */}
        <AppPagination
          currentPage={page}
          totalItems={total}
          itemsPerPage={PAGE_SIZE}
          onPageChange={setPage}
          showInfo={true}
        />
      </div>
    </div>
  );
};

export default WalletPage;
