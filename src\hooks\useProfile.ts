import {
  useUpdateProfile,
  useUploadImage,
  useUserProfile,
  useRemoveProfileImage,
} from "@/api-services/auth";
import { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useAppStore } from "@/state-store/app-state-store";
import { useTimezones, Timezone } from "@/api-services/timezone";

interface ProfileFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  timezone: string;
  language: string;
  profile_image: string;
}

export const useProfile = () => {
  const { data: userData, isLoading, isError, refetch } = useUserProfile();

  const user = userData?.data?.user;
  const { setUser } = useAppStore();

  // Fetch timezones using the custom hook
  const { data: timezones, isLoading: isLoadingTimezones } = useTimezones();

  const [avatar, setAvatar] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteEmail, setDeleteEmail] = useState("");
  const [userEmail, setUserEmail] = useState("");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { mutate: uploadImage, isPending: isUploading } = useUploadImage();
  const { mutate: updateProfile, isPending: isUpdating } = useUpdateProfile();
  const { mutate: removeProfileImage, isPending: isRemoving } =
    useRemoveProfileImage();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    getValues,
    formState: { errors, isDirty },
  } = useForm<ProfileFormData>({
    mode: "onBlur",
  });

  useEffect(() => {
    if (user) {
      setValue("first_name", user.first_name);
      setValue("last_name", user.last_name);
      setValue("email", user.email);
      setValue("phone", user.phone || "");
      setValue("timezone", user.timezone || "Asia/Kathmandu");
      setValue("language", user.language || "English");
      setValue("profile_image", user.profile_image || "");
      setAvatar(user.profile_image || null);
      setUserEmail(user.email || "");
      setUser(user);
    }
  }, [user, setValue, setUser]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    const formData = new FormData();
    formData.append("profilePic", file);

    uploadImage(formData, {
      onSuccess: (res) => {
        const imageUrl = res?.data?.imageUrl;
        if (imageUrl) {
          setAvatar(imageUrl);

          const updatedUser = { ...user, profile_image: imageUrl };
          setUser(updatedUser);
          refetch();
          toast.success("Profile picture updated successfully");
        } else {
          toast.error("No image URL returned from server");
          setAvatar(user?.profile_image || null);
        }
      },
      onError: () => {
        toast.error("Failed to upload image");
        setAvatar(user?.profile_image || null);
      },
    });
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      fileInputRef.current.click();
    }
  };

  const onSubmit = (data: ProfileFormData) => {
    const updateData = { ...data };
    // delete updateData.profile_image;

    // if (avatar) {
    //   updateData.profile_image = avatar || data.profile_image;
    // }

    updateProfile(updateData, {
      onSuccess: () => {
        const updatedUser = {
          ...user,
          firstName: data.first_name,
          lastName: data.last_name,
          email: data.email,
          phone: data.phone,
          timeZone: data.timezone,
          language: data.language,
          // profile_image: updateData.profile_image,
        };
        setUser(updatedUser);
        refetch();
        setUserEmail(data.email);
        toast.success("Profile updated successfully");
      },
      onError: () => {
        toast.error("Error updating profile");
      },
    });
  };

  const toggleDeleteDialog = () => {
    setShowDeleteDialog((prev) => !prev);
  };

  const handleDeleteAccount = () => {
    if (deleteEmail === userEmail) {
      console.log(`Account deleted for email: ${deleteEmail}`);
      // Perform delete logic here
      setShowDeleteDialog(false);
    } else {
      alert("Email does not match. Cannot delete account.");
    }
  };

  const handleRemoveProfileImage = () => {
    removeProfileImage(undefined, {
      onSuccess: () => {
        // Update local state
        setAvatar(null);

        // Update global user state
        if (user) {
          const updatedUser = { ...user, profile_image: null };
          setUser(updatedUser);
          refetch();
        }

        toast.success("Profile picture removed successfully");
      },
      onError: () => {
        toast.error("Failed to remove profile picture");
      },
    });
  };

  return {
    register,
    handleSubmit,
    setValue,
    errors,
    avatar,
    fileInputRef,
    deleteEmail,
    userEmail,
    setDeleteEmail,
    showDeleteDialog,
    toggleDeleteDialog,
    triggerFileInput,
    handleFileUpload,
    handleDeleteAccount,
    handleRemoveProfileImage,
    onSubmit,
    user,
    isUploading,
    isUpdating,
    isRemoving,
    isLoading,
    isError,
    watch,
    isDirty,
    getValues,
    timezones,
    isLoadingTimezones,
  };
};
