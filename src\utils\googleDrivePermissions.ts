// Google Drive Permissions Helper
export const GOOGLE_DRIVE_SCOPES = [
  'https://www.googleapis.com/auth/drive.readonly',
  'https://www.googleapis.com/auth/drive.file',
  'https://www.googleapis.com/auth/spreadsheets',
  'https://www.googleapis.com/auth/drive.metadata.readonly'
];

export interface GoogleDrivePermissionCheck {
  hasPermission: boolean;
  missingScopes: string[];
  error?: string;
}

/**
 * Check if the current token has required Google Drive permissions
 */
export async function checkGoogleDrivePermissions(token: string): Promise<GoogleDrivePermissionCheck> {
  try {
    // Check token info
    const tokenInfoResponse = await fetch(`https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${token}`);
    const tokenInfo = await tokenInfoResponse.json();

    if (!tokenInfoResponse.ok) {
      return {
        hasPermission: false,
        missingScopes: GOOGLE_DRIVE_SCOPES,
        error: 'Invalid or expired token'
      };
    }

    const grantedScopes = tokenInfo.scope?.split(' ') || [];
    const missingScopes = GOOGLE_DRIVE_SCOPES.filter(scope => !grantedScopes.includes(scope));

    return {
      hasPermission: missingScopes.length === 0,
      missingScopes,
      error: missingScopes.length > 0 ? 'Missing required scopes' : undefined
    };
  } catch (error) {
    return {
      hasPermission: false,
      missingScopes: GOOGLE_DRIVE_SCOPES,
      error: 'Failed to check permissions'
    };
  }
}

/**
 * Test Google Drive API access
 */
export async function testGoogleDriveAccess(token: string): Promise<boolean> {
  try {
    const response = await fetch('https://www.googleapis.com/drive/v3/files?pageSize=1', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.ok;
  } catch (error) {
    console.error('Google Drive access test failed:', error);
    return false;
  }
}

/**
 * Get user-friendly error message for permission issues
 */
export function getPermissionErrorMessage(permissionCheck: GoogleDrivePermissionCheck): string {
  if (permissionCheck.hasPermission) {
    return '';
  }

  if (permissionCheck.error === 'Invalid or expired token') {
    return 'आपका Google authentication expire हो गया है। कृपया दोबारा login करें।';
  }

  if (permissionCheck.missingScopes.length > 0) {
    return 'Google Drive access के लिए additional permissions की जरूरत है। कृपया दोबारा authorize करें।';
  }

  return 'Google Drive access में समस्या है। कृपया support team से संपर्क करें।';
}

/**
 * Generate authorization URL with proper scopes
 */
export function generateAuthUrl(clientId: string, redirectUri: string): string {
  const params = new URLSearchParams({
    client_id: clientId,
    redirect_uri: redirectUri,
    response_type: 'code',
    scope: GOOGLE_DRIVE_SCOPES.join(' '),
    access_type: 'offline',
    prompt: 'consent'
  });

  return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
}

/**
 * Validate Google Picker configuration
 */
export interface PickerConfigValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export function validatePickerConfig(config: any): PickerConfigValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!config?.apiKey) {
    errors.push('API Key missing');
  }

  if (!config?.token) {
    errors.push('OAuth token missing');
  }

  if (!config?.clientId) {
    warnings.push('Client ID missing - some features may not work');
  }

  // Check if token looks valid (basic format check)
  if (config?.token && !config.token.startsWith('ya29.')) {
    warnings.push('Token format looks unusual');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Debug Google Drive permissions
 */
export async function debugGoogleDrivePermissions(token: string, apiKey: string) {
  console.group('🔍 Google Drive Permissions Debug');
  
  try {
    // Check token permissions
    const permissionCheck = await checkGoogleDrivePermissions(token);
    console.log('Permission Check:', permissionCheck);

    // Test Drive API access
    const driveAccess = await testGoogleDriveAccess(token);
    console.log('Drive API Access:', driveAccess);

    // Check API key
    console.log('API Key provided:', !!apiKey);
    console.log('API Key format:', apiKey?.substring(0, 10) + '...');

    // Check browser environment
    console.log('GAPI loaded:', !!window.gapi);
    console.log('Picker loaded:', !!window.google?.picker);
    console.log('Origin:', window.location.origin);

  } catch (error) {
    console.error('Debug failed:', error);
  } finally {
    console.groupEnd();
  }
}
