const functionsBaseUrl = process.env.NEXT_PUBLIC_FUNCTIONS_URL;

export const fetchPipelines = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getPipelines`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch pipelines");
    }

    const data = await response.json();
    return {
      data: {
        data: data, // The response is already in the correct format with id and name
      },
    };
  } catch (error) {
    console.error("Error fetching pipelines:", error);
    throw error;
  }
};

export const fetchCrmLeadSources = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getcrmleadsource`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch lead sources");
    }

    const data = await response.json();
    // Transform the data to include id (using source_name as id) and name
    const transformedData = data.map((source: { source_name: string }) => ({
      id: source.source_name,
      name: source.source_name,
    }));

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching lead sources:", error);
    throw error;
  }
};

export const fetchSalesPersons = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getsalesperson`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch sales persons");
    }

    const data = await response.json();
    // Transform the data to include id, user_id, and name
    const transformedData = data.map(
      (person: {
        user_name: string;
        user_email: string;
        id: number;
        user_id: string;
      }) => ({
        id: person.id.toString(), // Use id for assignedTo
        user_id: person.user_id, // Use user_id for createdBy
        name: person.user_name,
        email: person.user_email,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching sales persons:", error);
    throw error;
  }
};

export const fetchCrmStages = async (apiKey: string, pipelineId: string) => {
  try {
    const response = await fetch(
      `${functionsBaseUrl}/v1/getCrmStages?pipeline_id=${pipelineId}`,
      {
        method: "GET",
        headers: {
          "API-Key": apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch stages");
    }

    const data = await response.json();
    return {
      data: {
        data: data, // Assuming the response is already in the correct format
      },
    };
  } catch (error) {
    console.error("Error fetching stages:", error);
    throw error;
  }
};

export const fetchTaskUsers = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getTaskUsers`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch task users");
    }

    const data = await response.json();
    // Transform the data to include id and first_name
    const transformedData = data.map(
      (user: { id: string; full_name: string }) => ({
        id: user.id,
        full_name: user.full_name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching task users:", error);
    throw error;
  }
};

export const fetchTaskCategories = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getTaskCategories`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch task categories");
    }

    const data = await response.json();
    // Transform the data to include id and name
    const transformedData = data.map(
      (category: { id: string; name: string }) => ({
        id: category.id,
        name: category.name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching task categories:", error);
    throw error;
  }
};

export const fetchCrmCustomFields = async (
  apiKey: string,
  pipelineId: string
) => {
  try {
    const response = await fetch(
      `${functionsBaseUrl}/v1/getleadscustomfields?pipeline_id=${pipelineId}`,
      {
        method: "GET",
        headers: {
          "API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch custom fields: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching custom fields:", error);
    throw error;
  }
};

export const fetchContactCustomFields = async (apiKey: string) => {
  try {
    const response = await fetch(
      `${functionsBaseUrl}/v1/getcontactcustomfields`,
      {
        method: "GET",
        headers: {
          "API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch contact custom fields: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching contact custom fields:", error);
    throw error;
  }
};

export const fetchCountries = async () => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/get_countries`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch countries: ${response.status}`);
    }

    const data = await response.json();
    // Transform the data to include id (using code as id) and name
    const transformedData = data.map(
      (country: { name: string; code: string }) => ({
        id: country.code,
        name: country.name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching countries:", error);
    throw error;
  }
};

export const fetchProductCategories = async (apiKey: string) => {
  try {
    const response = await fetch(
      `${functionsBaseUrl}/v1/get-product-categories`,
      {
        method: "GET",
        headers: {
          "API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch product categories: ${response.status}`);
    }

    const data = await response.json();
    // Transform the data to include id and name
    const transformedData = data.map(
      (category: { id: number; name: string }) => ({
        id: category.id.toString(),
        name: category.name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching product categories:", error);
    throw error;
  }
};

export const fetchProductUnits = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/get-product-unit`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch product units: ${response.status}`);
    }

    const data = await response.json();
    // Transform the data to include id and name
    const transformedData = data.map((unit: { id: number; unit: string }) => ({
      id: unit.id.toString(),
      name: unit.unit,
    }));

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching product units:", error);
    throw error;
  }
};

export const fetchProductCustomFields = async (apiKey: string) => {
  try {
    const response = await fetch(
      `${functionsBaseUrl}/v1/get-product-custom-fields`,
      {
        method: "GET",
        headers: {
          "API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch product custom fields: ${response.status}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching product custom fields:", error);
    throw error;
  }
};
