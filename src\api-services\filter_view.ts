import { makeRequest } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/filterview`;

// Get all views for a form
function getFilterView(formId: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${formId}`,
        method: "GET"
    })
}

const useGetFilterView = (formId: string) => {
    return useQuery({
        queryKey: ['filter-view', formId],
        queryFn: () => getFilterView(formId)
    })
}

// Create view (POST)
function createFilterView(data: any) {
    return makeRequest({
        endpoint: `${baseEndpoint}`,
        method: "POST",
        data
    })
}

const useCreateFilterView = () => {
    return useMutation({
        mutationFn: (data: any) => createFilterView(data)
    })
}

// Update view (PUT /:id)
function updateFilterView(id: string, data: any) {  
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "PUT",
        data
    })
}

const useUpdateFilterView = () => {
    return useMutation({
        mutationFn: ({ id, data }: { id: string, data: any }) => updateFilterView(id, data)
    })
}

// Delete view (DELETE /:id)
function deleteFilterView(id: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "DELETE"
    })
}

const useDeleteFilterView = () => {
    return useMutation({
        mutationFn: (id: string) => deleteFilterView(id)
    })
}

export { useGetFilterView, useCreateFilterView, useUpdateFilterView, useDeleteFilterView };
