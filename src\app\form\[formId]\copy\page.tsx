"use client";
export const runtime = "edge";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { makeRequest } from "@/api-services/utils";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import FormWrapper from "@/provider/FormWrapper";
import { ThemeProvider } from "@/app/theme-provider";
import { Loader } from "lucide-react";
import { useFormsPublic } from "@/api-services/form";

type Props = {
  params: { formId: string };
};

export default function CopyFormPage({ params }: Props) {
  const { formId } = params;
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingSession, setIsCheckingSession] = useState(true);
  const [showNotAllowedMessage, setShowNotAllowedMessage] = useState(false);

  const {
    data: responseData,
    isLoading: isFormLoading,
    error,
  } = useFormsPublic(formId);

  useEffect(() => {
    // Check session on component mount
    checkSession();
  }, []);

  useEffect(() => {
    if (responseData) {
      const copyAllowed =
        responseData?.data?.form?.automate_form_settings?.[0]?.copy_allowed;
      if (copyAllowed === false) {
        setShowNotAllowedMessage(true);
      }
    }
  }, [responseData]);

  const checkSession = async () => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setIsCheckingSession(false);
    } catch (error) {
      console.error("Session check error:", error);
      setIsCheckingSession(false);
    }
  };

  const handleAddToWorkspace = async () => {
    setIsLoading(true);
    try {
      // Check if user has a valid session
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        // No session, redirect to login
        router.push("/login");
        return;
      }

      // Session exists, call the API to add form to workspace
      const result = await makeRequest({
        endpoint: `/v1/forms/copy-to-workspace/${formId}`,
        method: "GET",
      });

      if (result?.success || result?.data) {
        // Successfully added to workspace, redirect to home
        router.push("/home");
      } else {
        // Handle error case
        console.error("Failed to add form to workspace");
        router.push("/home");
      }
    } catch (error) {
      console.error("Error adding form to workspace:", error);
      router.push("/home");
    } finally {
      setIsLoading(false);
    }
  };

  if (isCheckingSession || isFormLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader className="animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  if (showNotAllowedMessage) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-8 bg-white dark:bg-app-background rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-red-600 mb-4">
            Copying Not Allowed
          </h2>
          <p className="text-lg">
            This form is not enabled to be copied to your workspace. Please
            contact the owner of the form.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="bg-white dark:bg-app-background border-b border-gray-200 dark:border-app-border-primary px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="cursor-pointer">
              <Image
                src="/logo.png"
                alt="Automate Forms Logo"
                height={100}
                width={100}
                quality={100}
                className="h-8 w-auto"
              />
            </Link>
          </div>

          {/* Add to Workspace Button */}
          <Button
            onClick={handleAddToWorkspace}
            disabled={isLoading}
            className="bg-app-text-color hover:bg-app-hero-background text-app-background hover:text-app-text-color border borer-app-border-primary px-6 py-2 rounded-lg font-medium"
          >
            {isLoading ? (
              <>
                <Loader className="animate-spin mr-2 h-4 w-4" />
                Adding...
              </>
            ) : (
              "Add form to workspace"
            )}
          </Button>
        </div>
      </header>

      {/* Embedded Form */}
      <div className="flex-1 overflow-auto">
        <ThemeProvider>
          <FormWrapper formId={formId} />
        </ThemeProvider>
      </div>
    </div>
  );
}
