import { useGetRoleBasedOnWorkspace } from "@/api-services/auth";
import { useGetPermission } from "@/api-services/permission";

export const usePermission = () => {
  const { data: rolesData, isLoading: isLoadingRoles } = useGetRoleBasedOnWorkspace();
  const { data: permissionData, isLoading: isLoadingPermission } = useGetPermission(rolesData?.data?.role_id);

  const permission = {
    create_form: permissionData?.data?.data?.form?.CREATE_FORM?.is_assigned ?? false,
    edit_form: permissionData?.data?.data?.form?.EDIT_FORM?.is_assigned ?? false,
    delete_form: permissionData?.data?.data?.form?.DELETE_FORM?.is_assigned ?? false,
    view_form_submission: permissionData?.data?.data?.form?.VIEW_FORM_SUNMISSION?.is_assigned ?? false,
    export_form_data: permissionData?.data?.data?.form?.EXPORT_FROM_DATA?.is_assigned ?? false,
    integration: permissionData?.data?.data?.form?.FORM_INTEGRATION?.is_assigned ?? false,
    create_folder: permissionData?.data?.data?.folder?.CREATE_FOLDER?.is_assigned ?? false,
    edit_folder: permissionData?.data?.data?.folder?.EDIT_FOLDER?.is_assigned ?? false,
    delete_folder: permissionData?.data?.data?.folder?.DELETE_FOLDER?.is_assigned ?? false,
    view_folder: permissionData?.data?.data?.folder?.VIEW_FOLDER?.is_assigned ?? false,
  };
  

  const PermissionProtected = ({children,permissionKey}:{children:React.ReactNode,permissionKey:keyof typeof permission}) =>{
    if(isLoadingRoles || isLoadingPermission){
        return <div>Loading...</div>;
    }
    if(permission[permissionKey]){
        return children;
    }
    return null;
  }
    

    return {
        permission,
        PermissionProtected
    };
}
