import React, { useState } from "react";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  <PERSON>ert<PERSON>ircle,
  BarChart,
  Edit,
  EllipsisVertical,
  File,
  Files,
  MessageSquare,
  Newspaper,
  RefreshCcw,
  Trash,
  UploadCloud,
  User,
  Sparkles,
} from "lucide-react";
import { formatDate } from "@/utils/dateFormat";
import { useRouter } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import { useCloneForm, useMoveFormToTrash } from "@/api-services/form";
import toast from "react-hot-toast";
import FormMenuPopover from "./formMenuPopover";
import FormDeleteModal from "./FormDeleteModal";
import ShareFormDialog from "./ShareFormDialog";
import { useQueryClient } from "@tanstack/react-query";
import { QueryKeys } from "@/api-services/utils";
import { truncateTextByWords } from "@/utils/textFormat";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface Form {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  type: string;
  description: string;
  formheading: string;
  published: boolean;
  response_count: number;
  created_by_name?: string;
  p_image?: string;
  ai_creation?: boolean;
}

const BorderCard = ({ params, form }: { params: {limit: number, offset: number}, form: Form }) => {
  // Early return if form is undefined or null
  if (!form) {
    return null;
  }

  const createdAt = formatDate(form?.created_at);
  const updatedAt = formatDate(form?.updated_at);
  const router = useRouter();
  const { setFormTitle, setFormDescription } = useAppStore();
  const { mutate: cloneForm, isPending } = useCloneForm(params);
  const { mutate: moveFormToTrash, isPending: isDeleting } = useMoveFormToTrash();
  const [isModalOpen, setModalOpen] = useState(false);
  const [isShareModalOpen, setShareModalOpen] = useState(false);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [imgError, setImgError] = useState(false);
  const queryClient = useQueryClient();

  const formTitle = form?.title || form?.formheading || "Untitled Form";

  const handleDelete = () => {
    moveFormToTrash(form?.id, {
      onSuccess: () => {
        toast.success("Form moved to trash successfully");
        setModalOpen(false);
        queryClient.invalidateQueries({
          queryKey: QueryKeys.USER_FORMS(params.limit, params.offset),
        });
      },
      onError: () => {
        toast.error("Failed to move form to trash");
      },
    });
  };

  const handleAction = (action: string) => {
    switch (action) {
      case "edit":
        setFormTitle(form?.title || "Untitled Form");
        setFormDescription(
          form?.description || "Add your form description here"
        );
        router.push(`/playground?formId=${form?.id}&formType=${form?.type}`);
        break;
      case "delete":
        setModalOpen(true);
        break;
      case "duplicate":
        cloneForm(form?.id, {
          onSuccess: () => {
            toast.success("Form duplicated successfully");
          },
          onError: () => {
            toast.error("Failed to duplicate form");
          },
        });
        break;
      case "share":
        setShareModalOpen(true);
        break;
      case "publish":
        alert("Publish action triggered!");
        break;
      case "responses":
        alert("Responses action triggered!");
        break;
      case "open":
        router.push(`/form/${form?.id}`);
        break;
      default:
        break;
    }
    setPopoverOpen(false);
  };

  return (
    <div
      className={cn(
        "relative flex flex-col gap-4 w-full bg-app-background dark:bg-app-main-background rounded-2xl shadow-sm border border-gray-100 dark:border-gray-800 hover:border-emerald-300 dark:hover:border-emerald-300 transition-colors p-4 cursor-pointer",
        popoverOpen && "z-10"
      )}
      onClick={() => handleAction("open")}
    >
      <div className="flex justify-between items-start gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div
                className="font-semibold text-app-text-color text-lg cursor-pointer flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction("edit");
                }}
              >
                <h3 className="line-clamp-2 leading-tight">{formTitle}</h3>
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs whitespace-pre-line break-words p-3 shadow-lg bg-app-hero-background text-app-text-secondary border border-app-border-primary rounded-md">
              <p>{formTitle}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="flex items-center gap-2">
          {form.ai_creation && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Sparkles
                    className="text-yellow-500 flex-shrink-0"
                    aria-label="AI Generated"
                  />
                </TooltipTrigger>
                <TooltipContent className="max-w-xs whitespace-pre-line break-words p-3 shadow-lg bg-app-hero-background text-app-text-secondary border border-app-border-primary rounded-md">
                  <p>This form was generated by AI</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <FormMenuPopover
            form={form}
            handleAction={handleAction}
            popoverOpen={popoverOpen}
            setPopoverOpen={setPopoverOpen}
          />
        </div>
      </div>

      <div className="flex items-center justify-center gap-3 text-sm text-app-text-secondary mt-auto">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Avatar className="h-8 w-8 border border-gray-200">
                <AvatarImage
                  src={
                    form.p_image && form.p_image.trim() !== ""
                      ? form.p_image
                      : undefined
                  }
                  alt={form.created_by_name || "Unknown"}
                />
                <AvatarFallback className="bg-gray-200 text-gray-600 uppercase text-xs">
                  {(form.created_by_name || "U").trim().charAt(0)}
                </AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent className="p-2 bg-app-hero-background text-app-text-secondary">
              <p>Created by: {form.created_by_name || "Unknown"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="h-6 border-l border-gray-300 dark:border-gray-600"></div>

        <div className="flex items-center gap-1">
          <BarChart className="w-5 h-5" />
          <span>{form.response_count}</span>
        </div>

        <div className="h-6 border-l border-gray-300 dark:border-gray-600"></div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="p-2 hover:bg-app-hero-background rounded-full cursor-pointer">
                <AlertCircle className="w-5 h-5" />
              </div>
            </TooltipTrigger>
            <TooltipContent className="p-3 bg-app-hero-background text-app-text-secondary">
              <p>Created at: {createdAt}</p>
              <p>Updated at: {updatedAt}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <FormDeleteModal
        isOpen={isModalOpen}
        title="Are you sure?"
        message={form?.title}
        onClose={() => setModalOpen(false)}
        onConfirm={handleDelete}
      />

      <ShareFormDialog
        isOpen={isShareModalOpen}
        onClose={() => setShareModalOpen(false)}
        formId={form.id}
      />
    </div>
  );
};

export default BorderCard;
