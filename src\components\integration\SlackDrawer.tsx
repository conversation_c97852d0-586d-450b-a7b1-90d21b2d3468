import React, { useState, useEffect } from "react";
import { X, Loader2 } from "lucide-react";
import {
  useAddConnection,
  useGetIntegrationActions,
  useGetConnections,
  useGetChannels,
  useGetUsers,
  useLinkSlackForm,
} from "@/api-services/slack-integration";
import { useDisconnectIntegration } from "@/api-services/integration";
import { useSearchParams } from "next/navigation";
import { toast } from "react-hot-toast";
import SearchableSelect from "@/components/common/SearchableSelect";
import { useGetFormFields } from "@/api-services/form_fields";
import RichTextEditorWithMapping from "@/components/common/RichTextEditorWithMapping";
import { Option } from "@/types/types";

interface Action {
  id: string;
  name: string;
  description: string;
}

interface Connection {
  id: string;
  name: string;
}

interface Channel {
  id: string;
  name: string;
  is_channel: boolean;
  is_group: boolean;
  is_im: boolean;
  is_private: boolean;
  is_archived: boolean;
  is_member: boolean;
}

interface User {
  id: string;
  name: string;
  real_name: string;
  display_name: string;
  is_bot: boolean;
  deleted: boolean;
}

interface ExistingConnection {
  id: string;
  name: string;
  enabled: boolean;
  created_at: string;
  auth_type: string;
  credentialId: string;
  actionId: string;
  metadata?: {
    channel_id?: string;
    channel_name?: string;
    target_user_id?: string;
    message_template?: string;
  };
}

interface SlackDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: ExistingConnection[];
  onRefresh?: () => void;
}

export default function SlackDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: SlackDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");
  const credentialId = searchParams.get("credential_id");
  const successState = searchParams.get("success");

  const [connectionName, setConnectionName] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [selectedChannel, setSelectedChannel] = useState("");
  const [selectedUser, setSelectedUser] = useState("");
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [messageTemplate, setMessageTemplate] = useState("");
  const { data: formFields } = useGetFormFields(formId || "");

  const addConnectionMutation = useAddConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { mutate: disconnectIntegration, isPending } =
    useDisconnectIntegration();
  const { data: channelsResponse } = useGetChannels(selectedConnection);
  const { data: usersResponse } = useGetUsers(selectedConnection);
  const { mutate: linkSlackForm } = useLinkSlackForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const channels: Channel[] = channelsResponse?.data?.data || [];
  const users: User[] = usersResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;
  const hasExistingConnection = existingConnections.length > 0;

  const isChannelAction =
    selectedAction === "ebfc4af1-c5bc-44f2-9228-62e32034aa9b"; // Send Message action ID
  const isUserAction =
    selectedAction === "22bb3c03-e476-4b51-becb-837ea4853df6"; // Send Direct Message action ID

  // Reset form state when drawer opens for a new connection
  useEffect(() => {
    if (isOpen && !hasExistingConnection) {
      setConnectionType("new");
      setConnectionName("");
      setSelectedConnection("");
      setSelectedAction("");
      setSelectedChannel("");
      setSelectedUser("");
      setMessageTemplate("");
    }
  }, [isOpen, hasExistingConnection]);

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (
      successState === "true" &&
      credentialId &&
      connections.length > 0 &&
      actions.length > 0
    ) {
      setSelectedConnection(credentialId);
      setConnectionType("existing");
      // Restore the action from localStorage and only set if it matches an available action
      const savedAction = localStorage.getItem("selectedSlackAction");
      if (savedAction && actions.some((a) => a.id === savedAction)) {
        setSelectedAction(savedAction);
      } else if (actions.length > 0) {
        setSelectedAction(actions[0].id);
      }
    }
  }, [successState, credentialId, connections, actions]);

  useEffect(() => {
    if (initialActionId) {
      const selectedActionObj = actions.find((a) => a.id === initialActionId);
      if (selectedActionObj) {
        setSelectedAction(initialActionId);
        localStorage.setItem("selectedSlackAction", initialActionId);
        localStorage.setItem("selectedSlackActionName", selectedActionObj.name);
      }
    }
  }, [initialActionId, actions]);

  // Utility to check if a template references only valid fields
  function isTemplateValid(template: string, fields: any[]): boolean {
    const regex = /\{\{([a-f0-9-]+)(?:\.[^\}]+)?\}\}/gi;
    let match;
    while ((match = regex.exec(template)) !== null) {
      const fieldId = match[1];
      if (!fields.some((f) => f.id === fieldId)) {
        return false;
      }
    }
    return true;
  }

  // Helper to convert backend format to template format
  const convertMappedToTemplate = (key: string, options: Option[]): string => {
    if (!key) return "";
    return key.replace(
      /\{\{([^.]+)\.([^.]+)\}\}/g,
      (match, fieldId, fieldName) => {
        const opt = options.find(
          (opt) => opt.id === fieldId && opt.name === fieldName
        );
        if (!opt) return match;
        return `{{${opt.index}.${fieldName}}}`;
      }
    );
  };

  // Pre-fill form fields when there's an existing connection
  useEffect(() => {
    if (
      hasExistingConnection &&
      existingConnections[0] &&
      formFields?.data?.fields
    ) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      setSelectedConnection(connection.credentialId);
      setSelectedAction(connection.actionId);
      if (connection.metadata) {
        if (connection.metadata.channel_id) {
          setSelectedChannel(connection.metadata.channel_id);
        }
        if (connection.metadata.target_user_id) {
          setSelectedUser(connection.metadata.target_user_id);
        }
        if (connection.metadata.message_template) {
          const fields = formFields.data.fields;
          const options = buildFieldOptions(fields);
          // Convert backend format to template format for display
          const convertedTemplate = convertMappedToTemplate(
            connection.metadata.message_template,
            options
          );
          setMessageTemplate(
            isTemplateValid(connection.metadata.message_template, fields)
              ? convertedTemplate
              : ""
          );
        } else {
          setMessageTemplate("");
        }
      }
    } else {
      // Reset when no existing connection
      setSelectedConnection("");
      setSelectedAction("");
      setSelectedChannel("");
      setSelectedUser("");
      setMessageTemplate("");
    }
  }, [hasExistingConnection, existingConnections, formFields]);

  // Reset messageTemplate when drawer closes
  useEffect(() => {
    if (!isOpen) {
      setMessageTemplate("");
    }
  }, [isOpen]);

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
    setSelectedChannel("");
    setSelectedUser("");
    if (successState !== "true" && connectionId) {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set("credential_id", connectionId);
      window.history.pushState({}, "", newUrl);
    }
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
    setSelectedChannel("");
    setSelectedUser("");
    if (actionId) {
      const selectedActionObj = actions.find((a) => a.id === actionId);
      if (selectedActionObj) {
        localStorage.setItem("selectedSlackAction", actionId);
        localStorage.setItem("selectedSlackActionName", selectedActionObj.name);
      }
    } else {
      localStorage.removeItem("selectedSlackAction");
      localStorage.removeItem("selectedSlackActionName");
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName || !selectedAction) return;
    setIsAddingConnection(true);
    try {
      const response = await addConnectionMutation.mutateAsync({
        integrationId,
        formId: formId || "",
        formType: formType || "",
        name: connectionName,
        actionId: selectedAction,
      });
      // After redirect, the drawer will open and preselect the connection and action
      if (response.data?.data?.id) {
        setSelectedConnection(response.data.data.id);
        setConnectionType("existing");
        refetchConnections();
        // Save action to localStorage
        if (selectedAction) {
          const selectedActionObj = actions.find(
            (a) => a.id === selectedAction
          );
          if (selectedActionObj) {
            localStorage.setItem("selectedSlackAction", selectedAction);
            localStorage.setItem(
              "selectedSlackActionName",
              selectedActionObj.name
            );
          }
        }
      }
    } catch (error) {
      console.error("Error adding connection:", error);
      toast.error("Failed to add connection. Please try again.");
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    setIsDisconnecting(true);
    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: async () => {
          await onRefresh?.();
          toast.success("Slack disconnected successfully!");
          setSaveSuccess(false);
          setSelectedAction("");
          setSelectedConnection("");
          setSelectedChannel("");
          setSelectedUser("");
          setConnectionType("new");
          setConnectionName("");
        },
        onError: (error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect Slack. Please try again.");
        },
        onSettled: () => {
          setIsDisconnecting(false);
        },
      }
    );
  };

  // Helper to build options for SelectInputCombo
  const buildFieldOptions = (fields: any[]) => {
    const options: {
      id: string;
      name: string;
      title: string;
      index: number;
    }[] = [];
    let logicalIndex = 1;
    fields.forEach((f) => {
      if (f.component === "NAME_INPUT") {
        options.push({
          id: f.id,
          name: "firstName",
          title: f.firstNameTitle || "First Name",
          index: logicalIndex,
        });
        options.push({
          id: f.id,
          name: "lastName",
          title: f.lastNameTitle || "Last Name",
          index: logicalIndex,
        });
        logicalIndex++;
      } else if (f.component === "ADDRESS") {
        options.push({
          id: f.id,
          name: "address",
          title: "Address",
          index: logicalIndex,
        });
        const allowed = f.allowedAddressFields || {
          country: true,
          city: true,
          pincode: true,
          state: true,
        };
        if (allowed.city) {
          options.push({
            id: f.id,
            name: "city",
            title: "City",
            index: logicalIndex,
          });
        }
        if (allowed.state) {
          options.push({
            id: f.id,
            name: "state",
            title: "State",
            index: logicalIndex,
          });
        }
        if (allowed.country) {
          options.push({
            id: f.id,
            name: "country",
            title: "Country",
            index: logicalIndex,
          });
        }
        if (allowed.pincode) {
          options.push({
            id: f.id,
            name: "pincode",
            title: "Pincode",
            index: logicalIndex,
          });
        }
        logicalIndex++;
      } else {
        options.push({
          id: f.id,
          name: f.name,
          title: f.title || f.name,
          index: logicalIndex,
        });
        logicalIndex++;
      }
    });
    return options;
  };

  // Helper to convert template to mapped format
  const convertTemplateToMapped = (
    template: string,
    options: any[],
    fields: any[]
  ): string => {
    return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
      const opt = options.find(
        (opt) => String(opt.index) === idx && opt.name === name
      );
      if (!opt) return match;
      const field = fields.find((f) => f.id === opt.id);
      if (field && field.component === "NAME_INPUT") {
        return `{{${opt.id}.name.${opt.name}}}`;
      }
      if (field && field.component === "ADDRESS") {
        return `{{${opt.id}.address.${opt.name}}}`;
      }
      return `{{${opt.id}}}`;
    });
  };

  const handleSave = async () => {
    if (connectionType === "existing") {
      setIsSaving(true);
      try {
        const fields = formFields?.data?.fields || [];
        const options = buildFieldOptions(fields);
        const mappedData = convertTemplateToMapped(
          messageTemplate,
          options,
          fields
        );

        const linkFormData = {
          form_id: formId || "",
          integration_id: integrationId,
          credential_id: selectedConnection,
          action_id: selectedAction,
          column_mapped_data: [
            {
              id: "message",
              name: "message",
              title: "Message",
              key: mappedData,
            },
          ],
          channel_id: isChannelAction ? selectedChannel : "",
          channel_name: isChannelAction
            ? channels.find((c) => c.id === selectedChannel)?.name || ""
            : "",
          message_template: messageTemplate,
          target_user_id: isUserAction ? selectedUser : "",
        };

        // Use the same API for both new connections and updates
        linkSlackForm(linkFormData, {
          onSuccess: (response: any) => {
            if (response?.success) {
              setSaveSuccess(true);
              setSuccessMessage(
                existingConnections.length > 0
                  ? "Slack integration updated successfully!"
                  : "Slack integration linked successfully!"
              );
              onRefresh?.();
              onClose();
            }
          },
          onError: (error: Error) => {
            console.error("Error linking/updating Slack form:", error);
            toast.error(
              existingConnections.length > 0
                ? "Failed to update Slack integration. Please try again."
                : "Failed to link Slack integration. Please try again."
            );
          },
        });
      } catch (error) {
        console.error("Error preparing form data:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSaving(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div className="fixed inset-0 bg-black/50 z-[199] transition-opacity" />
      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    {hasExistingConnection
                      ? "Update Slack Integration"
                      : "Connect Slack Account"}
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>
            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">Slack</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action) => (
                      <option key={action.id} value={action.id}>
                        {action.name}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>
                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                          disabled={hasExistingConnection}
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>
                    {connectionType === "new" ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Connection Name
                          </label>
                          <input
                            type="text"
                            value={connectionName}
                            onChange={(e) => setConnectionName(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter connection name"
                          />
                        </div>
                        <button
                          onClick={handleAddConnection}
                          disabled={!connectionName || isAddingConnection}
                          className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                            !connectionName || isAddingConnection
                              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                              : "bg-app-text-color text-app-background hover:bg-opacity-90"
                          }`}
                        >
                          {isAddingConnection ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            "Add New Connection"
                          )}
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Select Connection
                          </label>
                          <select
                            value={selectedConnection}
                            onChange={handleConnectionSelect}
                            disabled={
                              !hasValidConnections || hasExistingConnection
                            }
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                          >
                            <option value="">Select a connection</option>
                            {connections.map((connection) => (
                              <option key={connection.id} value={connection.id}>
                                {connection.name}
                              </option>
                            ))}
                          </select>
                        </div>
                        {selectedConnection && (
                          <>
                            {isChannelAction && (
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  Select Channel
                                </label>
                                <SearchableSelect
                                  options={channels.map((channel) => ({
                                    id: channel.id,
                                    name: channel.name,
                                  }))}
                                  value={selectedChannel}
                                  onChange={setSelectedChannel}
                                  placeholder="Select a channel"
                                  label="Channel"
                                  required
                                />
                              </div>
                            )}
                            {isUserAction && (
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  Select User
                                </label>
                                <SearchableSelect
                                  options={users.map((user) => ({
                                    id: user.id,
                                    name:
                                      user.real_name ||
                                      user.display_name ||
                                      user.name,
                                  }))}
                                  value={selectedUser}
                                  onChange={setSelectedUser}
                                  placeholder="Select a user"
                                  label="User"
                                  required
                                />
                              </div>
                            )}
                            {formFields?.data?.fields !== undefined && (
                              <div className="mt-4">
                                <label className="block text-sm font-medium mb-1">
                                  Message Template
                                </label>
                                <RichTextEditorWithMapping
                                  value={messageTemplate}
                                  onChange={setMessageTemplate}
                                  options={buildFieldOptions(
                                    formFields?.data?.fields || []
                                  )}
                                  placeholder="Type your message content here..."
                                />
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {hasExistingConnection ? (
                  <>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        isDisconnecting
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-red-500 text-white hover:bg-red-600"
                      }`}
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        "Disconnect"
                      )}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedConnection ||
                        !selectedAction ||
                        (isChannelAction && !selectedChannel) ||
                        (isUserAction && !selectedUser) ||
                        !messageTemplate ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedConnection ||
                        !selectedAction ||
                        (isChannelAction && !selectedChannel) ||
                        (isUserAction && !selectedUser) ||
                        !messageTemplate ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        "Update"
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleSave}
                    disabled={
                      !selectedConnection ||
                      !selectedAction ||
                      (isChannelAction && !selectedChannel) ||
                      (isUserAction && !selectedUser) ||
                      !messageTemplate ||
                      isSaving
                    }
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      !selectedConnection ||
                      !selectedAction ||
                      (isChannelAction && !selectedChannel) ||
                      (isUserAction && !selectedUser) ||
                      !messageTemplate ||
                      isSaving
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
