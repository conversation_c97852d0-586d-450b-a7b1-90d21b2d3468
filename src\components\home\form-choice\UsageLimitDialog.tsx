import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { AlertCircle } from "lucide-react";
import { useUserProfile } from "@/api-services/auth";

interface UsageLimitDialogProps {
  onClose: () => void;
}

export default function UsageLimitDialog({ onClose }: UsageLimitDialogProps) {
  const router = useRouter();
  const { data: profileData } = useUserProfile();
  
  // Check if user is admin
  const isAdmin = profileData?.data?.user?.custom_role === "admin";

  const handleUpgrade = () => {
    // Only allow navigation to billing if user IS admin
    if (isAdmin) {
      router.push("/billing");
    }
    onClose();
  };

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-5 h-5" />
            AI Credits Limit Reached
          </DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-app-text-secondary mb-4">
            You have reached your AI credits usage limit. Please upgrade your plan to continue using AI-powered form creation.
          </p>
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color">
              Cancel
            </Button>
            {isAdmin && (
              <Button onClick={handleUpgrade} className="bg-emerald-600 hover:bg-emerald-700 text-app-background">
                Upgrade Plan
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 