import React, { useRef, useState, useEffect } from "react";
import "react-quill-new/dist/quill.snow.css";
import ReactQuill from "react-quill-new";
import { Option } from "@/types/types";

interface RichTextEditorWithMappingProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  options: Option[];
}

// Helper to convert {{index.Name}} to {{fieldId.Name}}
const convertTemplateToMapped = (
  template: string,
  options: Option[]
): string => {
  return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
    const opt = options.find(
      (opt) => String(opt.index) === idx && opt.name === name
    );
    if (!opt) return match;
    return `{{${opt.id}.${opt.name}}}`;
  });
};

// Helper to convert backend format to template format
const convertMappedToTemplate = (key: string, options: Option[]): string => {
  if (!key) return "";
  return key.replace(
    /\{\{([^.]+)\.([^.]+)\}\}/g,
    (match, fieldId, fieldName) => {
      const opt = options.find(
        (opt) => opt.id === fieldId && opt.name === fieldName
      );
      if (!opt) return match; // Keep original if no matching option
      return `{{${opt.index}.${fieldName}}}`;
    }
  );
};

const RichTextEditorWithMapping: React.FC<RichTextEditorWithMappingProps> = ({
  value,
  onChange,
  placeholder = "Type your content here...",
  label,
  options,
}) => {
  const [showFieldDropdown, setShowFieldDropdown] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<any>(null);
  const [displayValue, setDisplayValue] = useState(
    convertMappedToTemplate(value || "", options)
  );

  // Synchronize displayValue with value prop
  useEffect(() => {
    const convertedValue = convertMappedToTemplate(value || "", options);
    setDisplayValue(convertedValue);
    // Remove displayValue from dependencies to prevent unnecessary updates
  }, [value, options]);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setShowFieldDropdown(false);
      }
    }
    if (showFieldDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showFieldDropdown]);

  // Insert field at cursor position
  const insertField = (option: Option) => {
    if (!quillRef.current) return;
    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();
    const insert = `{{${option.index}.${option.name}}}`;

    if (range) {
      quill.insertText(range.index, insert);
    } else {
      quill.insertText(quill.getLength(), insert); // Append at the end
    }
    setShowFieldDropdown(false);

    // Trigger onChange with updated content
    const updatedContent = quill.root.innerHTML;
    const mappedContent = convertTemplateToMapped(updatedContent, options);
    onChange(mappedContent);
  };

  // Handle editor content change
  const handleChange = (content: string) => {
    setDisplayValue(content);
    const mappedContent = convertTemplateToMapped(content, options);
    onChange(mappedContent);
  };

  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ color: [] }, { background: [] }],
      ["link"],
      ["clean"],
    ],
  };

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "link",
    "color",
    "background",
  ];

  return (
    <div className="w-full relative" ref={wrapperRef}>
      {label && (
        <label className="block text-sm font-medium mb-1">{label}</label>
      )}
      <div className="relative">
        <ReactQuill
          ref={quillRef}
          value={displayValue}
          onChange={handleChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          className="bg-app-background text-app-text-color"
        />
        <button
          type="button"
          onClick={() => setShowFieldDropdown(!showFieldDropdown)}
          className="absolute top-2 right-2 px-2 py-1 text-sm bg-app-text-color text-app-background rounded hover:bg-opacity-90"
        >
          Insert Field
        </button>
      </div>
      {showFieldDropdown && (
        <div className="absolute z-50 bg-app-background border border-app-border-primary rounded-md mt-1 w-full max-h-48 overflow-y-auto shadow-lg scroller-style">
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={`${option.id}.${option.name}`}
                className="px-3 py-2 hover:bg-app-hero-background cursor-pointer text-sm"
                onClick={() => insertField(option)}
              >
                {`${option.index}.${option.name}`}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-app-text-color text-sm">
              No fields available
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RichTextEditorWithMapping;
