import { useQuery, useMutation } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1/slack`;

// Get Integration Actions
async function getIntegrationActions(integrationId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/action/${integrationId}`,
    method: "GET",
  });
}

const useGetIntegrationActions = (integrationId: string) => {
  return useQuery({
    queryKey: QueryKeys.SLACK_ACTIONS(integrationId),
    queryFn: () => getIntegrationActions(integrationId),
  });
};

// Get Slack Connections
async function getConnections(integrationId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/connection/${integrationId}`,
    method: "GET",
  });
}

const useGetConnections = (integrationId: string) => {
  return useQuery({
    queryKey: QueryKeys.SLACK_CONNECTIONS(integrationId),
    queryFn: () => getConnections(integrationId),
  });
};

// Add New Connection (Slack)
async function addConnection({
  integrationId,
  name,
  formId,
  formType,
  actionId,
}: {
  integrationId: string;
  name: string;
  formId: string;
  formType: string;
  actionId: string;
}) {
  const redirectUri =
    process.env.NEXT_PUBLIC_REDIRECT_URL || "http://localhost:8000";
  const response = await makeRequest({
    endpoint: `${baseEndpoint}/addconnection?integration_id=${integrationId}&name=${encodeURIComponent(
      name
    )}&redirect_uri=${redirectUri}&formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}&actionId=${encodeURIComponent(actionId)}`,
    method: "GET",
  });

  if (response?.data?.authUrl) {
    window.location.href = response.data.authUrl;
  }

  return response;
}

const useAddConnection = () => {
  return useMutation({
    mutationFn: addConnection,
  });
};

// Get Slack Channels
async function getChannels(credentialId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/channels`,
    method: "POST",
    data: { credentialId },
  });
}

const useGetChannels = (credentialId: string) => {
  return useQuery({
    queryKey: ["SLACK_CHANNELS", credentialId],
    queryFn: () => getChannels(credentialId),
    enabled: !!credentialId,
  });
};

// Get Slack Users
async function getUsers(credentialId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/users`,
    method: "POST",
    data: { credentialId },
  });
}

const useGetUsers = (credentialId: string) => {
  return useQuery({
    queryKey: ["SLACK_USERS", credentialId],
    queryFn: () => getUsers(credentialId),
    enabled: !!credentialId,
  });
};

// Link Slack Form
async function linkSlackForm(data: {
  form_id: string;
  integration_id: string;
  credential_id: string;
  action_id: string;
  column_mapped_data: Array<{
    id: string;
    name: string;
    title: string;
    key: string;
  }>;
  channel_id: string;
  channel_name: string;
  message_template: string;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/linkform`,
    method: "POST",
    data,
  });
}

const useLinkSlackForm = () => {
  return useMutation({
    mutationFn: linkSlackForm,
  });
};

export { 
  useGetIntegrationActions, 
  useGetConnections, 
  useAddConnection, 
  useGetChannels,
  useGetUsers,
  useLinkSlackForm
};
