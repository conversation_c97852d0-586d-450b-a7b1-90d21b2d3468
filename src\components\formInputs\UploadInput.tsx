import React, { Suspense, useRef, useState } from "react";
import { CloudUpload, Loader2, Trash2, <PERSON>Text, FileAudio, FileVideo, FileImage } from "lucide-react";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import { useUploadFile } from "@/api-services/form_submission";
import Image from "next/image";
import toast from "react-hot-toast";
import Loader from "../common/loader";

const fileTypeFormats = {
  image: "jpg, jpeg, png, gif, webp",
  video: "mp4, webm, ogg",
  audio: "mp3, wav, ogg",
  document: "pdf, doc, docx, txt, xls, xlsx",
};

const UploadInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  placeholder,
  title,
  titleMedia,
  allowedFileTypes = { image: true },
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
  minSize = 0,
  maxSize = 1,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, number: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  placeholder?: string;
  titleMedia?: string;
  allowedFileTypes?: Record<string, boolean>;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
  minSize?: number;
  maxSize?: number;
}) => {
  const { deleteField, duplicateField } = useAppStore();
  const [file, setFile] = useState<File | null>(null);
  const [uploadedFile, setUploadedFile] = useState<string | null>(
    value || null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUploadClick = () => {
    if (isPreview && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const { mutate: uploadFile, isPending } = useUploadFile();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = e.target.files?.[0];
      if (!file) return;

      // Get file type and extension
      const fileType = file.type.split('/')[0];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      // Validate file type against allowed types
      const isAllowedType = Object.entries(allowedFileTypes).some(([type, isAllowed]) => {
        if (!isAllowed) return false;
        
        switch (type) {
          case 'image':
            return ['image', 'jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileType) || 
                   ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension || '');
          case 'video':
            return ['video', 'mp4', 'webm', 'ogg'].includes(fileType) || 
                   ['mp4', 'webm', 'ogg'].includes(fileExtension || '');
          case 'audio':
            return ['audio', 'mp3', 'wav', 'ogg'].includes(fileType) || 
                   ['mp3', 'wav', 'ogg'].includes(fileExtension || '');
          case 'document':
            return ['application', 'text'].includes(fileType) || 
                   ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx'].includes(fileExtension || '');
          default:
            return false;
        }
      });

      if (!isAllowedType) {
        toast.error(`File type not allowed. Allowed types: ${getAllowedFormatsText()}`);
        return;
      }

      // Convert file size to MB
      const fileSizeInMB = file.size / (1024 * 1024);

      // Validate file size
      if (fileSizeInMB < minSize) {
        toast.error(`File size must be at least ${minSize}MB`);
        return;
      }
      if (fileSizeInMB > maxSize) {
        toast.error(`File size must not exceed ${maxSize}MB`);
        return;
      }

      setFile(file);

      if (!workspace_id) {
        toast.error("Workspace ID is required for upload");
        return;
      }

      const formData = new FormData();
      formData.append("upload", file as Blob);

      uploadFile(
        { formData, workspace_id },
        {
          onSuccess: (res) => {
            if (res?.data?.fileUrl) {
              setUploadedFile(res.data.fileUrl);
              onChange?.(res.data.fileUrl);
              toast.success("File uploaded successfully");
            } else {
              toast.error("Failed to get file URL from response");
              setFile(null);
            }
          },
          onError: (error: any) => {
            console.error("Error uploading file:", error);
            const errorMessage =
              error?.response?.data?.message ||
              error?.message ||
              "Failed to upload file";
            toast.error(errorMessage);
            setFile(null);
          },
        }
      );
    } catch (error: any) {
      console.error("Error in handleFileChange:", error);
      toast.error(error?.message || "An unexpected error occurred");
      setFile(null);
    }
  };

  const handleDeleteFile = () => {
    setFile(null);
    setUploadedFile(null);
    onChange?.("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const getAllowedFormatsText = () => {
    const enabledTypes = Object.entries(allowedFileTypes)
      .filter(([_, enabled]) => enabled)
      .map(([type]) => type);

    if (enabledTypes.length === 0) return "No file types allowed";

    const formats = enabledTypes
      .map(
        (type) =>
          `${type} (${fileTypeFormats[type as keyof typeof fileTypeFormats]})`
      )
      .join(", ");

    return `Upload ${formats}`;
  };

  const getFileIcon = (file: File | string) => {
    const fileType = typeof file === 'string' 
      ? file.split('.').pop()?.toLowerCase()
      : file.type.split('/')[0];

    switch (fileType) {
      case 'image':
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return <FileImage className="w-8 h-8 text-blue-500" />;
      case 'video':
      case 'mp4':
      case 'webm':
      case 'ogg':
        return <FileVideo className="w-8 h-8 text-red-500" />;
      case 'audio':
      case 'mp3':
      case 'wav':
        return <FileAudio className="w-8 h-8 text-green-500" />;
      default:
        return <FileText className="w-8 h-8 text-gray-500" />;
    }
  };

  const getFileName = (file: File | string) => {
    if (typeof file === 'string') {
      return file.split('/').pop() || 'Uploaded file';
    }
    return file.name;
  };

  const isImageFile = (file: File | string) => {
    const fileType = typeof file === 'string' 
      ? file.split('.').pop()?.toLowerCase()
      : file.type.split('/')[0];
    
    return ['image', 'jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileType || '');
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        {/* Replaced with the desired container */}
        {!file && !uploadedFile ? (
          <div
            className="relative w-full mt-2 border rounded-md bg-app-hero-background flex items-center justify-center py-4 "
            onClick={handleUploadClick}
          >
            <div className="flex flex-col items-center py-6">
              {isPending ? (
                <Loader2 className="h-6 w-6 text-app-text-secondary animate-spin" />
              ) : (
                <CloudUpload className="h-6 w-6 text-app-text-secondary" />
              )}
              <p className="text-sm text-app-text-secondary mt-2 text-center px-4">
                {getAllowedFormatsText()}
              </p>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between w-full mt-2 p-3 border rounded-md bg-app-hero-background">
            <div className="flex items-center gap-3 flex-1">
              {file && isImageFile(file) ? (
                <div className="w-16 h-12 relative">
                  <Image
                    src={URL.createObjectURL(file)}
                    alt="uploadedFile"
                    fill
                    className="object-cover rounded"
                  />
                </div>
              ) : uploadedFile && isImageFile(uploadedFile) ? (
                <div className="w-16 h-12 relative">
                  <Image
                    src={uploadedFile}
                    alt="uploadedFile"
                    fill
                    className="object-cover rounded"
                  />
                </div>
              ) : (
                getFileIcon(file || uploadedFile || '')
              )}
              <span className="text-sm text-app-text-secondary truncate">
                {getFileName(file || uploadedFile || '')}
              </span>
            </div>
            <button
              onClick={handleDeleteFile}
              className="p-1 text-red-600 hover:bg-red-50 rounded-full ml-2"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Hidden File Input */}
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileChange}
          disabled={isDisable}
        />
        <input
          type="text"
          className="hidden"
          value={uploadedFile || ""}
          name={`${id}_upload`}
          disabled={isDisable}
        />
      </FieldWrapper>
    </Suspense>
  );
};

export default UploadInput;
