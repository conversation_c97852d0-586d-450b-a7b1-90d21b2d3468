import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest } from "./utils";
import { useSearchParams } from "next/navigation";
const baseEndpoint = "/v1/form/template";

async function createCategory(data: {
  name: string;
  description: string;
  icon: string;
  share_with_team: boolean;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/categories`,
    method: "POST",
    data,
  });
}

const useCreateCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["form-templates-categories"],
      });
    },
  });
};

async function getCategories() {
  return makeRequest({
    endpoint: `${baseEndpoint}/categories`,
    method: "GET",
  });
}

const useGetCategories = () => {
  return useQuery({
    queryKey: ["form-templates-categories"],
    queryFn: getCategories,
  });
};

async function deleteCategory(category_id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/categories/${category_id}`,
    method: "DELETE",
  });
}

const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["form-templates-categories"],
      });
    },
  });
};

async function createTemplate(data: {
  name: string;
  description: string;
  image_url: string;
  template_data: {
    heading: string;
    header_img: string;
    description: string;
    type: string;
    [key: string]: string;
  };
  categoryId: string;
}): Promise<any> {
  return makeRequest({
    endpoint: `${baseEndpoint}`,
    method: "POST",
    data,
  });
}

const useCreateTemplate = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["form-templates-categories"],
      });
    },
  });
};

async function getTemplatesByCategoryId(categoryId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${categoryId}`,
    method: "GET",
  });
}

const useGetTemplatesByCategoryId = (categoryId: string) => {
  return useQuery({
    queryKey: ["form-templates", categoryId],
    queryFn: () => getTemplatesByCategoryId(categoryId),
    enabled: !!categoryId,
  });
};

async function deleteTemplate(templateId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${templateId}`,
    method: "DELETE",
  });
}

const useDeleteTemplate = () => {
  return useMutation({
    mutationFn: deleteTemplate,
  });
};

async function updateTemplate(templateId: string, data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${templateId}`,
    method: "PUT",
    data,
  });
}

const useUpdateTemplate = () => {
  return useMutation({
    mutationFn: ({ templateId, data }: { templateId: string; data: any }) =>
      updateTemplate(templateId, data),
  });
};

async function getTemplateDetails(templateId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/temp/${templateId}`,
    method: "GET",
  });
}

const useGetTemplateDetails = (templateId: string) => {
  const isTemplate = useSearchParams().get("formType") === "template";
  return useQuery({
    queryKey: ["form-templates-details", templateId],
    queryFn: () => getTemplateDetails(templateId),
    enabled: isTemplate,
  });
};

async function createFormWithTemplate(data: { template_id: string; folder_id?: string }) {
  return makeRequest({
    endpoint: `/v1/forms/createform/template`,
    method: "POST",
    data,
  });
}

const useCreateFormWithTemplate = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createFormWithTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["usageStats"],
      });
    },
  });
};

async function changeTemplateStatus(templateId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/status/${templateId}`,
    method: "PUT",
  });
}

const useChangeTemplateStatus = () => {
  return useMutation({
    mutationFn: changeTemplateStatus,
  });
};

async function cloneTemplate(templateId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/clone/${templateId}`,
    method: "POST",
  });
}

const useCloneTemplate = () => {
  return useMutation({
    mutationFn: cloneTemplate,
  });
};

async function getPublicTemplatesByCategory(categoryId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/category/${categoryId}/published`,
    method: "GET",
  });
}

const useGetPublicTemplatesByCategory = (categoryId: string) => {
  return useQuery({
    queryKey: ["public-templates", categoryId],
    queryFn: () => getPublicTemplatesByCategory(categoryId),
    enabled: !!categoryId,
  });
};

export {
  useGetCategories,
  useCreateCategory,
  useDeleteCategory,
  useGetTemplatesByCategoryId,
  useCreateTemplate,
  useDeleteTemplate,
  useUpdateTemplate,
  useGetTemplateDetails,
  useCreateFormWithTemplate,
  useChangeTemplateStatus,
  useCloneTemplate,
  useGetPublicTemplatesByCategory,
};
