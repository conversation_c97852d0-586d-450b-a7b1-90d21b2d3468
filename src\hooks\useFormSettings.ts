import { useState, useEffect } from "react";
import { useUpdateFormSettings } from "@/api-services/form_setting";
import { useGetFormDetails } from "@/api-services/form";
import toast from "react-hot-toast";

export const useFormSettings = (formId: string) => {
  const [accessSetting, setAccessSetting] = useState("private");
  const [formVisibility, setFormVisibility] = useState("enabled");
  const [copyAllowed, setCopyAllowed] = useState("enabled");
  const { data: formDetailsResponse } = useGetFormDetails(formId!);
  const [showPublishDialog, setShowPublishDialog] = useState(false);

  const isPublished = formDetailsResponse?.data?.form?.published || false;

  // Initialize form visibility and copy settings based on form details response
  useEffect(() => {
    if (formDetailsResponse?.data?.form?.automate_form_settings?.[0]) {
      const settings = formDetailsResponse.data.form.automate_form_settings[0];
      const acceptResponses = settings.accept_responses;
      const copyAllowed = settings.copy_allowed;

      setFormVisibility(acceptResponses ? "enabled" : "disabled");
      setCopyAllowed(copyAllowed ? "enabled" : "disabled");
    }
  }, [formDetailsResponse]);

  const [isCopied, setIsCopied] = useState(false);
  const accessOptions = [
    {
      value: "private",
      label: "Private form",
      description: "Only available to invited peoples",
    },
    { value: "public", label: "Public form", description: "Anyone to anyone" },
  ];
  const updateFormSettings = useUpdateFormSettings();

  const visibilityOptions = [
    {
      value: "enabled",
      label: "Enable form",
      description: "Accepting responses",
    },
    {
      value: "disabled",
      label: "Disable form",
      description: "Not accepting responses",
    },
  ];

  const copyOptions = [
    {
      value: "enabled",
      label: "Enable copy form",
      description: "Allow users to copy this form",
    },
    {
      value: "disabled",
      label: "Disable copy form",
      description: "Prevent users from copying this form",
    },
  ];

  const handleVisibilityChange = async (value: string) => {
    try {
      setFormVisibility(value);
      await updateFormSettings.mutateAsync({
        formId,
        data: {
          is_public: true, // Always true as per requirements
          accept_responses: value === "enabled",
          copy_allowed: copyAllowed === "enabled",
        },
      });
      toast.success(
        value === "enabled"
          ? "Form enabled successfully"
          : "Form disabled successfully"
      );
    } catch (error) {
      // Revert the state if the API call fails
      setFormVisibility(formVisibility);
      console.error("Failed to update form settings:", error);
      toast.error("Failed to update form visibility");
    }
  };

  const handleCopyAllowedChange = async (value: string) => {
    if (value === "enabled" && !isPublished) {
      setShowPublishDialog(true);
      return;
    }
    try {
      setCopyAllowed(value);
      await updateFormSettings.mutateAsync({
        formId,
        data: {
          is_public: true, // Always true as per requirements
          accept_responses: formVisibility === "enabled",
          copy_allowed: value === "enabled",
        },
      });
      toast.success(
        value === "enabled"
          ? "Copy form enabled successfully"
          : "Copy form disabled successfully"
      );
    } catch (error) {
      // Revert the state if the API call fails
      setCopyAllowed(copyAllowed);
      console.error("Failed to update form settings:", error);
      toast.error("Failed to update copy form settings");
    }
  };

  const handleCopyForm = async () => {
    try {
      // You can replace this URL with the actual form copy URL
      const copyUrl = `${window.location.origin}/form/${formId}/copy`;
      await navigator.clipboard.writeText(copyUrl);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy form URL:", error);
    }
  };

  return {
    accessSetting,
    formVisibility,
    copyAllowed,
    accessOptions,
    visibilityOptions,
    copyOptions,
    setFormVisibility: handleVisibilityChange,
    setCopyAllowed: handleCopyAllowedChange,
    handleCopyForm,
    isCopied,
    isPublished,
    showPublishDialog,
    setShowPublishDialog,
  };
};
