"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import FormsList from "../forms-list/forms-list";
import BorderList from "../forms-list/border-list";
import { useFolderListContainer } from "@/hooks/useFolderListContainer";
import {
  LayoutGrid,
  LayoutList,
  SearchIcon,
} from "lucide-react";
import { useGetUserForms } from "@/api-services/form";
import { Button } from "@/components/ui/button";
import AppPagination from "@/components/common/app-pagination";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

const LIMIT = 10;

const FolderListContainer = ({ title }: any) => {
  const [params, setParams] = useState({
    limit: 10,
    offset: 0,
  });
  const { selectedTab, setSelectedTab, searchQuery, setSearchQuery } =
    useFolderListContainer();

  const { data: formsListResponse } = useGetUserForms(params, {});

  const MAX_OFFSET = (formsListResponse as any)?.data?.total_count || 0;


  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * LIMIT;
    setParams((prevParams) => ({
      ...prevParams,
      offset: newOffset,
    }));
  };

  const currentPage = Math.floor(params.offset / LIMIT) + 1;

  // State for popup
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [folderName, setFolderName] = useState("");

  const handleCreateFolder = () => {
    console.log("Creating folder:", folderName);
    setIsDialogOpen(false);
  };

  return (
    <div className="flex flex-col w-full overflow-auto space-y-5 py-2">
      <div className="flex flex-row items-center justify-between max-[680px]:items-start gap-2">
        <div className="flex flex-row items-center gap-4 max-[680px]:flex-col max-[680px]:items-start max-[680px]:gap-0.5">
          <h3 className="text-2xl font-semibold text-app-text-color">
            {title}
          </h3>
          <div className="relative">
            <SearchIcon className="absolute p-0.5 bottom-2 left-2 text-app-text-secondary" />
            <Input
              placeholder="Search Forms..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 max-w-[300px] w-full font-medium bg-app-background placeholder:text-app-text-secondary text-app-text-color border-app-border-primary"
            />
          </div>
        </div>
        <div className="flex gap-3">
          <Button onClick={() => setIsDialogOpen(true)}>Create Folder</Button>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="flex rounded-md dark:bg-app-hero-background bg-app-hero-background">
              <TabsTrigger
                value="list"
                className={`p-2 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "list"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutList />
                <span>List</span>
              </TabsTrigger>
              <TabsTrigger
                value="grid"
                className={`p-1.5 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "grid"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutGrid />
                <span>Grid</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsContent value="list">
            <FormsList params={params} formsList={(formsListResponse as any)?.data?.forms} />
          </TabsContent>
          <TabsContent value="grid">
            <BorderList params={params} formsList={(formsListResponse as any)?.data?.forms} />
          </TabsContent>
        </Tabs>

        <AppPagination
          currentPage={currentPage}
          totalItems={MAX_OFFSET}
          itemsPerPage={LIMIT}
          onPageChange={handlePageChange}
          showInfo={true}
        />
      </div>

      {/* Create Folder Popup */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-sm mx-auto p-6 rounded-lg bg-white dark:bg-gray-900 shadow-xl border border-gray-300 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100">
              Create Folder
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Enter folder name"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              className="w-full bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2"
            />
          </div>
          <DialogFooter className="flex justify-center">
            <DialogClose asChild>
              <Button
                onClick={handleCreateFolder}
                className="w-full bg-black hover:bg-black-700 text-white"
              >
                Create Folder
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FolderListContainer;
