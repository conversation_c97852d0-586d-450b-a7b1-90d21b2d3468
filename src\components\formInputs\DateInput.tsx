import React from "react";
import { CalendarIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import { DayPicker } from "react-day-picker";
import "react-day-picker/dist/style.css";

const DateInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  isRequired,
  description,
  placeholder,
  title,
  component,
  titleMedia,
  isPreview = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
  dateFormat = "MM/DD/YYYY",
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  placeholder?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
  dateFormat?: string;
}) => {
  const { deleteField, duplicateField } = useAppStore();
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined
  );

  useGetConditionById(id, selectedDate ? dateToISOString(selectedDate) : "");

  // Add a date formatting function
  function formatDate(date: Date | undefined, format: string): string {
    if (!date) return "";
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    switch (format) {
      case "MM/DD/YYYY":
        return `${mm}/${dd}/${yyyy}`;
      case "DD/MM/YYYY":
        return `${dd}/${mm}/${yyyy}`;
      case "YYYY/MM/DD":
        return `${yyyy}/${mm}/${dd}`;
      default:
        return date.toLocaleDateString();
    }
  }

  // Function to convert date to ISO string without timezone conversion
  function dateToISOString(date: Date | undefined): string {
    if (!date) return "";
    
    // Create a new date with the same year, month, and day but at noon UTC
    // This prevents timezone conversion issues
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();
    
    // Create date at noon UTC to avoid timezone issues
    const utcDate = new Date(Date.UTC(year, month, day, 12, 0, 0, 0));
    return utcDate.toISOString();
  }

  if (isHide && isPreview) {
    return null;
  }

  return (
    <FieldWrapper
      id={id}
      dragHandleProps={dragHandleProps}
      deleteField={deleteField}
      duplicateField={duplicateField}
      fieldIndex={fieldIndex}
      triggerSettingsAction={triggerSettingsAction}
      isRequired={isRequired}
      title={title}
      description={description}
      component={component}
      titleMedia={titleMedia}
      isPreview={isPreview}
      isEyeCross={isHide}
      workspace_id={workspace_id}
    >
      <input
        type="text"
        className="hidden"
        value={selectedDate ? dateToISOString(selectedDate) : ""}
        name={`${id}_date`}
      />
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start mt-2 bg-app-hero-background hover:bg-app-hero-background hover:text-app-text-color"
          >
            <CalendarIcon className="mr-2" />
            {selectedDate ? formatDate(selectedDate, dateFormat) : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <DayPicker
            mode="single"
            selected={selectedDate}
            onSelect={(date) => {
              setSelectedDate(date);
              onChange?.(date ? dateToISOString(date) : "");
            }}
            fromYear={1900}
            toYear={new Date().getFullYear() + 10}
            captionLayout="dropdown"
            className="rounded-md"
            showOutsideDays
          />
        </PopoverContent>
      </Popover>
    </FieldWrapper>
  );
};

export default DateInput;
