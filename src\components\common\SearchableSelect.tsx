import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, X } from "lucide-react";

interface Option {
  id: string | number;
  name: string;
  [key: string]: any;
}

interface SearchableSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  label?: string;
  required?: boolean;
  getOptionLabel?: (option: Option) => string;
  getOptionValue?: (option: Option) => string;
}

export default function SearchableSelect({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  className = "",
  disabled = false,
  label,
  required = false,
  getOptionLabel = (option) => option?.name || "",
  getOptionValue = (option) => {
    if (!option || option.id === undefined || option.id === null) return "";
    return String(option.id);
  },
}: SearchableSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter((option) =>
    getOptionLabel(option).toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Find selected option when value changes
  useEffect(() => {
    const option = options.find((opt) => getOptionValue(opt) === value);
    setSelectedOption(option || null);
  }, [value, options, getOptionValue]);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (option: Option) => {
    onChange(getOptionValue(option));
    setSearchTerm("");
    setIsOpen(false);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange("");
    setSelectedOption(null);
    setSearchTerm("");
  };

  return (
    <div className="relative" ref={wrapperRef}>
      {label && (
        <label className="block text-sm font-medium mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div
        className={`relative w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color cursor-pointer ${
          disabled ? "opacity-50 cursor-not-allowed" : ""
        } ${className}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 truncate capitalize">
            {selectedOption ? (
              getOptionLabel(selectedOption)
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          <div className="flex items-center">
            {selectedOption && !disabled && (
              <button
                onClick={handleClear}
                className="p-1 hover:bg-gray-100 rounded-full mr-1"
              >
                <X className="h-4 w-4 text-gray-500" />
              </button>
            )}
            <ChevronDown
              className={`h-4 w-4 text-gray-500 transition-transform ${
                isOpen ? "transform rotate-180" : ""
              }`}
            />
          </div>
        </div>
      </div>

      {isOpen && !disabled && (
        <div className="absolute z-10 w-full mt-1 bg-app-background border border-app-border-primary rounded-md shadow-lg max-h-60 overflow-auto scroller-style">
          <div className="sticky top-0 bg-app-background p-2 border-b border-app-border-primary">
            <input
              type="text"
              className="w-full p-2 border border-app-border-primary bg-app-main-background rounded-md focus:outline-none focus:ring-2 focus:ring-app-text-color"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
          <div className="py-1 ">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                  key={getOptionValue(option)}
                  className={`px-4 py-2 cursor-pointer capitalize ${
                    getOptionValue(option) === value
                      ? "bg-app-sidebar-hover hover:bg-app-sidebar-hover-active"
                      : "hover:bg-app-sidebar-hover-active"
                  }`}
                  onClick={() => handleSelect(option)}
                >
                  {getOptionLabel(option)}
                </div>
              ))
            ) : (
              <div className="px-4 py-2 text-gray-500">No options found</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
