"use client";
import { useSearchParams } from "next/navigation";
import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  useGetFormResponses,
  useSearchFormResponses,
} from "@/api-services/form_response";
import { useGetFormFields } from "@/api-services/form_fields";
import { utils, writeFile } from "xlsx";
import {
  Download,
  Save,
  RotateCcw,
  Upload,
  Check,
  Pencil,
  Trash2,
  List,
  X,
  ChevronDown,
  MoreVertical,
} from "lucide-react";
import { useDebounce } from "use-debounce";
import { filterTableData, tableDataParser } from "@/utils/tableDataParser";
import TableRenderer, { TableRendererRef } from "./tableRenderer";
import TableFilter from "./table-filter/table-filter";
import DatePicker from "./date-picker";
import { format } from "date-fns";
import { usePermission } from "@/hooks/usePersmission";
import AppPagination from "@/components/common/app-pagination";
import toast from "react-hot-toast";
import {
  useCreateFilterView,
  useGetFilterView,
  useDeleteFilterView,
  useUpdateFilterView,
} from "@/api-services/filter_view";
import { useUserProfile } from "@/api-services/auth";
import { createPortal } from "react-dom";
import { Button } from "../ui/button";

const LIMIT = 14;

interface Answer {
  id: string;
  name: string;
  title: string;
  value: string | { [key: string]: string };
}

interface FormResponse {
  response_id: string;
  form_id: string;
  answers: Answer[];
  submitted_at: string;
}

interface View {
  id: string;
  name: string;
  description: string;
  data: any;
  access: string[];
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

const ViewTab = ({
  view,
  isActive,
  onApply,
  onEdit,
  onDelete,
  dropdownOpen,
  setDropdownOpen,
}: {
  view: any;
  isActive: boolean;
  onApply: () => void;
  onEdit: () => void;
  onDelete: () => void;
  dropdownOpen: boolean;
  setDropdownOpen: (open: boolean) => void;
}) => {
  const tabRef = useRef<HTMLDivElement>(null);
  const [dropdownPos, setDropdownPos] = useState<{
    left: number;
    top: number;
  } | null>(null);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownOpen &&
        tabRef.current &&
        !tabRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen, setDropdownOpen]);

  // Calculate dropdown position when opening
  useEffect(() => {
    if (dropdownOpen && tabRef.current) {
      const rect = tabRef.current.getBoundingClientRect();
      setDropdownPos({
        left: rect.right,
        top: rect.top,
      });
    }
  }, [dropdownOpen]);

  return (
    <div
      ref={tabRef}
      className={`flex items-center px-4 py-2 rounded-t-lg border-b-2 cursor-pointer transition-all mr-1 whitespace-nowrap relative ${
        isActive
          ? "border-emerald-300 bg-app-background dark:bg-app-background text-emerald-600 dark:text-emerald-400 font-semibold"
          : "border-transparent bg-transparent text-app-text-color dark:text-app-text-color hover:bg-gray-100 dark:hover:bg-gray-800"
      }`}
      onClick={onApply}
      title={view.name}
    >
      <span className="truncate max-w-[120px]">{view.name}</span>
    </div>
  );
};

const FormResponses = () => {
  const { PermissionProtected } = usePermission();
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  const [text, setText] = useState("");
  const [value] = useDebounce(text, 300);
  const [selectedFilterKeys, setSelectedFilterKeys] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasSavedFilters, setHasSavedFilters] = useState(false);
  const [isLoadingFilters, setIsLoadingFilters] = useState(false);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [showViewsDropdown, setShowViewsDropdown] = useState(false);
  const [viewTitle, setViewTitle] = useState("");
  const [viewDescription, setViewDescription] = useState("");
  const [views, setViews] = useState<View[]>([
    {
      id: "1",
      name: "QA Board",
      description: "QA tickets for May",
      data: {},
      access: [],
    },
    {
      id: "2",
      name: "My Bugs",
      description: "All bugs assigned to me",
      data: {},
      access: [],
    },
  ]);

  // Ref to access AG Grid methods
  const tableRef = useRef<TableRendererRef>(null);

  const { data: formFieldsData } = useGetFormFields(formId as string);

  const [paginationParams, setPaginationParams] = useState<{
    offset: number;
    limit: number;
    start_date: Date | undefined;
    end_date: Date | undefined;
  }>({
    offset: (currentPage - 1) * LIMIT,
    limit: LIMIT,
    start_date: undefined,
    end_date: undefined,
  });

  // Update offset when currentPage changes
  useEffect(() => {
    setPaginationParams((prev) => ({
      ...prev,
      offset: (currentPage - 1) * LIMIT,
    }));
  }, [currentPage]);

  // Fetch form responses
  const { data, isLoading, error } = useGetFormResponses(formId as string, {
    ...paginationParams,
    start_date: paginationParams.start_date
      ? format(paginationParams.start_date, "yyyy-MM-dd")
      : "",
    end_date: paginationParams.end_date
      ? format(paginationParams.end_date, "yyyy-MM-dd")
      : "",
  });

  const {
    mutate: searchFormResponses,
    data: searchData,
    isPending: isSearchLoading,
  } = useSearchFormResponses();

  useEffect(() => {
    if (value && formId) {
      searchFormResponses({
        form_id: formId,
        searchtext: value,
        limit: LIMIT,
        offset: 0,
      });
      setCurrentPage(1);
    }
  }, [value, formId, searchFormResponses]);

  // Ensure data is correctly extracted
  const responses: FormResponse[] = value
    ? searchData?.data?.responses || []
    : data?.data?.responses || [];

  const renderTableData = tableDataParser(responses);

  const MAX_OFFSET = value
    ? searchData?.data?.total_count || 0
    : data?.data?.total_count || 0;

  // Extract unique column headers dynamically from all responses
  const headers: string[] = Array.from(
    new Set(
      responses?.flatMap((res) =>
        res?.answers?.flatMap((ans) =>
          ans.name === "Full Name" ? ["First Name", "Last Name"] : [ans.name]
        )
      ) || []
    )
  );

  // Extract number columns from formFieldsData
  const numberColumns: string[] = (formFieldsData?.data?.fields || [])
    .filter((f: any) => f.component === "NUMBER" || f.type === "NUMBER")
    .map((f: any) => f.title || f.name);

  // Helper to find the best matching field for a data key
  const getFieldTitleForKey = (key: string) => {
    const fields = formFieldsData?.data?.fields || [];
    // 1. Try to match by id
    let field = fields.find((f: any) => f.id === key);
    if (field) return field.title || field.name;

    // 2. Try to match by name
    field = fields.find((f: any) => f.name === key);
    if (field) return field.title || field.name;

    // 3. Try to match by type/component (for generic keys like 'phone', 'checkbox', 'radio')
    field = fields.find(
      (f: any) =>
        (f.type && f.type.toLowerCase() === key.toLowerCase()) ||
        (f.component && f.component.toLowerCase() === key.toLowerCase())
    );
    if (field) return field.title || field.name;

    // 4. Fallback to the key itself
    return key;
  };

  // Function to export data as CSV
  const exportData = (format: "csv" | "excel") => {
    // Use the same processed data as the table display
    const processedData = filterTableData(renderTableData, selectedFilterKeys);

    const formattedData = processedData.map((row) => {
      const exportRow: Record<string, string> = {};

      // Convert all values to strings and handle any special formatting
      Object.entries(row).forEach(([key, value]) => {
        const headerTitle = getFieldTitleForKey(key);
        if (value === null || value === undefined) {
          exportRow[headerTitle] = "N/A";
        } else if (typeof value === "object") {
          exportRow[headerTitle] = JSON.stringify(value);
        } else {
          exportRow[headerTitle] = String(value);
        }
      });

      return exportRow;
    });

    const worksheet = utils.json_to_sheet(formattedData);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, "Responses");

    if (format === "csv") {
      writeFile(workbook, "form_responses.csv");
    } else {
      writeFile(workbook, "form_responses.xlsx");
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [paginationParams.start_date, paginationParams.end_date]);

  const workspaceId =
    typeof window !== "undefined" ? localStorage.getItem("workspace_id") : "";
  const createFilterViewMutation = useCreateFilterView();
  const {
    data: filterViewsData,
    isLoading: isViewsLoading,
    error: viewsError,
    refetch: refetchViews,
  } = useGetFilterView(formId || "");
  const deleteFilterViewMutation = useDeleteFilterView();
  const updateFilterViewMutation = useUpdateFilterView();
  const [editingViewId, setEditingViewId] = useState<string | null>(null);
  const { data: userProfile } = useUserProfile();
  const userId = userProfile?.data?.user?.user_id;

  // Add state to track the currently applied view and its filters
  const [appliedViewId, setAppliedViewId] = useState<string | null>(null);
  const [lastAppliedViewFilters, setLastAppliedViewFilters] =
    useState<any>(null);
  const [openDropdownViewId, setOpenDropdownViewId] = useState<string | null>(
    null
  );
  const [showViewsMenu, setShowViewsMenu] = useState(false);
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const [menuPos, setMenuPos] = useState<{ left: number; top: number } | null>(
    null
  );

  // Add summary dialog state
  const [showSummaryDialog, setShowSummaryDialog] = useState(false);
  const [summaryName, setSummaryName] = useState("");
  const [summaryDescription, setSummaryDescription] = useState("");

  // Utility to get the current filter state
  const getCurrentFilterState = () => {
    const agGridFilters = tableRef.current
      ? tableRef.current.getAgGridFilterModel()
      : null;
    return {
      searchText: text,
      startDate: paginationParams.start_date,
      endDate: paginationParams.end_date,
      selectedColumns: selectedFilterKeys,
      agGridFilters: agGridFilters,
    };
  };

  // Helper to compare filter states (shallow for now)
  const isSameFilterState = (a: any, b: any) => {
    if (!a || !b) return false;
    return (
      a.searchText === b.searchText &&
      JSON.stringify(a.selectedColumns) === JSON.stringify(b.selectedColumns) &&
      String(a.startDate) === String(b.startDate) &&
      String(a.endDate) === String(b.endDate) &&
      JSON.stringify(a.agGridFilters) === JSON.stringify(b.agGridFilters)
    );
  };

  // Update handleApplyView to track applied view and filters
  const handleApplyView = (view: View) => {
    setShowViewsDropdown(false);
    const data = view.data || {};
    setText("");
    setSelectedFilterKeys([]);
    setPaginationParams((prev) => ({
      ...prev,
      start_date: undefined,
      end_date: undefined,
    }));
    if (tableRef.current) {
      tableRef.current.resetAgGridFilters();
    }
    setTimeout(() => {
      if (data.searchText) setText(data.searchText);
      if (Array.isArray(data.selectedColumns))
        setSelectedFilterKeys(data.selectedColumns);
      setPaginationParams((prev) => ({
        ...prev,
        start_date: data.startDate ? new Date(data.startDate) : undefined,
        end_date: data.endDate ? new Date(data.endDate) : undefined,
      }));
      if (data.agGridFilters && tableRef.current) {
        tableRef.current.setAgGridFilterModel(data.agGridFilters);
      }
      setCurrentPage(1);
      setAppliedViewId(view.id);
      setLastAppliedViewFilters({
        searchText: data.searchText || "",
        startDate: data.startDate ? new Date(data.startDate) : undefined,
        endDate: data.endDate ? new Date(data.endDate) : undefined,
        selectedColumns: Array.isArray(data.selectedColumns)
          ? data.selectedColumns
          : [],
        agGridFilters: data.agGridFilters || null,
      });
      toast.success(`Applied view: ${view.name}`);
    }, 100);
  };

  // Add updateView handler
  const handleUpdateView = async () => {
    if (!appliedViewId) return;
    const agGridFilters = tableRef.current
      ? tableRef.current.getAgGridFilterModel()
      : null;
    const filterData = {
      form_id: formId,
      name: viewTitle, // Optionally use last applied view's name
      description: viewDescription, // Optionally use last applied view's description
      data: {
        searchText: text,
        startDate: paginationParams.start_date,
        endDate: paginationParams.end_date,
        selectedColumns: selectedFilterKeys,
        agGridFilters: agGridFilters,
      },
      access: userId ? [userId] : [],
    };
    try {
      const result = await updateFilterViewMutation.mutateAsync({
        id: appliedViewId,
        data: filterData,
      });
      if (result?.success) {
        toast.success("View updated!");
        setLastAppliedViewFilters(getCurrentFilterState());
        refetchViews();
      } else {
        toast.error(result?.error || "Failed to update view");
      }
    } catch (err: any) {
      toast.error(err?.message || "Failed to update view");
    }
  };

  const handleCreateView = async () => {
    const filterData = {
      name: viewTitle,
      description: viewDescription,
      data: {
        searchText: text,
        startDate: paginationParams.start_date,
        endDate: paginationParams.end_date,
        selectedColumns: selectedFilterKeys,
        // Add more filter state as needed
      },
      access: workspaceId ? [workspaceId] : [],
    };
    try {
      await createFilterViewMutation.mutateAsync(filterData);
      setShowSaveViewModal(false);
      setViewTitle("");
      setViewDescription("");
      toast.success("View saved!");
      console.log("Saving view filterData:", filterData);
    } catch (err: any) {
      toast.error(err?.message || "Failed to save view");
    }
  };

  const handleEditView = (view: View) => {
    console.log("Editing View:", view);
    setEditingViewId(view.id);
    setViewTitle(view.name);
    setViewDescription(view.description || "");
    // Restore filter state from the view's data
    const data = view.data || {};
    setText(data.searchText || "");
    setSelectedFilterKeys(
      Array.isArray(data.selectedColumns) ? data.selectedColumns : []
    );
    setPaginationParams((prev) => ({
      ...prev,
      start_date: data.startDate ? new Date(data.startDate) : undefined,
      end_date: data.endDate ? new Date(data.endDate) : undefined,
    }));
    if (data.agGridFilters) {
      setTimeout(() => {
        if (tableRef.current) {
          tableRef.current.setAgGridFilterModel(data.agGridFilters);
        }
      }, 300);
    } else {
      setTimeout(() => {
        if (tableRef.current) {
          tableRef.current.resetAgGridFilters();
        }
      }, 300);
    }
    setShowSaveViewModal(true);
  };

  const handleSaveView = async () => {
    // Get current AG Grid filter model
    const agGridFilters = tableRef.current
      ? tableRef.current.getAgGridFilterModel()
      : null;

    const filterData = {
      form_id: formId,
      name: viewTitle,
      description: viewDescription,
      data: {
        searchText: text,
        startDate: paginationParams.start_date,
        endDate: paginationParams.end_date,
        selectedColumns: selectedFilterKeys,
        agGridFilters: agGridFilters,
      },
      access: userId ? [userId] : [],
    };
    try {
      let result;
      if (editingViewId) {
        result = await updateFilterViewMutation.mutateAsync({
          id: editingViewId,
          data: filterData,
        });
        if (result?.success) {
          toast.success("View updated!");
        } else {
          toast.error(result?.error || "Failed to update view");
          return;
        }
      } else {
        result = await createFilterViewMutation.mutateAsync(filterData);
        if (result?.success) {
          toast.success("View saved!");
        } else {
          toast.error(result?.error || "Failed to save view");
          return;
        }
      }
      setShowSaveViewModal(false);
      setViewTitle("");
      setViewDescription("");
      setEditingViewId(null);
      refetchViews();
      console.log("Saving view filterData:", filterData);
    } catch (err: any) {
      toast.error(err?.message || "Failed to save view");
    }
  };

  const handleDeleteView = async (id: string) => {
    try {
      await deleteFilterViewMutation.mutateAsync(id);
      toast.success("View deleted");
      refetchViews();
    } catch (err: any) {
      toast.error(err?.message || "Failed to delete view");
    }
  };

  // Handle summary form submission
  const handleSubmitSummary = async () => {
    if (!summaryName.trim()) {
      toast.error("Summary name is required");
      return;
    }

    try {
      // TODO: Add API call to save summary
      console.log("Submitting summary:", {
        name: summaryName,
        description: summaryDescription,
        formId: formId,
        filters: getCurrentFilterState()
      });
      
      toast.success("Summary created successfully!");
      setShowSummaryDialog(false);
      setSummaryName("");
      setSummaryDescription("");
    } catch (err: any) {
      toast.error(err?.message || "Failed to create summary");
    }
  };

  // 1. Add agGridFilters state
  const [agGridFilters, setAgGridFilters] = useState({});

  // 2. Update isAnyFilterApplied to use agGridFilters state
  const isAnyFilterApplied =
    text.trim().length > 0 ||
    selectedFilterKeys.length > 0 ||
    paginationParams.start_date ||
    paginationParams.end_date ||
    (agGridFilters && Object.keys(agGridFilters).length > 0);

  // Always use only the filter views for the current form:
  const currentFormViews = filterViewsData?.data?.data || [];

  // Debug log to check currentFormViews content
  console.log(currentFormViews, "currentFormViews");

  return (
    <div className="flex flex-col w-full p-4">
      {/* Save View and Views UI */}
      <div className="flex flex-col gap-2 mb-2">
        <div className="flex justify-between items-center">
          <div className="flex flex-wrap items-center gap-2">
            <input
              type="text"
              placeholder="Search..."
              className="bg-app-main-background text-app-text-color px-4 py-2 rounded-md outline-none border border-app-border-primary w-64 h-10"
              onChange={(e) => setText(e.target.value)}
              value={text}
            />
            {/* Always show both date pickers, just swap label for selected date */}
            <DatePicker
              onDateChange={(startDate) => {
                setPaginationParams((prevParams) => ({
                  ...prevParams,
                  start_date: startDate as Date,
                }));
              }}
              label={
                paginationParams.start_date
                  ? format(paginationParams.start_date, "yyyy-MM-dd")
                  : "Start Date"
              }
              selectedDate={paginationParams.start_date}
            />
            <DatePicker
              onDateChange={(endDate) => {
                setPaginationParams((prevParams) => ({
                  ...prevParams,
                  end_date: endDate as Date,
                }));
              }}
              label={
                paginationParams.end_date
                  ? format(paginationParams.end_date, "yyyy-MM-dd")
                  : "End Date"
              }
              selectedDate={paginationParams.end_date}
            />
            {(paginationParams.start_date || paginationParams.end_date) && (
              <button
                className="ml-1 p-2 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-app-border-primary dark:hover:bg-app-border-secondary text-gray-500 dark:text-gray-300 flex items-center justify-center cursor-pointer transition-colors"
                onClick={() =>
                  setPaginationParams((prev) => ({
                    ...prev,
                    start_date: undefined,
                    end_date: undefined,
                  }))
                }
                title="Clear filters"
              >
                <X className="w-4 h-4" />
              </button>
            )}
            <TableFilter
              data={renderTableData}
              onFilterChange={(selectedKeys) => {
                setSelectedFilterKeys(selectedKeys);
              }}
              numberColumns={numberColumns}
            />
            {/* Show Save View only if no view is applied, and filters are applied */}
            {!appliedViewId && isAnyFilterApplied && (
              <button
                className="px-4 py-2 rounded border border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900 text-green-700 dark:text-green-300 transition shadow-sm flex items-center gap-2 cursor-pointer hover:bg-green-100 dark:hover:bg-green-800"
                onClick={() => {
                  setEditingViewId(null);
                  setViewTitle("");
                  setViewDescription("");
                  setShowSaveViewModal(true);
                }}
              >
                <Save className="w-4 h-4 text-green-500 dark:text-green-300" />
                Save View
              </button>
            )}
            {/* Show Update View only if a view is applied and filters have changed */}
            {appliedViewId &&
              !isSameFilterState(
                getCurrentFilterState(),
                lastAppliedViewFilters
              ) && (
                <button
                  className="px-4 py-2 rounded border border-orange-500 dark:border-orange-400 bg-orange-50 dark:bg-orange-900 text-orange-700 dark:text-orange-300 transition shadow-sm flex items-center gap-2 cursor-pointer hover:bg-orange-100 dark:hover:bg-orange-800 hover:text-orange-800 dark:hover:text-orange-200"
                  onClick={handleUpdateView}
                >
                  <Pencil className="w-4 h-4 text-orange-500 dark:text-orange-300" />
                  Update View
                </button>
              )}
            {/* Reset button: only show if any filter is applied */}
            {isAnyFilterApplied && (
              <button
                className="px-4 py-2 rounded border border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300 transition shadow-sm flex items-center gap-2 cursor-pointer hover:bg-red-100 dark:hover:bg-red-800"
                onClick={() => {
                  setText("");
                  setSelectedFilterKeys([]);
                  setPaginationParams((prev) => ({
                    ...prev,
                    start_date: undefined,
                    end_date: undefined,
                  }));
                  setCurrentPage(1);
                  if (tableRef.current) {
                    tableRef.current.resetAgGridFilters();
                  }
                  setAppliedViewId(null);
                  setLastAppliedViewFilters(null);
                }}
              >
                <RotateCcw className="w-4 h-4 text-red-500 dark:text-red-300" />
                Reset
              </button>
            )}
            {/* <Button 
              className="px-4 py-2 rounded border border-app-border-primary dark:border-app-border-secondary bg-app-main-background dark:bg-app-background text-app-text-color transition hover:bg-app-border-primary hover:text-white shadow-sm flex items-center gap-2"
              onClick={() => setShowSummaryDialog(true)}
            >
              Add summary
            </Button> */}
          </div>
          <div className="flex gap-2">
            <button
              className="px-4 py-2 rounded border border-app-border-primary dark:border-app-border-secondary bg-app-main-background dark:bg-app-background text-app-text-color transition hover:bg-app-border-primary dark:hover:bg-app-border-secondary hover:text-white dark:hover:text-white shadow-sm flex items-center gap-2"
              onClick={() => exportData("excel")}
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Filter Status */}
      {isLoadingFilters && (
        <div className="mb-2 text-sm text-blue-600">
          Loading saved filters...
        </div>
      )}
      {/* {hasSavedFilters && !isLoadingFilters && (
        <div className="mb-2 text-sm text-green-600">
          ✓ Saved filters available - Click "Restore" to apply them (includes AG Grid column filters)
        </div>
      )} */}

      {/* Table container with improved layout */}
      <div className="w-full overflow-auto h-[calc(100vh-320px)] min-h-[400px] scroller-style bg-white dark:bg-app-main-background text-app-text-color dark:text-app-text-color rounded-lg shadow p-2 mb-6">
        <TableRenderer
          ref={tableRef}
          data={filterTableData(renderTableData, selectedFilterKeys)}
          params={{
            ...paginationParams,
            start_date: paginationParams.start_date
              ? format(paginationParams.start_date, "PPP")
              : "",
            end_date: paginationParams.end_date
              ? format(paginationParams.end_date, "PPP")
              : "",
          }}
          id={formId!}
          error={error}
          isLoading={isLoading}
          onFilterChanged={setAgGridFilters}
        />
      </div>

      {/* Tab bar for views at the bottom */}
      {currentFormViews.length > 0 && (
        <div
          className="w-full bg-white dark:bg-app-background border-t border-gray-200 dark:border-gray-700 flex items-center px-2 py-1 overflow-x-auto gap-1 sticky bottom-0 z-40"
          style={{ minHeight: 48 }}
        >
          {/* Menu icon for all views */}
          <button
            ref={menuButtonRef}
            className="flex items-center justify-center w-10 h-10 rounded hover:bg-gray-200 dark:hover:bg-gray-700 mr-2"
            onClick={(e) => {
              e.stopPropagation();
              setShowViewsMenu((open) => !open);
              if (!showViewsMenu && menuButtonRef.current) {
                const rect = menuButtonRef.current.getBoundingClientRect();
                setMenuPos({
                  left: rect.left + rect.width / 2 + 8,
                  top: rect.bottom,
                });
              }
            }}
            title="Show all views"
          >
            <List className="w-6 h-6 text-gray-600" />
          </button>
          {/* Views menu dropdown (portal) */}
          {showViewsMenu &&
            menuPos &&
            createPortal(
              <div
                style={{
                  position: "fixed",
                  left: menuPos.left + 8,
                  top: menuPos.top - 8,
                  transform: "translate(0, -100%)",
                  minWidth: 180,
                  zIndex: 9999,
                }}
                className="bg-white dark:bg-app-background border border-gray-200 dark:border-gray-700 rounded shadow-lg py-2"
              >
                {currentFormViews.map((view: View) => (
                  <div key={view.id} className="flex items-center group">
                    <button
                      className={`flex-1 flex items-center gap-2 text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 ${
                        appliedViewId === view.id
                          ? "font-semibold text-emerald-600 dark:text-emerald-400 bg-app-background dark:bg-app-background border-l-4 border-emerald-300"
                          : "text-app-text-color dark:text-app-text-color"
                      }`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowViewsMenu(false);
                        handleApplyView(view);
                      }}
                      style={{ border: "none", background: "none" }}
                    >
                      <span className="truncate max-w-[120px]">
                        {view.name}
                      </span>
                    </button>
                    <button
                      className="p-2 text-gray-500 hover:text-indigo-600"
                      title="Edit"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowViewsMenu(false);
                        handleEditView(view);
                      }}
                    >
                      <Pencil className="w-4 h-4" />
                    </button>
                    <button
                      className="p-2 text-red-500 hover:text-red-700"
                      title="Delete"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setShowViewsMenu(false);
                        handleDeleteView(view.id);
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>,
              document.body
            )}
          {/* View tabs */}
          {currentFormViews.map((view: View) => (
            <ViewTab
              key={view.id}
              view={view}
              isActive={appliedViewId === view.id}
              onApply={() => handleApplyView(view)}
              onEdit={() => handleEditView(view)}
              onDelete={() => handleDeleteView(view.id)}
              dropdownOpen={openDropdownViewId === view.id}
              setDropdownOpen={(open: boolean) =>
                setOpenDropdownViewId(open ? view.id : null)
              }
            />
          ))}
        </div>
      )}

      {/* Save/Edit View Modal */}
      {showSaveViewModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white dark:bg-app-main-background rounded-lg shadow-lg p-6 w-full max-w-lg relative">
            <button
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 dark:text-app-text-secondary dark:hover:text-app-text-color"
              onClick={() => setShowSaveViewModal(false)}
            >
              ✕
            </button>
            <h2 className="text-xl font-semibold mb-4 text-app-text-color dark:text-app-text-color">
              {editingViewId ? "Edit View" : "Create View"}
            </h2>
            <input
              className="w-full mb-3 px-3 py-2 border rounded bg-app-main-background dark:bg-app-background text-app-text-color dark:text-app-text-color border-app-border-primary dark:border-app-border-secondary"
              placeholder="Title"
              value={viewTitle}
              onChange={(e) => setViewTitle(e.target.value)}
            />
            <textarea
              className="w-full mb-3 px-3 py-2 border rounded bg-app-main-background dark:bg-app-background text-app-text-color dark:text-app-text-color border-app-border-primary dark:border-app-border-secondary"
              placeholder="Description (optional)"
              value={viewDescription}
              onChange={(e) => setViewDescription(e.target.value)}
            />
            <div className="flex justify-end gap-2">
              <button
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-app-text-color dark:bg-app-border-primary dark:hover:bg-app-border-secondary dark:text-app-text-color cursor-pointer transition-colors"
                onClick={() => setShowSaveViewModal(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded bg-app-border-primary text-white hover:bg-app-primary-button-hover dark:bg-app-border-secondary dark:text-white dark:hover:bg-app-border-primary cursor-pointer transition-colors shadow-sm"
                disabled={
                  !viewTitle.trim() ||
                  createFilterViewMutation.isPending ||
                  updateFilterViewMutation.isPending
                }
                onClick={handleSaveView}
              >
                {createFilterViewMutation.isPending ||
                updateFilterViewMutation.isPending
                  ? "Saving..."
                  : editingViewId
                  ? "Update View"
                  : "Create View"}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Summary Dialog */}
      {showSummaryDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white dark:bg-app-main-background rounded-lg shadow-lg p-6 w-full max-w-lg relative">
            <button
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 dark:text-app-text-secondary dark:hover:text-app-text-color"
              onClick={() => {
                setShowSummaryDialog(false);
                setSummaryName("");
                setSummaryDescription("");
              }}
            >
              ✕
            </button>
            <h2 className="text-xl font-semibold mb-4 text-app-text-color dark:text-app-text-color">
              Create Summary
            </h2>
            <div className="mb-4">
              <label className="block text-sm font-medium text-app-text-color dark:text-app-text-color mb-2">
                Summary Name *
              </label>
              <input
                className="w-full px-3 py-2 border rounded bg-app-main-background dark:bg-app-background text-app-text-color dark:text-app-text-color border-app-border-primary dark:border-app-border-secondary focus:outline-none focus:ring-2 focus:ring-app-border-primary dark:focus:ring-app-border-secondary"
                placeholder="Enter summary name"
                value={summaryName}
                onChange={(e) => setSummaryName(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSubmitSummary();
                  }
                }}
              />
            </div>
            <div className="mb-6">
              <label className="block text-sm font-medium text-app-text-color dark:text-app-text-color mb-2">
                Summary Description *
              </label>
              <textarea
                className="w-full px-3 py-2 border rounded bg-app-main-background dark:bg-app-background text-app-text-color dark:text-app-text-color border-app-border-primary dark:border-app-border-secondary focus:outline-none focus:ring-2 focus:ring-app-border-primary dark:focus:ring-app-border-secondary resize-none"
                placeholder="Enter summary description"
                value={summaryDescription}
                onChange={(e) => setSummaryDescription(e.target.value)}
                rows={3}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-app-text-color dark:bg-app-border-primary dark:hover:bg-app-border-secondary dark:text-app-text-color cursor-pointer transition-colors"
                onClick={() => {
                  setShowSummaryDialog(false);
                  setSummaryName("");
                  setSummaryDescription("");
                }}
              >
                Cancel
              </Button>
              <Button
                className="px-4 py-2 rounded bg-app-border-primary text-white hover:bg-app-primary-button-hover dark:bg-app-border-secondary dark:text-white dark:hover:bg-app-border-primary cursor-pointer transition-colors shadow-sm"
                disabled={!summaryName.trim() || !summaryDescription.trim()}
                onClick={handleSubmitSummary}
              >
                Create Summary
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-app-background">
        <AppPagination
          currentPage={currentPage}
          totalItems={MAX_OFFSET}
          itemsPerPage={LIMIT}
          onPageChange={handlePageChange}
          showInfo={true}
        />
      </div>
    </div>
  );
};

export default FormResponses;
