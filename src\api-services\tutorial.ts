import toast from "react-hot-toast";
import { makeRequest } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/tutorial`;

function getTutorial(subCategoryId: number, appId: number) {
    return makeRequest({
        endpoint: `${baseEndpoint}?sub_category_id=${subCategoryId}&app=${appId}`,
        method: "GET"
    });
}

const useGetTutorial = (subCategoryId: number, appId: number) => {
    return useQuery({
        queryKey: ["tutorial", subCategoryId, appId],
        queryFn: () => getTutorial(subCategoryId, appId),
        enabled: !!subCategoryId && !!appId
    });
};

function getTutorialById(id: number) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "GET"
    });
}

const useGetTutorialById = (id: number) => {
    return useQuery({
        queryKey: ["tutorial", id],
        queryFn: () => getTutorialById(id),
        enabled: !!id
    });
};

function getTutorialBySubCategory() {
    return makeRequest({
        endpoint: `${baseEndpoint}/sub-categories`,
        method: "GET"
    });
}

const useGetTutorialBySubCategory = () => {
    return useQuery({
        queryKey: ["tutorial-sub-categories"],
        queryFn: getTutorialBySubCategory
    });
};

export { useGetTutorial, useGetTutorialById, useGetTutorialBySubCategory };