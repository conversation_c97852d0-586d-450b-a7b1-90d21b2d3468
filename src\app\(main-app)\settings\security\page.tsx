"use client";
import React from "react";
import { Eye, EyeOff, LockKeyhole, ShieldPlus } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import useChangePassword from "@/hooks/useChangePassword";
import { useChangePassword as useChangePasswordAPI } from "@/api-services/forgot_password";
import toast from "react-hot-toast";
import { ChangePasswordForm } from "@/hooks/useChangePassword";
import { useRouter } from "next/navigation";

const Page = () => {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    watch,
    errors,
    showPassword,
    showNewPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleNewPasswordVisibility,
    toggleConfirmPasswordVisibility,
    trigger,
  } = useChangePassword();

  const { mutate: changePassword, isPending } = useChangePasswordAPI();

  const onSubmit = (data: ChangePasswordForm) => {
    if (data.currentPassword === data.newPassword) {
      toast.error("Current and new password are same, please try again.");
      return;
    }
    changePassword(
      {
        oldPassword: data.currentPassword,
        newPassword: data.newPassword,
        confirmNewPassword: data.confirmPassword,
      },
      {
        onSuccess: (response) => {
          if (response?.success === false) {
            // Handle API error response
            const errorMessage = response.error || "Failed to change password";
            toast.error(errorMessage);
            return;
          }
          
          // Only proceed with success flow if the API call was actually successful
          toast.success("Password changed successfully!");
          setTimeout(() => {
            router.push("/login");
          }, 1500);
        },
        onError: (error: any) => {
          const errorMessage = error?.response?.data?.error || "Failed to change password";
          toast.error(errorMessage);
        }
      }
    );
  };

  return (
    <div className="flex items-center justify-center w-full text-app-text-color">
      <div className="flex items-center justify-center w-full py-5 max-w-7xl">
        <div className="border border-app-hero-background max-w-4xl w-full rounded-2xl shadow-lg bg-app-background">
          <h2 className="p-4 px-16 max-[540px]:px-4 flex items-center gap-3 border-b border-app-hero-background text-lg font-semibold">
            <ShieldPlus className="inline-block" /> Change Password
          </h2>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="py-8 px-16 max-[540px]:px-4 flex flex-col gap-6 w-1/2 max-[1024px]:w-3/4 max-[600px]:w-full"
          >
            {/* Current Password */}
            <div>
              <Label className="text-app-text-secondary">
                Current Password <span className="text-red-600">*</span>
              </Label>
              <div className="relative text-app-text-secondary">
                <LockKeyhole className="absolute p-0.5 bottom-2.5 left-2" />
                <Input
                  type={showPassword ? "text" : "password"}
                  className="bg-app-hero-background pl-10"
                  placeholder="Enter your current password"
                  {...register("currentPassword", {
                    required: "Current password is required",
                    onBlur: () => trigger("currentPassword"),
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-app-text-secondary" />
                  ) : (
                    <Eye className="h-4 w-4 text-app-text-secondary" />
                  )}
                </Button>
              </div>
              {errors.currentPassword && (
                <p className="text-red-500 text-sm">
                  {errors.currentPassword.message}
                </p>
              )}
            </div>

            {/* New Password */}
            <div>
              <Label className="text-app-text-secondary">
                New Password <span className="text-red-600">*</span>
              </Label>
              <div className="relative text-app-text-secondary">
                <LockKeyhole className="absolute p-0.5 bottom-2.5 left-2" />

                <Input
                  type={showNewPassword ? "text" : "password"}
                  className="bg-app-hero-background pl-10"
                  placeholder="Enter new password"
                  {...register("newPassword", {
                    required: "New password is required",
                    minLength: {
                      value: 8,
                      message: "Password must be at least 8 characters",
                    },
                    pattern: {
                      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                      message: "Password must include uppercase, lowercase, number and special character",
                    },
                    onBlur: () => trigger("newPassword"),
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={toggleNewPasswordVisibility}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4 text-app-text-secondary" />
                  ) : (
                    <Eye className="h-4 w-4 text-app-text-secondary" />
                  )}
                </Button>
              </div>
              {errors.newPassword && (
                <p className="text-red-500 text-sm">
                  {errors.newPassword.message}
                </p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <Label className="text-app-text-secondary">
                Confirm Password <span className="text-red-600">*</span>
              </Label>
              <div className="relative text-app-text-secondary">
                <LockKeyhole className="absolute p-0.5 bottom-2.5 left-2" />

                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  className="bg-app-hero-background pl-10"
                  placeholder="Confirm new password"
                  {...register("confirmPassword", {
                    required: "Please confirm your password",
                    validate: (value) =>
                      value === watch("newPassword") ||
                      "Passwords do not match",
                    onBlur: () => trigger("confirmPassword"),
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-app-text-secondary" />
                  ) : (
                    <Eye className="h-4 w-4 text-app-text-secondary" />
                  )}
                </Button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="h-8 mb-10 w-fit hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C] rounded-xl "
              disabled={isPending}
            >
              {isPending ? "Changing..." : "Change Password"}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Page;
