import React, { Suspense, useRef, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>P<PERSON>,
  <PERSON>ting<PERSON>,
  Trash2,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { useAppStore } from "@/state-store/app-state-store";
import { useUploadFile } from "@/api-services/form_submission";
import toast from "react-hot-toast";
import { defaultHideFieldState } from "@/state-store/globalForCondition";
import { ThenAction } from "@/types/condition";
import { useUserProfile } from "@/api-services/auth";
import Loader from "../common/loader";

interface FieldWrapperProps {
  id: string;
  dragHandleProps?: any;
  deleteField: (id: string) => void;
  duplicateField: (id: string) => void;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  showTitle?: boolean;
  showDescription?: boolean;
  showFileUpload?: boolean;
  showSettingsButton?: boolean;
  component?: string;
  rightButtons?: boolean;
  titleMedia?: string;
  children: React.ReactNode;
  isPreview?: boolean;
  isEyeCross?: boolean;
  workspace_id: number;
}

const FieldWrapper = ({
  id,
  dragHandleProps,
  deleteField,
  duplicateField,
  fieldIndex,
  component,
  triggerSettingsAction,
  isRequired = false,
  title: initialTitle = "",
  description: initialDescription = "",
  showTitle = true,
  showDescription = true,
  showFileUpload = true,
  showSettingsButton = true,
  rightButtons = false,
  titleMedia: initialTitleMedia = "",
  children,
  isPreview = false,
  isEyeCross = true,
  workspace_id,
}: FieldWrapperProps) => {
  const {
    updateField,
    setFields,
    fields,
    theme,
    conditions,
    setActiveComponent,
  } = useAppStore();
  const { mutate: uploadFile, isPending } = useUploadFile();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [titleMedia, setTitleMedia] = useState(initialTitleMedia);
  const [title, setTitle] = useState(initialTitle);
  const [description, setDescription] = useState(initialDescription);
  const [isTitleEditable, setIsTitleEditable] = useState<boolean>(false);
  const [isDescriptionEditable, setIsDescriptionEditable] =
    useState<boolean>(false);
  const [isHide, setIsHide] = useState(isEyeCross);

  const handleTitleClick = () => {
    setIsTitleEditable(true);
  };

  const handleDescriptionClick = () => {
    setIsDescriptionEditable(true);
  };

  const handleTitleBlur = () => {
    const trimmedTitle = title.trim();
    if (trimmedTitle === "") {
      setTitle(initialTitle);
    } else {
      setTitle(trimmedTitle);
    }
    setFields(
      fields.map((field) => {
        if (field.id === id) {
          return { ...field, title: trimmedTitle || initialTitle };
        }
        return field;
      })
    );
    setIsTitleEditable(false);
  };

  const handleDescriptionBlur = () => {
    if (description.trim() === "") {
      setDescription(initialDescription);
    }
    setFields(
      fields.map((field) => {
        if (field.id === id) {
          return { ...field, description: description };
        }
        return field;
      })
    );
    setIsDescriptionEditable(false);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Don't allow empty strings with only spaces
    if (value.trim() === "" && value !== "") {
      return;
    }
    setTitle(value);
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setDescription(e.target.value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const formData = new FormData();
      formData.append("upload", file as Blob);

      uploadFile(
        { formData, workspace_id },
        {
          onSuccess: (response) => {
            const fileUrl = response?.data?.fileUrl;
            if (fileUrl) {
              setTitleMedia(fileUrl);
              updateField(id, { titleMedia: fileUrl });
              toast.success("File uploaded successfully");
            } else {
              toast.error("Failed to get file URL from server");
            }
          },
          onError: (error) => {
            toast.error("Failed to upload file");
            console.error("Upload error:", error);
          },
        }
      );
    }
  };

  const handleDeleteFile = () => {
    setTitleMedia("");
    updateField(id, { titleMedia: "" });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleEyeClick = () => {
    const newHideState = !isHide;
    setIsHide(newHideState);
    defaultHideFieldState[id] = newHideState;
    updateField(id, { isHide: newHideState });
  };

  const handleDeleteField = () => {
    deleteField(id);
    setActiveComponent(null);
  };

  return (
    <Suspense fallback={<Loader />}>
      <div
        className={`${
          isPreview
            ? ""
            : "shadow-[0_2px_4px_rgba(0,0,0,0.05),0_-2px_4px_rgba(0,0,0,0.05),2px_0_4px_rgba(0,0,0,0.05),-2px_0_4px_rgba(0,0,0,0.05)]"
        } flex flex-col items-center w-full gap-2 px-4 py-6 rounded-md bg-app-background ${
          theme === "dark" ? "border" : ""
        } ${isHide ? "opacity-50" : ""}`}
      >
        {!isPreview && (
          <div
            className="rotate-90 font-semibold text-app-text-color -mt-6"
            {...dragHandleProps}
          >
            ⋮⋮
          </div>
        )}
        <div
          className={`flex flex-row items-start w-full ${
            rightButtons ? "justify-end space-x-4" : "justify-between"
          }`}
        >
          {showTitle && !rightButtons && (
            <h3 className="text-lg font-semibold flex flex-row gap-1 w-2/3">
              {!isPreview && <span>{fieldIndex}.</span>}
              {!isPreview && isTitleEditable ? (
                <input
                  type="text"
                  value={title}
                  onChange={handleTitleChange}
                  onBlur={handleTitleBlur}
                  className="text-lg font-semibold w-full bg-transparent border-none focus:ring-0"
                />
              ) : (
                <span onClick={isPreview ? undefined : handleTitleClick}>
                  {title}
                </span>
              )}
              {isRequired && <span className="text-red-500 text-3xl">*</span>}
            </h3>
          )}
          {!isPreview && (
            <div className="space-x-3 flex flex-row">
              <div className="relative group flex items-center justify-center">
                <button
                  className="relative flex items-center justify-center"
                  onClick={handleEyeClick}
                >
                  {isHide ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
                <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block">
                  <div className="bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                    {!isHide ? "Default Show" : "Default Hide"}
                  </div>
                  <div className="absolute left-1/2 -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                </div>
              </div>
              {showFileUpload && !titleMedia && (
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isPending}
                  className="relative"
                >
                  {isPending ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <ImagePlus className="w-5 h-5" />
                  )}
                </button>
              )}
              {showSettingsButton && (
                <button
                  onClick={() => triggerSettingsAction(id, `${component}`)}
                >
                  <Settings className="w-5 h-5" />
                </button>
              )}
              <button onClick={() => duplicateField(id)}>
                <Copy className="w-5 h-5" />
              </button>
              <button onClick={handleDeleteField}>
                <Trash2 className="w-5 h-5 text-red-600" />
              </button>
            </div>
          )}
        </div>
        {showDescription && (
          <p className="text-left w-full text-sm text-app-text-secondary">
            {isDescriptionEditable ? (
              <textarea
                value={description}
                onChange={handleDescriptionChange}
                onBlur={handleDescriptionBlur}
                className="w-full p-2 border rounded-md focus:ring-0 bg-transparent"
              />
            ) : (
              <span
                onClick={() => {
                  if (isPreview) return;
                  handleDescriptionClick();
                }}
              >
                {description}
              </span>
            )}
          </p>
        )}
        {showFileUpload && titleMedia && (
          <div className="relative w-fit mt-2 mb-4">
            <div className="w-full">
              {titleMedia.toLowerCase().match(/\.(mp4|webm|ogg)$/) ? (
                <video
                  src={titleMedia}
                  controls
                  className="w-full h-auto rounded-md"
                />
              ) : (
                <img
                  src={titleMedia}
                  alt="Uploaded"
                  className="w-auto h-auto max-h-[300px] border rounded-md"
                />
              )}
            </div>
            {!isPreview && (
              <button
                onClick={handleDeleteFile}
                className="absolute top-0 right-0 m-2 p-1 text-red-600 bg-white rounded-full hover:bg-red-100"
              >
                <Trash2 />
              </button>
            )}
          </div>
        )}
        {showFileUpload && (
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            onChange={handleFileChange}
            accept="image/*,video/*"
          />
        )}
        {children}
      </div>
    </Suspense>
  );
};

export default FieldWrapper;
