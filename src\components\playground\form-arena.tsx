import React, {
  useCallback,
  useState,
  useEffect,
  useRef,
  Suspense,
} from "react";
import { useDropzone } from "react-dropzone";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { useAppStore } from "@/state-store/app-state-store";
import { useSearchParams } from "next/navigation";
import { toolContainersElement } from "@/fields/fieldsData";
import { useGetFormDetails, useUpdateForm } from "@/api-services/form";
import toast from "react-hot-toast";
import { useGetFormFields } from "@/api-services/form_fields";
import { Loader2, Trash2, Upload } from "lucide-react";
import { useUploadFile } from "@/api-services/form_submission";
import Image from "next/image";
import { QueryKeys } from "@/api-services/utils";
import { useQueryClient } from "@tanstack/react-query";
import MultiPageFormPreview from "./MultiPageFormPreview";
import { ThankYouResponseSelect } from "./thank-you-response-select";
import { Button } from "@/components/ui/button";
import {
  useGetTemplateDetails,
  useUpdateTemplate,
} from "@/api-services/form-templates";
import {
  conditionSet,
  defaultHideFieldState,
} from "@/state-store/globalForCondition";
import { useGetThankYouPage } from "@/api-services/form_setting";
import Loader from "../common/loader";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";

const FormArena = () => {
  const {
    fields,
    addField,
    reorderFields,
    setActiveComponent,
    headerImage,
    setHeaderImage,
    setFields,
    backgroundImage,
    setBackgroundImage,
    backgroundColor,
    setBackgroundColor,
    headingColor,
    setHeadingColor,
    setDescriptionColor,
    descriptionColor,
    fontFamily,
    setFontFamily,
    submitButtonBgColor,
    setSubmitButtonBgColor,
    submitButtonTextColor,
    setSubmitButtonTextColor,
    submitButtonText,
    setSubmitButtonText,
    submitButtonPosition,
    setSubmitButtonPosition,
    setConditions,
    conditions,
    setThankYouConditions,
    thankYouConditions,
    formDescription,
    setFormDescription,
  } = useAppStore();
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");
  const mode = searchParams.get("mode");
  const { mutate: updateForm, isPending } = useUpdateForm(formId!);
  const { data: formFields, isLoading } = useGetFormFields(formId!);
  const { mutate: uploadFile } = useUploadFile();
  const [isEditingHeading, setIsEditingHeading] = useState<boolean>(false);
  const [isEditingDescription, setIsEditingDescription] =
    useState<boolean>(false);
  const fieldTrackerRef = useRef<boolean>(false);
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const isModePreview = mode === "preview";
  const isMultiPage = formType === "multipage";
  const containerRef = useRef<HTMLDivElement>(null);
  const prevFieldsLengthRef = useRef<number>(fields.length);
  const editorRef = useRef<HTMLDivElement>(null);

  const isTemplate = formType === "template";

  const { data: formDetailsResponse } = useGetFormDetails(formId!);

  const { data: templateDetails } = useGetTemplateDetails(formId!);

  const { data: thankYouPageData } = useGetThankYouPage(formId!);

  const workspace_id = formDetailsResponse?.data?.form?.workspace_id;

  const { mutate: updateTemplate, isPending: isUpdatingTemplate } =
    useUpdateTemplate();
  const formDetails = formDetailsResponse?.data?.form;
  const templateData = templateDetails?.data?.template;

  const [formHeading, setFormHeading] = useState<string>("");

  useEffect(() => {
    if (formDetails) {
      const initialHeading = formDetails.heading || "Untitled Form";
      const initialDescription =
        formDetails.description && formDetails.description !== "<p><br></p>"
          ? formDetails.description
          : "<p>Add your form description here</p>";

      if (!formDetails.heading && !isTemplate) {
        updateForm(
          {
            formheading: initialHeading,
            description: initialDescription,
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: QueryKeys.FORM_DETAILS(formId!),
              });
            },
          }
        );
      }
      setFormHeading(initialHeading);
      setFormDescription(initialDescription);
    }
  }, [formDetails, isTemplate, formId]);

  // Only set defaultHideFieldState when fields are initially loaded, not on every render
  useEffect(() => {
    fields.forEach((field) => {
      // Only set if not already set to preserve condition states
      if (defaultHideFieldState[field.id] === undefined) {
        defaultHideFieldState[field.id] = field?.isHide || false;
      }
    });
  }, [fields]);

  useEffect(() => {
    if (isTemplate) {
      if (templateData?.template_data?.fields) {
        setFields(templateData?.template_data?.fields);
        fieldTrackerRef.current = true;
      }
    } else {
      if (formFields?.data?.fields) {
        setFields(formFields.data.fields);
        fieldTrackerRef.current = true;
      }
    }
  }, [
    formFields?.data?.fields,
    templateData?.template_data?.fields,
    setFields,
  ]);

  useEffect(() => {
    if (formFields?.data?.condition && conditions?.length === 0) {
      if (!conditionSet.condition.includes(formId!)) {
        setConditions(formFields.data.condition);
        conditionSet.condition.push(formId!);
      }
    }
  }, [formFields?.data?.condition]);

  useEffect(() => {
    if (
      thankYouPageData?.data?.data?.thank_you_data &&
      thankYouConditions?.length === 0
    ) {
      if (!conditionSet.thank_you_condition.includes(formId!)) {
        setThankYouConditions(thankYouPageData?.data?.data?.thank_you_data);
        conditionSet.thank_you_condition.push(formId!);
      }
    }
  }, [thankYouPageData?.data?.data?.thank_you_data]);

  useEffect(() => {
    setHeaderImage(formDetails?.header_img || null);
    setBackgroundImage(formDetails?.bg_image || null);
    setBackgroundColor(formDetails?.bg_color || null);
    setHeadingColor(formDetails?.heading_color);
    setDescriptionColor(formDetails?.description_color);
    setFontFamily(formDetails?.font_family);
    setSubmitButtonText(formDetails?.button_properties?.text);
    setSubmitButtonBgColor(formDetails?.button_properties?.backgroundColor);
    setSubmitButtonTextColor(formDetails?.button_properties?.textColor);
    setSubmitButtonPosition(formDetails?.button_properties?.position);
  }, [formDetails]);

  function handleBlur() {
    const updatedTitle =
      !formDetails?.title || formDetails.title === "Untitled Form"
        ? formHeading
        : formDetails?.title;

    if (!formHeading.trim()) {
      setFormHeading("Untitled Form");
      setFormDescription("<p>Add your form description here</p>");
    } else {
      // Get the current content from the editor
      let editorContent =
        document.querySelector(".ql-editor")?.innerHTML || formDescription;
      if (editorContent === "<p><br></p>") {
        editorContent = "<p>Add your form description here</p>";
      }

      if (isTemplate) {
        updateTemplate(
          {
            templateId: formId!,
            data: {
              ...templateData,
              template_data: {
                ...templateData?.template_data,
                formheading: formHeading,
                description: editorContent, // Use the HTML content from editor
              },
            },
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: ["form-templates-details", formId!],
              });
              toast.success("Form details updated successfully");
              setIsEditingHeading(false);
              setIsEditingDescription(false);
            },
          }
        );
      } else {
        updateForm(
          {
            title: updatedTitle,
            formheading: formHeading,
            description: editorContent, // Use the HTML content from editor
            header_img: headerImage,
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: QueryKeys.FORM_DETAILS(formId!),
              });
              toast.success("Form details updated successfully");
              setIsEditingHeading(false);
              setIsEditingDescription(false);
            },
            onError: () => {
              toast.error("Something went wrong");
            },
          }
        );
      }
    }
  }

  useEffect(() => {
    if (containerRef.current && fields.length > prevFieldsLengthRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
    prevFieldsLengthRef.current = fields.length;
  }, [fields]);

  const handleDragEnd = (result: any) => {
    const { source, destination } = result;
    if (!destination) return;
    if (source.index === destination.index) return;
    reorderFields(source.index, destination.index);
  };

  const onDrop = useCallback(
    (acceptedItems: any) => {
      acceptedItems.forEach((item: any) => {
        try {
          const parsedItem = JSON.parse(item);
          addField(parsedItem);
        } catch (error) {
          console.error("Invalid field format", error);
        }
      });
    },
    [addField]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: { "application/json": [] },
    noClick: true,
    noKeyboard: true,
  });

  const handleImageUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      fileInputRef.current.click();
    }
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }
    setIsUploading(true);

    const formData = new FormData();
    formData.append("upload", file as Blob);

    uploadFile(
      { formData, workspace_id },
      {
        onSuccess: (response) => {
          const fileUrl = response?.data?.fileUrl;
          if (fileUrl) {
            setHeaderImage(fileUrl);
            if (isTemplate) {
              updateTemplate(
                {
                  templateId: formId!,
                  data: {
                    ...templateData,
                    template_data: {
                      ...templateData?.template_data,
                      header_img: fileUrl,
                    },
                  },
                },
                {
                  onSuccess: () => {
                    queryClient.invalidateQueries({
                      queryKey: ["form-templates-details", formId!],
                    });
                  },
                }
              );
            } else {
              updateForm(
                {
                  formheading: formHeading,
                  description: formDescription,
                  header_img: fileUrl,
                },
                {
                  onSuccess: () => {
                    toast.success("Image uploaded successfully");
                  },
                  onError: () => {
                    toast.error("Failed to upload image");
                  },
                }
              );
            }
          } else {
            toast.error("Failed to retrieve image URL");
          }
        },
        onError: () => {
          toast.error("Failed to upload image");
        },
        onSettled: () => {
          setIsUploading(false);
        },
      }
    );
  };

  const handleRemoveImage = () => {
    if (headerImage || templateData?.template_data?.header_img) {
      if (isTemplate) {
        updateTemplate(
          {
            templateId: formId!,
            data: {
              ...templateData,
              template_data: {
                ...templateData?.template_data,
                header_img: "",
              },
            },
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: ["form-templates-details", formId!],
              });
              setHeaderImage("");
              toast.success("Image removed successfully");
            },
          }
        );
      } else {
        updateForm(
          {
            header_img: "",
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: QueryKeys.FORM_DETAILS(formId!),
              });
              setHeaderImage("");
              toast.success("Image removed successfully");
            },
            onError: (error) => {
              console.error("Error removing image:", error);
              toast.error("Failed to remove image");
            },
          }
        );
      }
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  useEffect(() => {
    return () => {
      if (headerImage) {
        URL.revokeObjectURL(headerImage);
      }
    };
  }, [headerImage]);

  const descriptionModules = {
    toolbar: [
      [{ size: [] }],
      ["bold", "italic", "underline", "strike"],
      [{ align: ["", "center", "right", "justify"] }],
      [{ color: [] }, { background: [] }],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["link"],
      ["clean"],
    ],
  };

  const descriptionFormats = [
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "align",
    "list",
    "bullet",
    "indent",
    "color",
    "background",
    "link",
  ];

  useEffect(() => {
    if (!isEditingDescription) return;
    function handleClickOutside(event: MouseEvent) {
      if (
        editorRef.current &&
        !editorRef.current.contains(event.target as Node)
      ) {
        handleBlur();
        setIsEditingDescription(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isEditingDescription]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="w-10 h-10 animate-spin" />
      </div>
    );
  }

  return (
    <Suspense fallback={<Loader />}>
      <div
        {...getRootProps()}
        ref={containerRef}
        className={`max-w-4xl w-full bg-app-background overflow-auto px-3 ${
          isMultiPage && isModePreview ? "pb-0 h-fit" : "pb-4 h-full"
        } pt-4 mt-4 flex flex-col items-center gap-6 scroller-style rounded-lg relative ${
          isModePreview ? "mb-20" : "mb-2"
        }`}
        style={{
          backgroundImage:
            backgroundImage || templateData?.template_data?.bg_image
              ? `url(${
                  backgroundImage || templateData?.template_data?.bg_image
                })`
              : "none",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          backgroundColor:
            !backgroundImage && !templateData?.template_data?.bg_image
              ? backgroundColor || templateData?.template_data?.bg_color
              : "transparent",
        }}
      >
        {/* Add a semi-transparent overlay when background image is present */}
        {backgroundImage && (
          <div
            className="absolute inset-0 bg-white bg-opacity-20 pointer-events-none"
            style={{ zIndex: 0 }}
          />
        )}

        {/* Wrap the content in a relative container to appear above the overlay */}
        <div className="relative w-full" style={{ zIndex: 1 }}>
          <input {...getInputProps()} />

          {/* Render different views based on form type and preview mode */}
          {isModePreview && isMultiPage ? (
            <MultiPageFormPreview
              fields={fields.map((field) => ({
                ...field,
                validationValue: field.validationValue || undefined,
              }))}
              headerImage={headerImage}
              formHeading={formHeading}
              formDescription={formDescription}
              isUploading={isUploading}
              isModePreview={isModePreview}
              handleRemoveImage={handleRemoveImage}
              handleImageUploadClick={handleImageUploadClick}
              fileInputRef={fileInputRef}
              setActiveComponent={setActiveComponent}
              descriptionColor={
                descriptionColor ||
                templateData?.template_data?.description_color ||
                "var(--app-text-secondary)"
              }
              fontFamily={
                fontFamily ||
                templateData?.template_data?.font_family ||
                "Raleway"
              }
              workspace_id={workspace_id}
            />
          ) : (
            <>
              <div className="flex flex-col items-center text-center w-full gap-1">
                {/* Hidden file input */}
                <input
                  type="file"
                  ref={fileInputRef}
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                {/* Header Image Section */}
                {headerImage || templateData?.template_data?.header_img ? (
                  <div className="relative w-full max-h-52 mb-4 overflow-hidden rounded-lg border">
                    <Image
                      src={
                        headerImage || templateData?.template_data?.header_img
                      }
                      alt="Header"
                      className="w-full h-auto"
                      height={100}
                      width={100}
                      quality={100}
                      layout="responsive"
                      objectFit="cover"
                    />
                    {!isModePreview && (
                      <button
                        className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-700"
                        onClick={handleRemoveImage}
                        disabled={isUpdatingTemplate}
                      >
                        {isUpdatingTemplate ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Trash2 size={16} />
                        )}
                      </button>
                    )}
                  </div>
                ) : (
                  !isModePreview && (
                    <div className="flex items-end justify-end w-full mb-6">
                      <button
                        className="flex items-center gap-2 text-app-background px-2 py-1 rounded-lg bg-app-text-color transition text-sm"
                        onClick={handleImageUploadClick}
                        disabled={isUploading}
                      >
                        {isUploading ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin" />{" "}
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload size={14} /> Upload Banner
                          </>
                        )}
                      </button>
                    </div>
                  )
                )}

                <h2
                  className="text-3xl font-bold w-full px-3"
                  style={{
                    color:
                      headingColor ||
                      templateData?.template_data?.heading_color ||
                      "var(--app-text-color)",
                    fontFamily: `var(--font-${(
                      fontFamily || templateData?.template_data?.font_family
                    )
                      ?.toLowerCase()
                      .replace(/\s+/g, "-")})`,
                  }}
                >
                  {isEditingHeading ? (
                    <input
                      type="text"
                      value={formHeading}
                      onChange={(e) => setFormHeading(e.target.value)}
                      onBlur={handleBlur}
                      className="p-2 w-full text-center bg-app-background mb-1"
                      disabled={isModePreview || isPending}
                      style={{
                        color:
                          headingColor ||
                          templateData?.template_data?.heading_color ||
                          "var(--app-text-color)",
                      }}
                    />
                  ) : (
                    <span
                      onClick={() => {
                        setIsEditingHeading(true);
                        setFormHeading(
                          formDetails?.heading ||
                            templateData?.template_data?.formheading ||
                            formHeading
                        );
                      }}
                      className="cursor-pointer"
                      style={{
                        color:
                          headingColor ||
                          templateData?.template_data?.heading_color ||
                          "var(--app-text-color)",
                      }}
                    >
                      {formDetails?.heading ||
                        templateData?.template_data?.formheading ||
                        formHeading}
                    </span>
                  )}
                </h2>
                <div
                  className="text-sm text-app-text-secondary w-full px-3"
                  style={{
                    color:
                      descriptionColor ||
                      templateData?.template_data?.description_color ||
                      "var(--app-text-secondary)",
                    fontFamily: `var(--font-${(
                      fontFamily || templateData?.template_data?.font_family
                    )
                      ?.toLowerCase()
                      .replace(/\s+/g, "-")})`,
                  }}
                >
                  {isEditingDescription ? (
                    <div
                      ref={editorRef}
                      className="form-description-editor-container"
                    >
                      <div className="bg-app-background rounded-lg overflow-visible mb-8">
                        <ReactQuill
                          theme="snow"
                          value={formDescription}
                          onChange={(content) => {
                            // Store the full HTML content
                            setFormDescription(content);
                          }}
                          className="bg-app-background"
                          modules={descriptionModules}
                          formats={descriptionFormats}
                          bounds=".form-description-editor-container"
                          style={{ height: "180px" }}
                        />
                      </div>
                    </div>
                  ) : (
                    <div
                      className="ql-editor p-0 text-sm text-app-text-secondary w-full px-3 cursor-pointer"
                      style={{
                        color:
                          descriptionColor ||
                          templateData?.template_data?.description_color ||
                          "var(--app-text-secondary)",
                        fontFamily: `var(--font-${(
                          fontFamily || templateData?.template_data?.font_family
                        )
                          ?.toLowerCase()
                          .replace(/\s+/g, "-")})`,
                      }}
                      dangerouslySetInnerHTML={{ __html: formDescription }}
                      onClick={() => {
                        setIsEditingDescription(true);
                        setFormDescription(
                          formDetails?.description ||
                            templateData?.template_data?.description ||
                            formDescription
                        );
                      }}
                    />
                  )}
                </div>
              </div>

              {fields?.length === 0 && (
                <div className="w-full flex items-center justify-center border-2 border-app-hero-background text-app-text-secondary border-dashed rounded-md text-lg font-bold h-48 min-h-40 mt-6">
                  Add Elements From Field Panel
                </div>
              )}

              {/* Render fields based on form type */}
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="droppable">
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className="w-full space-y-6 mt-6"
                      style={{
                        fontFamily: `var(--font-${(
                          fontFamily || templateData?.template_data?.font_family
                        )
                          ?.toLowerCase()
                          .replace(/\s+/g, "-")})`,
                      }}
                    >
                      {fields?.map((field, index) => (
                        <Draggable
                          key={field.name + index}
                          draggableId={field.name + index}
                          index={index}
                        >
                          {(provided) => {
                            const FormElement =
                              toolContainersElement[
                                field.component as keyof typeof toolContainersElement
                              ];
                            return (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className="flex items-center gap-2 px-3 w-full"
                              >
                                {FormElement ? (
                                  <FormElement
                                    radioOptions={[]}
                                    key={field.id}
                                    dragHandleProps={provided.dragHandleProps}
                                    triggerSettingsAction={(
                                      id: string,
                                      type: string
                                    ) => setActiveComponent({ id, type })}
                                    fieldIndex={index + 1}
                                    isPreview={isModePreview}
                                    workspace_id={workspace_id}
                                    {...field}
                                  />
                                ) : (
                                  <div key={field.name}>
                                    Invalid Field: No component mapped
                                  </div>
                                )}
                              </div>
                            );
                          }}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
              <div
                className={`flex mt-6 px-3 ${
                  submitButtonPosition === "left"
                    ? "justify-start"
                    : submitButtonPosition === "center"
                    ? "justify-center"
                    : "justify-end"
                }`}
              >
                <Button
                  type="button"
                  disabled={isPending || isModePreview}
                  style={{
                    backgroundColor:
                      submitButtonBgColor || "var(--color-reverse-universal)",
                    color: submitButtonTextColor || "var(--color-universal)",
                  }}
                >
                  {submitButtonText || "Submit"}
                </Button>
              </div>
              {!isModePreview && <ThankYouResponseSelect />}
            </>
          )}
        </div>
      </div>

      {isModePreview && (
        <div className="fixed bottom-0 w-full flex items-center justify-center text-center text-sm text-gray-500 py-4 border-t bg-white">
          <span>Powered by</span>
          <Image
            src="/logo.png"
            alt="Automate Business Logo"
            height={100}
            width={100}
            quality={100}
            className="h-8 w-auto ml-2"
          />
        </div>
      )}
    </Suspense>
  );
};

export default FormArena;
