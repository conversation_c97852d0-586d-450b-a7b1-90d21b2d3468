import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface GooglePickerDebugProps {
  credentialId?: string;
}

interface DebugInfo {
  gapiLoaded: boolean;
  pickerLoaded: boolean;
  windowOrigin: string;
  userAgent: string;
  cookiesEnabled: boolean;
  localStorageEnabled: boolean;
  thirdPartyCookiesEnabled: boolean;
  networkConnectivity: boolean;
}

const GooglePickerDebug: React.FC<GooglePickerDebugProps> = ({ credentialId }) => {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const runDiagnostics = async () => {
    setIsChecking(true);
    
    const info: DebugInfo = {
      gapiLoaded: !!window.gapi,
      pickerLoaded: !!window.google?.picker,
      windowOrigin: window.location.origin,
      userAgent: navigator.userAgent,
      cookiesEnabled: navigator.cookieEnabled,
      localStorageEnabled: false,
      thirdPartyCookiesEnabled: false,
      networkConnectivity: false,
    };

    // Test localStorage
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      info.localStorageEnabled = true;
    } catch (e) {
      info.localStorageEnabled = false;
    }

    // Test network connectivity
    try {
      const response = await fetch('https://www.google.com/favicon.ico', { 
        mode: 'no-cors',
        cache: 'no-cache'
      });
      info.networkConnectivity = true;
    } catch (e) {
      info.networkConnectivity = false;
    }

    // Test third-party cookies (simplified check)
    try {
      // This is a basic check - in reality, third-party cookie detection is complex
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = 'https://accounts.google.com/favicon.ico';
      document.body.appendChild(iframe);
      
      setTimeout(() => {
        try {
          // If we can access the iframe without errors, third-party cookies might be enabled
          info.thirdPartyCookiesEnabled = true;
          document.body.removeChild(iframe);
        } catch (e) {
          info.thirdPartyCookiesEnabled = false;
          document.body.removeChild(iframe);
        }
      }, 1000);
    } catch (e) {
      info.thirdPartyCookiesEnabled = false;
    }

    setDebugInfo(info);
    setIsChecking(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getRecommendations = () => {
    if (!debugInfo) return [];

    const recommendations: string[] = [];

    if (!debugInfo.gapiLoaded) {
      recommendations.push("Google API script is not loaded. Check your internet connection and try refreshing the page.");
    }

    if (!debugInfo.pickerLoaded) {
      recommendations.push("Google Picker API is not loaded. This might be due to network issues or script blocking.");
    }

    if (!debugInfo.cookiesEnabled) {
      recommendations.push("Cookies are disabled. Please enable cookies in your browser settings.");
    }

    if (!debugInfo.localStorageEnabled) {
      recommendations.push("Local storage is disabled. Please enable local storage in your browser settings.");
    }

    if (!debugInfo.thirdPartyCookiesEnabled) {
      recommendations.push("Third-party cookies might be blocked. Consider allowing third-party cookies for Google services.");
    }

    if (!debugInfo.networkConnectivity) {
      recommendations.push("Network connectivity issues detected. Check your internet connection.");
    }

    if (debugInfo.userAgent.includes('Chrome') && debugInfo.userAgent.includes('SameSite')) {
      recommendations.push("Chrome's SameSite cookie policy might be affecting Google Picker. Try using a different browser or updating Chrome.");
    }

    return recommendations;
  };

  if (!debugInfo) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Running diagnostics...</span>
        </div>
      </div>
    );
  }

  const recommendations = getRecommendations();

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-800">Google Picker Diagnostics</h3>
        <Button
          onClick={runDiagnostics}
          disabled={isChecking}
          size="sm"
          variant="outline"
        >
          {isChecking ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
        <div className="flex items-center gap-2">
          {getStatusIcon(debugInfo.gapiLoaded)}
          <span>Google API Loaded</span>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(debugInfo.pickerLoaded)}
          <span>Picker API Loaded</span>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(debugInfo.cookiesEnabled)}
          <span>Cookies Enabled</span>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(debugInfo.localStorageEnabled)}
          <span>Local Storage</span>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(debugInfo.networkConnectivity)}
          <span>Network Connectivity</span>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(debugInfo.thirdPartyCookiesEnabled)}
          <span>Third-party Cookies</span>
        </div>
      </div>

      <div className="text-xs text-gray-600">
        <div><strong>Origin:</strong> {debugInfo.windowOrigin}</div>
        <div><strong>Credential ID:</strong> {credentialId || 'Not provided'}</div>
      </div>

      {recommendations.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-amber-700">
            <AlertCircle className="h-4 w-4" />
            <span className="font-medium">Recommendations:</span>
          </div>
          <ul className="text-sm text-gray-700 space-y-1 ml-6">
            {recommendations.map((rec, index) => (
              <li key={index} className="list-disc">{rec}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="text-xs text-gray-500 border-t pt-2">
        <details>
          <summary className="cursor-pointer hover:text-gray-700">Show technical details</summary>
          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default GooglePickerDebug;
