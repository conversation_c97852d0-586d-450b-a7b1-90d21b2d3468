"use client";

import { checkAuth } from "@/api-services/utils";
import AuthCommon from "@/components/auth/AuthCommon";
import SignUpForm from "@/components/auth/SignUpForm";
// import { checkAndRefreshTokenIfNeeded } from "@/api-services/utils";
import { useRouter } from "next/navigation";
import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";

// Client component to handle search params
const SignUpContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const refferal = searchParams?.get("ref");

  const handleButtonClick = async () => {
    const isAuthenticated = await checkAuth();
    if (isAuthenticated) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen h-full">
      <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
        <AuthCommon />
        <SignUpForm refferal={refferal || undefined} />
      </section>
    </div>
  );
};

// Main page component
const Page = () => {
  return (
    <Suspense fallback={
      <div className="flex flex-col items-center justify-center min-h-screen h-full">
        <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
          <AuthCommon />
          <div className="flex items-center justify-center">
            <div className="animate-pulse">Loading...</div>
          </div>
        </section>
      </div>
    }>
      <SignUpContent />
    </Suspense>
  );
};

export default Page;