import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

interface Role {
  id: string;
  name: string;
  description: string;
  usersCount: number;
}

interface RolesListProps {
  roles: Role[];
  selectedRole: Role | null;
  onRoleSelect: (role: Role) => void;
  onDeleteRole: (role: Role) => Promise<void>;
}

const RolesList = ({
  roles,
  selectedRole,
  onRoleSelect,
  onDeleteRole,
}: RolesListProps) => {
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = (e: React.MouseEvent, role: Role) => {
    e.stopPropagation();
    setRoleToDelete(role);
  };

  const handleConfirmDelete = async () => {
    if (!roleToDelete) return;

    try {
      setIsDeleting(true);
      await onDeleteRole(roleToDelete);
    } catch (error) {
      console.error("Error deleting role:", error);
    } finally {
      setIsDeleting(false);
      setRoleToDelete(null);
    }
  };

  return (
    <>
      <div className="w-full bg-app-hero-background rounded-xl border shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Available Roles</h3>
        <div className="space-y-3">
          {roles.map((role) => (
            <div
              key={role.id}
              className={`p-4 rounded-lg border cursor-pointer transition-colors hover:border-emerald-300 ${
                selectedRole?.id === role.id
                  ? "bg-app-hero-background hover:bg-app-sidebar-hover-active border-app-border-primary"
                  : "bg-app-main-background border-transparent"
              }`}
              onClick={() => onRoleSelect(role)}
            >
              <div className="flex items-center justify-between mb-1">
                <h4 className="font-medium text-app-text-color">{role.name}</h4>
                <div className="flex items-center gap-2">
                  {role.name.toLowerCase() !== "admin" && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleDeleteClick(e, role)}
                      disabled={isDeleting}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              </div>
              <p className="text-sm text-app-text-secondary mb-2">
                {role.description}
              </p>
              <div className="flex items-center gap-1">
                <div className="bg-gray-900 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {role.usersCount}
                </div>
                <span className="text-sm text-app">Users Assigned</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <AlertDialog
        open={!!roleToDelete}
        onOpenChange={() => !isDeleting && setRoleToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete this role?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              role
              {roleToDelete?.usersCount
                ? ` and affect ${roleToDelete.usersCount} users.`
                : "."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              className="bg-[#1f311c] hover:bg-[#1f311c]/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default RolesList;
