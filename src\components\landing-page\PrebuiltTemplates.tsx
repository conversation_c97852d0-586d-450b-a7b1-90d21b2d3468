import Image from "next/image";
import { useGetCategories, useGetPublicTemplatesByCategory } from "@/api-services/form-templates";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

type Category = {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
};

type Template = {
  id: string;
  name: string;
  description: string;
  image_url: string;
  template_data: {
    type: string;
    title: string;
    fields: any[];
    heading: string;
    header_img: string;
    description: string;
  };
  created_at: string;
  status: string;
};

interface PrebuiltTemplatesProps {
  isAuthenticated: boolean;
}

export default function PrebuiltTemplates({ isAuthenticated }: PrebuiltTemplatesProps) {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { data: categoriesData } = useGetCategories();
  const { data: templatesData } = useGetPublicTemplatesByCategory(selectedCategory || "");

  const categories: Category[] = categoriesData?.data?.categories || [];
  const templates: Template[] = templatesData?.data?.templates || [];

  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0].id);
    }
  }, [categories, selectedCategory]);

  const handleViewAllClick = () => {
    if (isAuthenticated) {
      router.push("/home/<USER>");
    } else {
      router.push("/login");
    }
  };

  return (
    <section className="py-20 bg-white" id="templates">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header with underline */}
        <div className="flex flex-col items-center mb-8">
          <h2 className="text-3xl font-bold text-center">
            Pre-built templates
          </h2>
          <Image
            src="/underline.png"
            alt=""
            className="w-40 max-w-full"
            width={160}
            height={16}
            quality={100}
          />
        </div>
        {/* Category filter buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((cat: Category, idx: number) => (
            <button
              key={cat.id}
              onClick={() => setSelectedCategory(cat.id)}
              className={`px-7 py-2 rounded-full font-semibold transition-colors text-base focus:outline-none focus:ring-2 focus:ring-green-700 focus:ring-offset-2 ${
                selectedCategory === cat.id
                  ? "bg-green-700 text-white"
                  : "bg-white border border-gray-400 text-gray-700 hover:bg-green-50"
              }`}
            >
              {cat.name}
            </button>
          ))}
        </div>
        {/* Template cards */}
        <div className="grid max-[500px]:grid-cols-1 max-[768px]:grid-cols-2 grid-cols-3 gap-10 mb-8 place-items-center">
          {templates.slice(0, 3).map((template: Template) => (
            <div
              key={template.id}
              className="bg-white rounded-2xl shadow p-6 flex flex-col items-center w-full max-w-xs"
            >
              <div className="w-full h-64 bg-gray-100 rounded-lg overflow-hidden mb-6 flex items-center justify-center">
                <Image
                  src={template.image_url}
                  alt={template.name}
                  width={320}
                  height={256}
                  className="object-cover w-full h-full"
                />
              </div>
              <h3 className="text-lg font-semibold mb-2">{template.name}</h3>
              <p className="text-gray-600 text-sm mb-4">{template.description}</p>
            </div>
          ))}
        </div>
        {/* View all */}
        <div className="text-center mt-8">
          <button 
            onClick={handleViewAllClick}
            className="text-green-700 font-bold underline text-xl"
          >
            View all
          </button>
        </div>
      </div>
    </section>
  );
}
