export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);

  // Check if it's an ISO string (contains 'T' and has time component)
  if (typeof dateString === 'string' && dateString.includes('T')) {
    // Treat ISO strings as UTC to avoid timezone conversion issues
    const year = date.getUTCFullYear();
    const month = date.toLocaleString("en-US", { month: "long", timeZone: "UTC" });
    const day = date.getUTCDate();
    const hour = String(date.getUTCHours()).padStart(2, '0');
    const minute = String(date.getUTCMinutes()).padStart(2, '0');
    
    return `${month} ${day}, ${year} at ${hour}:${minute}`;
  }

  // For non-ISO dates, use the original logic
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  };

  return date.toLocaleString("en-US", options);
};
