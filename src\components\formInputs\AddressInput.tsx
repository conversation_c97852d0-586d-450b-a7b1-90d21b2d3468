import React, { Suspense, useState, useEffect } from "react";
import { Input } from "../ui/input";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const AddressInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  isRequired,
  title,
  description,
  component,
  placeholder,
  allowedAddressFields,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  placeholder?: string;
  allowedAddressFields?: {
    country?: boolean;
    state?: boolean;
    city?: boolean;
    pincode?: boolean;
  };
  titleMedia?: string;
  isPreview: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField } = useAppStore();

  const [fieldData, setFieldData] = useState(() => {
    if (!value) {
      return {
        address: "",
        country: "",
        state: "",
        city: "",
        pincode: "",
      };
    }

    // Use a delimiter that won't appear in address data
    const delimiter = "||||";
    if (value.includes(delimiter)) {
      const parts = value.split(delimiter);
      return {
        address: parts[0] || "",
        country: parts[1] || "",
        state: parts[2] || "",
        city: parts[3] || "",
        pincode: parts[4] || "",
      };
    } else {
      // Fallback for old format (space-separated)
      const parts = value.split(" ");
      return {
        address: parts[0] || "",
        country: parts[1] || "",
        state: parts[2] || "",
        city: parts[3] || "",
        pincode: parts[4] || "",
      };
    }
  });

  const getSerializedValue = () => {
    const delimiter = "||||";
    return (
      fieldData.address +
      delimiter +
      fieldData.country +
      delimiter +
      fieldData.state +
      delimiter +
      fieldData.city +
      delimiter +
      fieldData.pincode
    );
  };

  // Update local state when value prop changes
  useEffect(() => {
    if (value) {
      const delimiter = "||||";
      if (value.includes(delimiter)) {
        const parts = value.split(delimiter);
        setFieldData({
          address: parts[0] || "",
          country: parts[1] || "",
          state: parts[2] || "",
          city: parts[3] || "",
          pincode: parts[4] || "",
        });
      } else {
        // Fallback for old format
        const parts = value.split(" ");
        setFieldData({
          address: parts[0] || "",
          country: parts[1] || "",
          state: parts[2] || "",
          city: parts[3] || "",
          pincode: parts[4] || "",
        });
      }
    }
  }, [value]);

  useGetConditionById(id, getSerializedValue());

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          <Input
            type="text"
            className="font-medium bg-app-hero-background"
            placeholder={placeholder}
            readOnly={!isPreview}
            name={`${id}_address`}
            disabled={isDisable}
            required={isRequired}
            value={fieldData.address}
            onChange={(e) => {
              const newFieldData = { ...fieldData, address: e.target.value };
              setFieldData(newFieldData);
              const delimiter = "||||";
              onChange?.(
                newFieldData.address +
                  delimiter +
                  newFieldData.country +
                  delimiter +
                  fieldData.state +
                  delimiter +
                  newFieldData.city +
                  delimiter +
                  newFieldData.pincode
              );
            }}
          />
          <div className="flex flex-row flex-wrap gap-4 mt-4">
            {allowedAddressFields?.country && (
              <Input
                type="text"
                placeholder="Country"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_country`}
                disabled={isDisable}
                value={fieldData.country}
                onChange={(e) => {
                  const newFieldData = {
                    ...fieldData,
                    country: e.target.value,
                  };
                  setFieldData(newFieldData);
                  const delimiter = "||||";
                  onChange?.(
                    newFieldData.address +
                      delimiter +
                      newFieldData.country +
                      delimiter +
                      fieldData.state +
                      delimiter +
                      newFieldData.city +
                      delimiter +
                      newFieldData.pincode
                  );
                }}
              />
            )}
            {allowedAddressFields?.state && (
              <Input
                type="text"
                placeholder="State"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_state`}
                disabled={isDisable}
                value={fieldData.state}
                onChange={(e) => {
                  const newFieldData = {
                    ...fieldData,
                    state: e.target.value,
                  };
                  setFieldData(newFieldData);
                  const delimiter = "||||";
                  onChange?.(
                    newFieldData.address +
                      delimiter +
                      newFieldData.country +
                      delimiter +
                      fieldData.state +
                      delimiter +
                      newFieldData.city +
                      delimiter +
                      newFieldData.pincode
                  );
                }}
              />
            )}
            {allowedAddressFields?.city && (
              <Input
                type="text"
                placeholder="City"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_city`}
                disabled={isDisable}
                value={fieldData.city}
                onChange={(e) => {
                  const newFieldData = { ...fieldData, city: e.target.value };
                  setFieldData(newFieldData);
                  const delimiter = "||||";
                  onChange?.(
                    newFieldData.address +
                      delimiter +
                      newFieldData.country +
                      delimiter +
                      fieldData.state +
                      delimiter +
                      newFieldData.city +
                      delimiter +
                      newFieldData.pincode
                  );
                }}
              />
            )}
            {allowedAddressFields?.pincode && (
              <Input
                type="text"
                placeholder="Pin/Zip Code"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_pincode`}
                disabled={isDisable}
                value={fieldData.pincode}
                onChange={(e) => {
                  // Only keep digits
                  const numericValue = e.target.value.replace(/[^0-9]/g, "");
                  const newFieldData = { ...fieldData, pincode: numericValue };
                  setFieldData(newFieldData);
                  const delimiter = "||||";
                  onChange?.(
                    newFieldData.address +
                      delimiter +
                      newFieldData.country +
                      delimiter +
                      fieldData.state +
                      delimiter +
                      newFieldData.city +
                      delimiter +
                      newFieldData.pincode
                  );
                }}
              />
            )}
          </div>
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default AddressInput;
