"use client";

import React, { useState, useEffect } from "react";
import { useGetTutorial, useGetTutorialBySubCategory } from "@/api-services/tutorial";

interface SubCategory {
  id: number;
  name: string;
}

interface Tutorial {
  id: number;
  created_at: string;
  title: string;
  description: string;
  video_url: string;
  thumbnail_link: string;
  published: boolean;
  updated_at: string | null;
  lessson_sequence: number;
}

// Fallback categories to ensure UI always has filters
const fallbackCategories: SubCategory[] = [
  { id: 1, name: "All Modules" },
  { id: 2, name: "Conditional form" },
  { id: 3, name: "Thank you page" },
  { id: 4, name: "Integration" },
  { id: 5, name: "Headers" },
];

const TutorialPage = () => {
  const [selectedModuleId, setSelectedModuleId] = useState<number | null>(1); // Default to All Modules (ID: 1)
  const [selectedModule, setSelectedModule] = useState("All Modules");
  const [currentlyPlayingVideo, setCurrentlyPlayingVideo] = useState<Tutorial | null>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [videoToPlay, setVideoToPlay] = useState<Tutorial | null>(null);
  
  // Fetch sub-categories for filter modules
  const { data: subCategoriesData, isLoading: isLoadingCategories, error: categoriesError } = useGetTutorialBySubCategory();
  
  // Fetch tutorials based on selected module
  // For "All Modules" we might need to fetch differently
  const shouldFetchAll = selectedModuleId === 1 || selectedModule === "All Modules";
  const apiSubCategoryId = shouldFetchAll ? 1 : selectedModuleId || 1; // Use 1 for All Modules instead of 0
  const { data: tutorialsData, isLoading: isLoadingTutorials, error: tutorialsError } = useGetTutorial(
    apiSubCategoryId,
    5 // App ID
  );

    // Use API data if available, otherwise fallback to hardcoded categories
  const subCategories: SubCategory[] = subCategoriesData?.data?.sub_categories?.length > 0 
    ? subCategoriesData.data.sub_categories 
    : fallbackCategories;
    
  const tutorials: Tutorial[] = tutorialsData?.data?.tutorials || [];

  // Set default selected module when categories are loaded
  useEffect(() => {
    if (subCategories.length > 0) {
      // Always try to select "All Modules" first
      const allModulesCategory = subCategories.find((cat: SubCategory) => cat.id === 1 || cat.name === "All Modules");
      if (allModulesCategory && selectedModuleId !== allModulesCategory.id) {
        setSelectedModuleId(allModulesCategory.id);
        setSelectedModule(allModulesCategory.name);
      }
    }
  }, [subCategories]);

  // Set currently playing video when tutorials are loaded
  useEffect(() => {
    if (tutorials.length > 0 && !currentlyPlayingVideo) {
      setCurrentlyPlayingVideo(tutorials[0]);
    }
  }, [tutorials, currentlyPlayingVideo]);

  const handleModuleSelect = (moduleId: number, moduleName: string) => {
    setSelectedModuleId(moduleId);
    setSelectedModule(moduleName);
    setCurrentlyPlayingVideo(null); // Reset currently playing video
  };

  const handleVideoSelect = (video: Tutorial) => {
    setCurrentlyPlayingVideo(video);
  };

  const handlePlayVideo = (video: Tutorial) => {
    setVideoToPlay(video);
    setIsVideoModalOpen(true);
  };

  const closeVideoModal = () => {
    setIsVideoModalOpen(false);
    setVideoToPlay(null);
  };

  // Function to check if URL is a YouTube video
  const isYouTubeUrl = (url: string) => {
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  // Function to convert YouTube URL to embed format
  const getYouTubeEmbedUrl = (url: string) => {
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = url.split('v=')[1]?.split('&')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    } else if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1]?.split('?')[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    return url;
  };

  // Function to check if URL is a valid video file
  const isValidVideoUrl = (url: string) => {
    if (isYouTubeUrl(url)) return true;
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];
    return videoExtensions.some(ext => url.toLowerCase().includes(ext));
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins} min ${secs} sec`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', { 
      day: '2-digit', 
      month: 'short', 
      year: 'numeric' 
    }).toLowerCase();
  };

  const otherVideos = tutorials.filter((video: Tutorial) => video.id !== currentlyPlayingVideo?.id);

  return (
    <div className="min-h-screen bg-app-background dark:bg-app-main-background p-6 transition-colors">
      {/* Header */}
      <div className="flex items-center justify-between bg-white dark:bg-app-main-background rounded-xl p-6 mb-8 shadow-sm transition-colors">
        <div>
          <h1 className="text-3xl font-bold text-app-text-color mb-2">Tutorials</h1>
          <p className="text-app-text-secondary text-lg max-w-xl">
            Your Learning Hub for Mastering Every Automate forms Feature.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <img
            src="/tutorial.png"
            alt="Tutorial Illustration"
            className="w-40 h-28 object-contain hidden md:block"
          />
        </div>
      </div>

      {/* Module Filters */}
      <div className="flex gap-4 mb-8 flex-wrap">
        {isLoadingCategories ? (
          <div className="flex gap-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="px-5 py-2 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse h-9 w-24"></div>
            ))}
          </div>
        ) : (
          subCategories.map((category: SubCategory) => (
            <button
              key={category.id}
              className={`px-5 py-2 rounded-full border text-sm font-medium transition-all focus:outline-none ${
                selectedModuleId === category.id
                  ? "bg-app-text-color text-white dark:bg-app-text-color dark:text-app-main-background"
                  : "bg-white text-app-text-color border-app-border-primary dark:bg-app-main-background dark:text-app-text-color dark:border-app-border-primary"
              }`}
              onClick={() => handleModuleSelect(category.id, category.name)}
            >
              {category.name}
            </button>
          ))
        )}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Currently Playing */}
        <div className="lg:col-span-2">
          <h2 className="text-lg font-semibold mb-2 text-app-text-color">Currently playing</h2>
          {isLoadingTutorials ? (
            <div className="bg-white dark:bg-app-main-background rounded-xl shadow-md p-4 mb-4 transition-colors">
              <div className="w-full aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse mb-4"></div>
              <div className="flex justify-between items-center px-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
              </div>
              <div className="px-2 mt-2">
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
              </div>
            </div>
          ) : currentlyPlayingVideo ? (
            <div className="bg-white dark:bg-app-main-background rounded-xl shadow-md overflow-hidden transition-colors">
              {/* Video Player Section */}
              <div className="relative w-full aspect-video bg-[#8fd19e] rounded-t-xl flex items-center justify-center overflow-hidden">
                {currentlyPlayingVideo.thumbnail_link ? (
                  <img
                    src={currentlyPlayingVideo.thumbnail_link}
                    alt="Video thumbnail"
                    className="absolute inset-0 w-full h-full object-cover opacity-90"
                    onError={(e) => {
                      // Replace with a working placeholder
                      e.currentTarget.src = `https://picsum.photos/800/450?random=${currentlyPlayingVideo.id}`;
                    }}
                  />
                ) : (
                  <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-[#8fd19e] to-[#1de6a6]"></div>
                )}
                <div className="relative z-10 flex flex-col items-center justify-center">
                  <span className="text-[#1de6a6] font-bold text-lg mb-2 bg-black bg-opacity-50 px-3 py-1 rounded">TUTORIAL</span>
                  <span className="text-3xl font-bold text-white mb-2 text-center bg-black bg-opacity-50 px-4 py-2 rounded">{currentlyPlayingVideo.title}</span>
                  <button 
                    onClick={() => handlePlayVideo(currentlyPlayingVideo)}
                    className="bg-white dark:bg-app-main-background rounded-full p-4 shadow-lg mt-2 hover:scale-105 transition-transform"
                  >
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="18" cy="18" r="18" fill="#1de6a6" />
                      <polygon points="15,12 26,18 15,24" fill="white" />
                    </svg>
                  </button>
                </div>
              </div>

                             {/* Video Info Section */}
               <div className="p-6">
                 {/* Title and Meta Info */}
                 <div className="mb-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                   <h3 className="text-xl font-bold text-app-text-color mb-2">{currentlyPlayingVideo.title}</h3>
                   <div className="flex justify-between items-center text-sm text-app-text-secondary">
                     <span>Uploaded on: {formatDate(currentlyPlayingVideo.created_at)}</span>
                     <span>Duration: 20 min 50 sec</span>
                   </div>
                 </div>

                 {/* Description Section */}
                 <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                   <h4 className="text-lg font-semibold text-app-text-color mb-3">Description</h4>
                   <p className="text-app-text-secondary leading-relaxed">
                     {currentlyPlayingVideo.description || "You can re-engage users who started filling but didn't complete with targeted messages or reminders. Use AI to offer autofill, hints, or conditional logic based on how users interact with the form. A higher fill percentage shows stronger user intent, even if they don't submit the form—this is valuable insight! See where users stop filling the form to identify confusing or missing information. Identify specific sections, pages, or specific form item that causes users to stop filling the form—helps you troubleshoot your form design. Read More"}
                   </p>
                 </div>

                 {/* Social Media Section */}
                 <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                   <h4 className="text-lg font-semibold text-app-text-color mb-3">You can Follow us on</h4>
                                      <div className="flex gap-3">
                     <a 
                       href="https://chat.whatsapp.com/HPC6X9a1qecAmncZgKZ8s0" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white hover:bg-green-600 transition-colors"
                     >
                       <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                       </svg>
                     </a>
                     <a 
                       href="https://www.facebook.com/automatewithkewal" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors"
                     >
                       <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                       </svg>
                     </a>
                     <a 
                       href="https://www.instagram.com/automatebusinessapps/" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-colors"
                     >
                       <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                       </svg>
                     </a>
                     <a 
                       href="https://www.linkedin.com/company/automatebusinessworkspace/" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       className="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center text-white hover:bg-blue-800 transition-colors"
                     >
                       <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                       </svg>
                     </a>
                     <a 
                       href="https://www.youtube.com/results?search_query=kewal+kishan" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center text-white hover:bg-red-700 transition-colors"
                     >
                       <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                       </svg>
                     </a>
                   </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-app-main-background rounded-xl shadow-md p-4 mb-4 transition-colors">
              <div className="relative w-full aspect-video bg-gradient-to-br from-[#8fd19e] to-[#1de6a6] rounded-lg flex items-center justify-center overflow-hidden mb-4">
                <div className="relative z-10 flex flex-col items-center justify-center">
                  <span className="text-[#1de6a6] font-bold text-lg mb-2">TUTORIAL</span>
                  <span className="text-3xl font-bold text-white mb-2 text-center">No Tutorials<br />Available</span>
                  <div className="bg-white dark:bg-app-main-background rounded-full p-4 shadow-lg mt-2 opacity-50">
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="18" cy="18" r="18" fill="#1de6a6" />
                      <polygon points="15,12 26,18 15,24" fill="white" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="flex justify-between items-center px-2">
                <span className="font-semibold text-app-text-color">No tutorials available for {selectedModule}</span>
                <span className="text-xs text-app-text-secondary">Please check back later</span>
              </div>
              <div className="px-2 text-xs text-app-text-secondary">
                Tutorials for this module are coming soon.
              </div>
            </div>
          )}
        </div>

        {/* More Videos */}
        <div>
          <h3 className="text-base font-semibold mb-4 text-app-text-color">
            {otherVideos.length} More Videos
          </h3>
          <div className="flex flex-col gap-4">
            {isLoadingTutorials ? (
              [1, 2, 3].map((i) => (
                <div key={i} className="bg-white dark:bg-app-main-background rounded-xl shadow-md p-3 flex flex-col gap-2 transition-colors">
                  <div className="w-full aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
                </div>
              ))
            ) : otherVideos.length > 0 ? (
              otherVideos.map((video: Tutorial) => (
                <div 
                  key={video.id} 
                  className="bg-white dark:bg-app-main-background rounded-xl shadow-md p-3 flex flex-col gap-2 transition-colors cursor-pointer hover:shadow-lg"
                  onClick={() => handleVideoSelect(video)}
                >
                  <div className="relative w-full aspect-video rounded-lg overflow-hidden mb-2">
                    {video.thumbnail_link ? (
                      <img
                        src={video.thumbnail_link}
                        alt="Video thumbnail"
                        className="absolute inset-0 w-full h-full object-cover opacity-90"
                        onError={(e) => {
                          // Replace with a working placeholder
                          e.currentTarget.src = `https://picsum.photos/400/225?random=${video.id}`;
                        }}
                      />
                    ) : (
                      <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-[#1de6e6] to-[#ea3d3d]"></div>
                    )}
                    <div className="relative z-10 flex flex-col items-center justify-center h-full">
                      <span className="text-[#1de6a6] font-bold text-xs mb-1">TUTORIAL</span>
                      <span className="text-lg font-bold text-white mb-1 text-center">{video.title}</span>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlayVideo(video);
                        }}
                        className="bg-white dark:bg-app-main-background rounded-full p-2 shadow-lg mt-1 hover:scale-105 transition-transform"
                      >
                        <svg width="24" height="24" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="18" cy="18" r="18" fill="#1de6a6" />
                          <polygon points="15,12 26,18 15,24" fill="white" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <span className="font-semibold text-app-text-color text-sm">{video.title}</span>
                  <span className="text-xs text-app-text-secondary">
                    Uploaded on: {formatDate(video.created_at)}
                  </span>
                  <span className="text-xs text-app-text-secondary">{video.description}</span>
                </div>
              ))
            ) : (
              <div className="bg-white dark:bg-app-main-background rounded-xl shadow-md p-6 text-center transition-colors">
                <div className="text-app-text-secondary mb-2">No additional videos</div>
                <div className="text-xs text-app-text-secondary">More content coming soon for {selectedModule}</div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Video Modal */}
      {isVideoModalOpen && videoToPlay && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-app-main-background rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-4 border-b border-app-border-primary">
              <h3 className="text-xl font-bold text-app-text-color">{videoToPlay.title}</h3>
              <button
                onClick={closeVideoModal}
                className="text-app-text-secondary hover:text-app-text-color text-2xl"
              >
                ×
              </button>
            </div>
            
            {/* Video Player */}
            <div className="p-4">
              <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden mb-4">
                {videoToPlay.video_url && isValidVideoUrl(videoToPlay.video_url) ? (
                  isYouTubeUrl(videoToPlay.video_url) ? (
                    // YouTube embed
                    <iframe
                      src={getYouTubeEmbedUrl(videoToPlay.video_url)}
                      className="absolute inset-0 w-full h-full"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      title={videoToPlay.title}
                    />
                  ) : (
                    // Regular video file
                    <video
                      src={videoToPlay.video_url}
                      controls
                      autoPlay
                      className="absolute inset-0 w-full h-full"
                      
                    >
                      Your browser does not support the video tag.
                    </video>
                  )
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center text-white bg-gray-800">
                    <div className="text-center">
                      <p className="text-lg mb-2">Video not available</p>
                      <p className="text-sm text-gray-400 mb-4">
                        {videoToPlay.video_url ? 
                          `Invalid video URL: ${videoToPlay.video_url}` : 
                          'No video URL provided'
                        }
                      </p>
                      <div className="bg-blue-600 text-white px-4 py-2 rounded text-sm">
                        <p className="font-semibold mb-2">Supported video formats:</p>
                        <ul className="text-xs space-y-1">
                          <li>• Direct video files (.mp4, .webm, .ogg)</li>
                          <li>• YouTube videos (youtube.com or youtu.be)</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Video Info */}
              <div className="space-y-2">
                <p className="text-app-text-secondary text-sm">
                  Uploaded on: {formatDate(videoToPlay.created_at)}
                </p>
                <p className="text-app-text-color">{videoToPlay.description}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorialPage; 