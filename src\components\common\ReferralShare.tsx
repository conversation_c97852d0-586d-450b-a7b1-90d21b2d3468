import React, { useState, useRef, useEffect } from "react";
import { Gift, Link as LinkIcon } from "lucide-react";
import { useGetReferralDetails } from "@/api-services/referral";
import { toast } from "react-hot-toast";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

const ReferralShare = () => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const pathname = usePathname();
  const isReferAndEarnPage = pathname === "/billing/refer-and-earn";

  const { data: referralData, isLoading, isError } = useGetReferralDetails();
  const referralCode = referralData?.data?.referral_code;
  const referralLink = referralCode
    ? `https://automateforms.ai/signup?ref=${referralCode}`
    : "";

  const socialMediaLinks = [
    {
      href: `https://wa.me/?text=${encodeURIComponent(referralLink)}`,
      alt: "Whatsapp icon",
      src: "/WhatsApp.png",
      ariaLabel: "Share on WhatsApp",
    },
    {
      href: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Facebook icon",
      src: "/Facebook.png",
      ariaLabel: "Share on Facebook",
    },
    {
      href: `https://twitter.com/intent/tweet?url=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Twitter X icon",
      src: "/X.png",
      ariaLabel: "Share on Twitter",
    },
    {
      href: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Linkedin icon",
      src: "/LinkedIn.png",
      ariaLabel: "Share on LinkedIn",
    },
    {
      href: `mailto:?subject=Join me on Automate Forms&body=${encodeURIComponent(
        referralLink
      )}`,
      alt: "Gmail icon",
      src: "/Gmail.png",
      ariaLabel: "Share via Email",
    },
  ];

  const handleCopyLink = () => {
    if (referralLink) {
      navigator.clipboard.writeText(referralLink);
      toast.success("Referral link copied to clipboard!");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative inline-block">
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-full hover:bg-app-text-secondary hover:text-app-main-background"
        aria-label="Share referral"
      >
        <Gift className="w-5 h-5" />
      </button>

      {isOpen && (
        <div
          ref={popoverRef}
          className="fixed z-50 bg-app-background border border-app-hero-background rounded-lg shadow-lg"
          style={{
            width: "500px",
            maxHeight: "80vh",
            top: buttonRef.current
              ? buttonRef.current.getBoundingClientRect().bottom + 8
              : 0,
            right:
              window.innerWidth -
              (buttonRef.current
                ? buttonRef.current.getBoundingClientRect().right
                : 0),
          }}
        >
          <div className="p-6 flex flex-col gap-2">
            <div className="font-semibold mb-2">
              Share the link with your friend and earn rewards
            </div>
            <div className="flex items-center gap-2 bg-app-main-background border border-dashed border-gray-300 rounded p-2">
              <LinkIcon className="w-5 h-5 text-app-text-secondary" />
              <span className="text-xs font-mono break-all text-app-text-color">
                {isLoading ? "Loading..." : referralLink}
              </span>
              <button
                className="ml-auto px-3 py-1 rounded bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color font-medium text-xs transition disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handleCopyLink}
                disabled={isLoading || !referralLink}
              >
                Copy
              </button>
            </div>

            <p className="text-sm mt-4 text-app-text-color">
              Share your referral link on social media and through email
            </p>
            <div className="flex flex-row items-center justify-start gap-4 flex-wrap mt-2">
              {socialMediaLinks.map(({ href, alt, src, ariaLabel }, index) => (
                <Link
                  key={index}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={ariaLabel}
                  className="hover:opacity-80 transition-opacity"
                >
                  <Image
                    src={src}
                    height={500}
                    width={500}
                    quality={100}
                    alt={alt}
                    className="h-8 w-8"
                  />
                </Link>
              ))}
            </div>

            {!isReferAndEarnPage && (
              <div className="mt-4 flex justify-center">
                <Link
                  href="/billing/refer-and-earn"
                  className="px-4 py-2 rounded bg-app-text-color text-app-background hover:bg-opacity-90 transition-colors text-sm font-medium"
                >
                  More Details
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ReferralShare;
