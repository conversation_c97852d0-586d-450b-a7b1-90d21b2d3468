"use client";

import React from "react";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface AppPaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
}

const AppPagination: React.FC<AppPaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
  showInfo = true,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  
  // Don't render pagination if there's only one page or no items
  if (totalPages <= 1) return null;

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
              className={`cursor-pointer transition-colors duration-200 ${
                currentPage === i
                  ? "bg-app-text-color text-app-background hover:bg-app-text-secondary"
                  : "text-app-text-color hover:bg-app-hero-background hover:text-app-text-color"
              }`}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      // Show ellipsis for large page counts
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, currentPage + 2);

      if (startPage > 1) {
        pages.push(
          <PaginationItem key={1}>
            <PaginationLink
              onClick={() => handlePageChange(1)}
              className="cursor-pointer text-app-text-color hover:bg-app-hero-background hover:text-app-text-color"
            >
              1
            </PaginationLink>
          </PaginationItem>
        );
        if (startPage > 2) {
          pages.push(
            <PaginationItem key="ellipsis-start">
              <PaginationEllipsis className="text-app-text-secondary" />
            </PaginationItem>
          );
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
              className={`cursor-pointer transition-colors duration-200 ${
                currentPage === i
                  ? "bg-app-text-color text-app-background hover:bg-app-text-secondary"
                  : "text-app-text-color hover:bg-app-hero-background hover:text-app-text-color"
              }`}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push(
            <PaginationItem key="ellipsis-end">
              <PaginationEllipsis className="text-app-text-secondary" />
            </PaginationItem>
          );
        }
        pages.push(
          <PaginationItem key={totalPages}>
            <PaginationLink
              onClick={() => handlePageChange(totalPages)}
              className="cursor-pointer text-app-text-color hover:bg-app-hero-background hover:text-app-text-color"
            >
              {totalPages}
            </PaginationLink>
          </PaginationItem>
        );
      }
    }

    return pages;
  };

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6">
      {showInfo && (
        <div className="flex items-center">
          <div className="px-3 py-1.5 bg-app-hero-background rounded-md border border-app-border-primary">
            <span className="text-sm text-app-text-secondary font-medium">
              Total: 
            </span>
            <span className="text-sm text-app-text-color font-semibold ml-1">
              {totalItems}
            </span>
          </div>
        </div>
      )}
      
      <Pagination className="mx-0">
        <PaginationContent className="gap-1">
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePageChange(currentPage - 1)}
              className={`cursor-pointer transition-colors duration-200 ${
                currentPage === 1
                  ? "opacity-50 cursor-not-allowed pointer-events-none"
                  : "text-app-text-color hover:bg-app-hero-background hover:text-app-text-color"
              }`}
            />
          </PaginationItem>
          
          {renderPageNumbers()}
          
          <PaginationItem>
            <PaginationNext
              onClick={() => handlePageChange(currentPage + 1)}
              className={`cursor-pointer transition-colors duration-200 ${
                currentPage === totalPages
                  ? "opacity-50 cursor-not-allowed pointer-events-none"
                  : "text-app-text-color hover:bg-app-hero-background hover:text-app-text-color"
              }`}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
};

export default AppPagination; 