import React, { useState } from "react";
import {
  Eye,
  Send,
  Clock,
  Monitor,
  Smartphone,
  Tablet as TabletIcon,
  Info,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  useGetViewsTrend,
  useGetSubmissionsTrend,
  useGetCardData,
  useGetViewsByDevice,
} from "@/api-services/analytics";
import { useSearchParams } from "next/navigation";
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  subYears,
} from "date-fns";
import { Pie } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend as ChartLegend,
} from "chart.js";
ChartJS.register(ArcElement, ChartTooltip, ChartLegend);

const TABS = ["This Week", "This month", "All time", "Custom"];
const DEVICE_COLORS = [
  "#3B82F6", // blue
  "#EF4444", // red
  "#22C55E", // green
  "#F59E0B", // amber
  "#8B5CF6", // violet
  "#EC4899", // pink
  "#14B8A6", // teal
  "#F97316", // orange
  "#6366F1", // indigo
];

function TrendChart({ data }: { data: { label: string; count: number }[] }) {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart
        data={data}
        margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
      >
        <CartesianGrid
          strokeDasharray="3 3"
          vertical={false}
          stroke="#f0f0f0"
        />
        <XAxis
          dataKey="label"
          tick={{ fontSize: 14, fill: "#222", fontWeight: 600 }}
          axisLine={{ stroke: "#222", strokeWidth: 2 }}
          tickLine={{ stroke: "#222", strokeWidth: 2 }}
        />
        <YAxis
          allowDecimals={false}
          tick={{ fontSize: 12, fill: "#64748b" }}
          axisLine={false}
          tickLine={false}
        />
        <Tooltip
          contentStyle={{
            borderRadius: 8,
            fontSize: 14,
            boxShadow:
              "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            border: "none",
          }}
        />
        <Line
          type="monotone"
          dataKey="count"
          stroke="#4f46e5"
          strokeWidth={3}
          dot={{ r: 5, fill: "#4f46e5", stroke: "#fff", strokeWidth: 2 }}
          activeDot={{ r: 7, stroke: "#fff", strokeWidth: 3 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

function DevicePieChart({ data, loading }: { data: any; loading: boolean }) {
  return (
    <div className="relative w-64 h-64 flex items-center justify-center">
      {loading ? (
        <div className="flex items-center justify-center h-full text-gray-400">
          Loading...
        </div>
      ) : (
        <Pie
          data={data}
          options={{
            plugins: {
              legend: { display: false },
            },
            cutout: "65%",
            responsive: true,
            maintainAspectRatio: false,
          }}
        />
      )}
      {!loading && (
        <div className="absolute inset-0 flex items-center justify-center flex-col">
          <span className="text-2xl font-bold text-app-text-color">
            {data.datasets[0].data.reduce((a: number, b: number) => a + b, 0)}
          </span>
          <span className="text-sm text-app-text-secondary">Total</span>
        </div>
      )}
    </div>
  );
}

const AnalyticsPage = () => {
  const [activeTab, setActiveTab] = useState("This Week");
  const [viewType, setViewType] = useState("Views");
  const [customFrom, setCustomFrom] = useState("");
  const [customTo, setCustomTo] = useState("");
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId") as string;

  // Calculate from/to dates based on activeTab
  const now = new Date();
  let from = "";
  let to = "";
  if (activeTab === "This Week") {
    from = format(startOfWeek(now, { weekStartsOn: 0 }), "yyyy-MM-dd");
    to = format(endOfWeek(now, { weekStartsOn: 0 }), "yyyy-MM-dd");
  } else if (activeTab === "This month") {
    from = format(startOfMonth(now), "yyyy-MM-dd");
    to = format(endOfMonth(now), "yyyy-MM-dd");
  } else if (activeTab === "All time") {
    from = format(subYears(now, 1), "yyyy-MM-dd");
    to = format(now, "yyyy-MM-dd");
  } else if (activeTab === "Custom") {
    from = customFrom;
    to = customTo;
  }

  const {
    data: viewsData,
    isLoading: isViewsLoading,
    error: viewsError,
  } = useGetViewsTrend(formId, from, to);
  const {
    data: submissionsData,
    isLoading: isSubmissionsLoading,
    error: submissionsError,
  } = useGetSubmissionsTrend(formId, from, to);
  const { data: cardData, isLoading: isCardLoading } = useGetCardData(
    formId,
    from,
    to
  );
  const { data: deviceData, isLoading: isDeviceLoading } = useGetViewsByDevice(
    formId,
    from,
    to
  );

  const isLoading =
    viewType === "Views" ? isViewsLoading : isSubmissionsLoading;
  const error = viewType === "Views" ? viewsError : submissionsError;
  const trendData =
    viewType === "Views"
      ? viewsData?.data?.trend || []
      : submissionsData?.data?.trend || [];

  const analytics = cardData?.data?.analytics || {};
  const views = analytics.uniqueViews ?? 0;
  const responses = analytics.submissions ?? 0;
  const conversionRate =
    analytics.conversionRate !== undefined
      ? analytics.conversionRate
      : views > 0
      ? ((responses / views) * 100).toFixed(2)
      : "0.00";

  // Device data processing
  const defaultDevices = [
    { label: "Desktop", icon: <Monitor className="w-5 h-5 text-blue-500" /> },
    { label: "Mobile", icon: <Smartphone className="w-5 h-5 text-red-500" /> },
    {
      label: "Tablet",
      icon: <TabletIcon className="w-5 h-5 text-green-500" />,
    },
  ];
  const deviceCounts = deviceData?.data?.responseCounts || {};
  const allDeviceNames = Array.from(
    new Set([
      ...defaultDevices.map((d) => d.label),
      ...Object.keys(deviceCounts),
    ])
  );

  const pieData = {
    labels: allDeviceNames,
    datasets: [
      {
        data: allDeviceNames.map((name) => deviceCounts[name] ?? 0),
        backgroundColor: DEVICE_COLORS.slice(0, allDeviceNames.length),
        borderWidth: 0,
        borderRadius: 4,
      },
    ],
  };

  const iconForDevice = (name: string) => {
    if (name === "Desktop")
      return <Monitor className="w-5 h-5 text-blue-500" />;
    if (name === "Mobile")
      return <Smartphone className="w-5 h-5 text-red-500" />;
    if (name === "Tablet")
      return <TabletIcon className="w-5 h-5 text-green-500" />;
    return null;
  };

  return (
    <div className="flex flex-col w-full p-6 bg-app-hero-background min-h-screen font-sans overflow-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-app-text-color mb-2">
          Form Analytics
        </h1>
        <p className="text-app-text-secondary">
          Track your form performance and user engagement
        </p>
      </div>

      {/* Tabs and view controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <div className="flex gap-1 bg-app-background p-1 rounded-lg">
            {TABS.map((tab) => (
              <button
                key={tab}
                className={`px-4 py-2 text-sm font-medium rounded-md whitespace-nowrap transition-all ${
                  activeTab === tab
                    ? "bg-app-text-color shadow-sm text-app-background"
                    : "text-app-text-secondary hover:text-app-text-color"
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </button>
            ))}
          </div>
          {activeTab === "Custom" && (
            <div className="flex items-center ml-2">
              <label className="text-sm font-medium text-app-text-secondary mr-2">
                From:
              </label>
              <input
                type="date"
                className="px-3 py-2 border bg-app-background rounded-md text-sm shadow-sm focus:outline-none"
                value={customFrom}
                onChange={(e) => setCustomFrom(e.target.value)}
              />
              <label className="text-sm font-medium text-app-text-secondary mx-2">
                To:
              </label>
              <input
                type="date"
                className="px-3 py-2 border bg-app-background rounded-md text-sm shadow-sm focus:outline-none"
                value={customTo}
                onChange={(e) => setCustomTo(e.target.value)}
              />
            </div>
          )}
        </div>

        <div className="flex gap-2 w-full sm:w-auto">
          <button
            className={`flex-1 sm:flex-none px-4 py-2 text-sm font-medium rounded-md transition-all border 
              ${
                viewType === "Views"
                  ? "bg-app-text-color text-app-background"
                  : "bg-app-background text-app-text-color border-app-border-primary"
              }
            `}
            onClick={() => setViewType("Views")}
          >
            Views
          </button>
          <button
            className={`flex-1 sm:flex-none px-4 py-2 text-sm font-medium rounded-md transition-all border border-app-border-primary
              ${
                viewType === "Submissions"
                  ? "bg-app-text-color text-app-background"
                  : "bg-app-background text-app-text-color border-app-border-primary"
              }
            `}
            onClick={() => setViewType("Submissions")}
          >
            Submissions
          </button>
        </div>
      </div>

      {/* Chart area */}
      <div className="bg-app-background rounded-xl p-6 mb-6 shadow-sm border border-app-border-primary">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-app-text-color">
            {viewType === "Views" ? "Views Trend" : "Submissions Trend"}
          </h2>
          <div className="text-sm text-app-text-secondary">
            {activeTab === "Custom" && customFrom && customTo
              ? `${format(new Date(customFrom), "MMM d, yyyy")} - ${format(
                  new Date(customTo),
                  "MMM d, yyyy"
                )}`
              : activeTab}
          </div>
        </div>
        <div className="h-80">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-pulse flex flex-col items-center">
                <div className="h-64 w-full bg-gray-100 rounded"></div>
                <div className="mt-4 text-app-text-secondary">
                  Loading chart data...
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full text-red-500">
              <div className="text-center">
                <div className="text-lg font-medium">Error loading data</div>
                <div className="text-sm mt-1">Please try again later</div>
              </div>
            </div>
          ) : (
            <TrendChart data={trendData} />
          )}
        </div>
      </div>

      {/* Summary cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 max-w-3xl mx-auto">
        <div className="bg-app-background rounded-xl shadow-sm p-4 border border-app-border-primary max-w-xs w-full mx-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-app-text-color">Views</h3>
            <Eye className="w-5 h-5 text-indigo-500" />
          </div>
          <div className="flex items-baseline gap-2">
            <span className="text-3xl font-bold text-app-text-secondary">
              {isCardLoading ? "--" : views.toLocaleString()}
            </span>
            <span className="text-sm text-app-text-color">total</span>
          </div>
        </div>

        <div className="bg-app-background rounded-xl shadow-sm p-4 border border-app-border-primary max-w-xs w-full mx-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-app-text-color">
              Responses
            </h3>
            <Send className="w-5 h-5 text-green-500" />
          </div>
          <div className="flex items-baseline gap-2">
            <span className="text-3xl font-bold text-app-text-secondary">
              {isCardLoading ? "--" : responses.toLocaleString()}
            </span>
            <span className="text-sm text-app-text-color">total</span>
          </div>
        </div>

        <div className="bg-app-background rounded-xl shadow-sm p-4 border border-app-border-primary max-w-xs w-full mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-1">
              <h3 className="text-sm font-medium text-app-text-color">
                Conversion Rate
              </h3>
              <Info className="w-4 h-4 text-gray-400" />
            </div>
          </div>
          <div className="flex items-baseline gap-2">
            <span className="text-3xl font-bold text-app-text-secondary">
              {isCardLoading ? "--" : conversionRate}%
            </span>
            <span className="text-sm text-app-text-color">from views</span>
          </div>
        </div>
      </div>

      {/* Device section */}
      <div className="bg-app-background rounded-xl shadow-sm p-6 border border-app-border-primary mb-16">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold text-app-text-color">
            Device Distribution
          </h2>
          <div className="text-sm text-app-text-secondary">
            Responses by device type
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 items-center lg:items-start">
          <DevicePieChart data={pieData} loading={isDeviceLoading} />

          <div className="flex-1 w-full">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {allDeviceNames.map((name, idx) => {
                const count = deviceCounts[name] ?? 0;
                const total = allDeviceNames.reduce(
                  (sum, n) => sum + (deviceCounts[n] ?? 0),
                  0
                );
                const percentage =
                  total > 0 ? Math.round((count / total) * 100) : 0;

                return (
                  <div
                    key={name}
                    className="bg-app-main-background rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <span
                          className="w-3 h-3 rounded-full"
                          style={{
                            backgroundColor:
                              DEVICE_COLORS[idx % DEVICE_COLORS.length],
                          }}
                        />
                        <span className="text-sm font-medium text-app-text-color">
                          {name}
                        </span>
                      </div>
                      {iconForDevice(name)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-app-text-color">
                        {isDeviceLoading ? "--" : count}
                      </span>
                      <span className="text-sm text-app-text-secondary">
                        {percentage}%
                      </span>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 rounded-full"
                        style={{
                          width: `${percentage}%`,
                          backgroundColor:
                            DEVICE_COLORS[idx % DEVICE_COLORS.length],
                        }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
