import React, { useState, useRef, useEffect } from "react";
import { useCreateTicket } from "@/api-services/support";
import { useUploadFile } from "@/api-services/form_submission";
import {
  FileImage,
  FileVideo,
  FileAudio,
  X,
  ChevronDown,
  Loader2,
  Mic,
} from "lucide-react";

interface RaiseTicketDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const categories = [
  "Report an error",
  "Give feedback",
  "Billing/subscription issue",
  "Delete my account",
];
const subcategories = [
  "Conditional logic forms issue",
  "AI form creation issue",
  "Start from scratch form issue",
  "Integration issue",
  "Other",
];

const RaiseTicketDialog: React.FC<RaiseTicketDialogProps> = ({
  open,
  onClose,
  onSuccess,
}) => {
  const [category, setCategory] = useState("");
  const [subcategory, setSubcategory] = useState("");
  const [subject, setSubject] = useState("");
  const [description, setDescription] = useState("");
  const [imageLinks, setImageLinks] = useState<string[]>([]);
  const [videoLink, setVideoLink] = useState<string>("");
  const [voiceLink, setVoiceLink] = useState<string>("");
  const [showCategory, setShowCategory] = useState(false);
  const [showSubcategory, setShowSubcategory] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [videoUploading, setVideoUploading] = useState(false);
  const [audioUploading, setAudioUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const audioChunksRef = useRef<Blob[]>([]);

  // API mutations
  const createTicketMutation = useCreateTicket();
  const uploadFileMutation = useUploadFile();

  // Refs for dropdowns
  const catRef = useRef<HTMLDivElement>(null);
  const subcatRef = useRef<HTMLDivElement>(null);

  // Close dropdowns on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        catRef.current &&
        !catRef.current.contains(event.target as Node) &&
        subcatRef.current &&
        !subcatRef.current.contains(event.target as Node)
      ) {
        setShowCategory(false);
        setShowSubcategory(false);
      }
    }
    if (showCategory || showSubcategory) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showCategory, showSubcategory]);

  // Reset and close on success
  useEffect(() => {
    if (createTicketMutation.isSuccess) {
      setCategory("");
      setSubcategory("");
      setSubject("");
      setDescription("");
      setImageLinks([]);
      setVideoLink("");
      setVoiceLink("");
      onSuccess?.();
      onClose();
      createTicketMutation.reset();
    }
  }, [createTicketMutation.isSuccess, onClose, onSuccess]);

  if (!open) return null;

  // Handle file uploads
  const handleUpload = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "image" | "video" | "audio"
  ) => {
    const selectedFile = e.target.files?.[0] || null;
    if (!selectedFile) return;
    if (type === "image") setImageUploading(true);
    if (type === "video") setVideoUploading(true);
    if (type === "audio") setAudioUploading(true);
    const formData = new FormData();
    formData.append("upload", selectedFile);
    uploadFileMutation.mutate(
      { formData },
      {
        onSuccess: (res: any) => {
          const url =
            res?.data?.url || res?.data?.fileUrl || res?.fileUrl || null;
          if (type === "image" && url) setImageLinks((prev) => [...prev, url]);
          if (type === "video" && url) setVideoLink(url);
          if (type === "audio" && url) setVoiceLink(url);
        },
        onSettled: () => {
          if (type === "image") setImageUploading(false);
          if (type === "video") setVideoUploading(false);
          if (type === "audio") setAudioUploading(false);
        },
      }
    );
  };

  const startRecording = async () => {
    setAudioChunks([]);
    audioChunksRef.current = [];
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const recorder = new MediaRecorder(stream);
    setMediaRecorder(recorder);
    recorder.start();
    setIsRecording(true);

    recorder.ondataavailable = (e) => {
      audioChunksRef.current.push(e.data);
    };

    recorder.onstop = async () => {
      setIsRecording(false);
      const audioBlob = new Blob(audioChunksRef.current, {
        type: "audio/webm",
      });
      const formData = new FormData();
      formData.append("upload", audioBlob, "recording.webm");
      uploadFileMutation.mutate(
        { formData },
        {
          onSuccess: (res: any) => {
            const url =
              res?.data?.url || res?.data?.fileUrl || res?.fileUrl || null;
            if (url) setVoiceLink(url);
          },
          onSettled: () => {
            setAudioUploading(false);
          },
        }
      );
      setAudioChunks([]);
      audioChunksRef.current = [];
    };
  };

  const stopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setMediaRecorder(null);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Prepare form data
    const formData: any = {
      category,
      sub_category: subcategory,
      subject: subject,
      description,
      image_links: imageLinks,
      video_link: videoLink,
      voice_link: voiceLink,
    };
    createTicketMutation.mutate(formData);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300 p-4">
      <div className="bg-app-hero-background rounded-xl shadow-xl w-full max-w-xl mx-auto relative max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between px-4 sm:px-6 pt-4 sm:pt-6 pb-3 sm:pb-4 border-b">
          <h2 className="text-xl sm:text-2xl font-bold text-app-text-color">
            Raise a Support Ticket
          </h2>
          <button
            onClick={onClose}
            className="text-app-text-color hover:text-app-text-secondary focus:outline-none transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form
          className="p-4 sm:p-6 space-y-4 sm:space-y-6 overflow-y-auto scroller-style"
          onSubmit={handleSubmit}
        >
          <div className="flex flex-col gap-4">
            {/* Category Dropdown */}
            <div className="relative flex-1" ref={catRef}>
              <label className="block text-sm font-medium text-app-text-secondary mb-1">
                Category
              </label>
              <button
                type="button"
                className={`w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 bg-app-background rounded-lg font-medium text-app-text-color flex items-center justify-between focus:outline-none border border-gray-200 transition-all hover:border-gray-300 ${
                  showCategory ? "ring-2 ring-blue-500 border-blue-500" : ""
                }`}
                onClick={() => {
                  setShowCategory((v) => !v);
                  setShowSubcategory(false);
                }}
              >
                <span className="truncate">
                  {category || "Select category"}
                </span>
                <ChevronDown
                  className={`ml-2 transition-transform ${
                    showCategory ? "rotate-180" : ""
                  }`}
                />
              </button>
              {showCategory && (
                <div className="absolute left-0 right-0 mt-1 bg-app-main-background border rounded-lg shadow-lg z-10 w-full max-h-48 overflow-y-auto">
                  {categories.map((cat) => (
                    <div
                      key={cat}
                      className={`px-3 sm:px-4 py-2 sm:py-2.5 cursor-pointer text-app-text-color transition-colors hover:bg-app-hero-background ${
                        category === cat
                          ? "bg-app-sidebar-hover-active text-blue-600 font-semibold"
                          : ""
                      }`}
                      onClick={() => {
                        setCategory(cat);
                        setSubcategory("");
                        setShowCategory(false);
                      }}
                    >
                      {cat}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Subcategory Dropdown */}
            <div className="relative flex-1" ref={subcatRef}>
              <label className="block text-sm font-medium text-app-text-secondary mb-1">
                Subcategory
              </label>
              <button
                type="button"
                className={`w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 bg-app-background rounded-lg font-medium text-app-text-color flex items-center justify-between focus:outline-none border border-gray-200 transition-all hover:border-gray-300 ${
                  showSubcategory ? "ring-2 ring-blue-500 border-blue-500" : ""
                }`}
                onClick={() => {
                  setShowSubcategory((v) => !v);
                  setShowCategory(false);
                }}
              >
                <span className="truncate">
                  {subcategory || "Select subcategory"}
                </span>
                <ChevronDown
                  className={`ml-2 transition-transform ${
                    showSubcategory ? "rotate-180" : ""
                  }`}
                />
              </button>
              {showSubcategory && (
                <div className="absolute left-0 right-0 mt-1 bg-app-main-background border rounded-lg shadow-lg z-10 w-full max-h-48 overflow-y-auto">
                  {subcategories.map((sub: string) => (
                    <div
                      key={sub}
                      className={`px-3 sm:px-4 py-2 sm:py-2.5 cursor-pointer text-app-text-color transition-colors hover:bg-app-hero-background ${
                        subcategory === sub
                          ? "bg-app-sidebar-hover-active text-blue-600 font-semibold"
                          : ""
                      }`}
                      onClick={() => {
                        setSubcategory(sub);
                        setShowSubcategory(false);
                      }}
                    >
                      {sub}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Subject */}
          <div>
            <label className="block text-sm font-medium text-app-text-secondary mb-1">
              Subject
            </label>
            <input
              type="text"
              placeholder="Briefly describe your issue"
              className="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-app-background rounded-lg font-medium text-app-text-color focus:outline-none border border-gray-200 hover:border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-app-text-secondary mb-1">
              Description
            </label>
            <textarea
              placeholder="Please provide detailed information about your issue"
              className="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-app-background rounded-lg font-medium text-app-text-color focus:outline-none min-h-[120px] sm:min-h-[150px] border border-gray-200 hover:border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              required
            />
          </div>

          {/* File Uploads */}
          <div>
            <label className="block text-sm font-medium text-app-text-secondary mb-2">
              Attachments (optional)
            </label>
            <div className="bg-app-background rounded-lg p-3 sm:p-4 border border-app-border-primary">
              <p className="text-sm text-app-text-secondary mb-3">
                Upload images, videos, or audio files that help explain your
                issue
              </p>

              <div className="flex flex-wrap gap-3 sm:gap-4">
                {/* Image Upload */}
                <label
                  className={`cursor-pointer flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 border-2 border-dashed rounded-lg transition-all group
                    ${imageLinks.length > 0 
                      ? 'border-[#1a2e1a] bg-[#e6f7e6]' 
                      : 'border-gray-300 hover:border-[#1a2e1a] hover:bg-[#e6f7e6]'
                    }`}
                >
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => handleUpload(e, "image")}
                    multiple
                  />
                  <div className="flex flex-col items-center">
                    <FileImage className={`text-xl sm:text-2xl mb-1 transition-colors ${
                      imageLinks.length > 0 
                        ? 'text-[#1a2e1a]' 
                        : 'text-gray-400 group-hover:text-[#1a2e1a]'
                    }`} />
                    <span className={`text-xs ${
                      imageLinks.length > 0 
                        ? 'text-[#1a2e1a]' 
                        : 'text-gray-500 group-hover:text-[#1a2e1a]'
                    }`}>
                      Images
                    </span>
                  </div>
                  {imageUploading && (
                    <span className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80">
                      <span className="text-xs text-[#1a2e1a]">
                        Uploading...
                      </span>
                    </span>
                  )}
                </label>

                {/* Video Upload */}
                <label
                  className={`cursor-pointer flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 border-2 border-dashed rounded-lg transition-all group
                    ${videoLink 
                      ? 'border-[#1a2e1a] bg-[#e6f7e6]' 
                      : 'border-gray-300 hover:border-[#1a2e1a] hover:bg-[#e6f7e6]'
                    }`}
                >
                  <input
                    type="file"
                    accept="video/*"
                    className="hidden"
                    onChange={(e) => handleUpload(e, "video")}
                  />
                  <div className="flex flex-col items-center">
                    <FileVideo className={`text-xl sm:text-2xl mb-1 transition-colors ${
                      videoLink 
                        ? 'text-[#1a2e1a]' 
                        : 'text-gray-400 group-hover:text-[#1a2e1a]'
                    }`} />
                    <span className={`text-xs ${
                      videoLink 
                        ? 'text-[#1a2e1a]' 
                        : 'text-gray-500 group-hover:text-[#1a2e1a]'
                    }`}>
                      Video
                    </span>
                  </div>
                  {videoUploading && (
                    <span className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80">
                      <span className="text-xs text-[#1a2e1a]">
                        Uploading...
                      </span>
                    </span>
                  )}
                </label>

                {/* Audio Upload */}
                <label
                  className={`cursor-pointer flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 border-2 border-dashed rounded-lg transition-all group
                    ${voiceLink || isRecording
                      ? 'border-[#1a2e1a] bg-[#e6f7e6]' 
                      : 'border-gray-300 hover:border-[#1a2e1a] hover:bg-[#e6f7e6]'
                    } relative`}
                  onClick={isRecording ? stopRecording : startRecording}
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      isRecording ? stopRecording() : startRecording();
                    }
                  }}
                  title={isRecording ? 'Stop recording' : 'Start recording'}
                >
                  <Mic
                    size={28}
                    className={`sm:w-7 sm:h-7 transition-colors duration-150 ${
                      isRecording
                        ? 'text-red-500'
                        : voiceLink
                        ? 'text-[#1a2e1a]'
                        : 'text-gray-400 group-hover:text-[#1a2e1a]'
                    }`}
                    style={{ cursor: 'pointer' }}
                  />
                  <span className={`text-xs mt-1 ${
                    isRecording
                      ? 'text-red-500'
                      : voiceLink
                      ? 'text-[#1a2e1a]'
                      : 'text-gray-500 group-hover:text-[#1a2e1a]'
                  }`}>
                    {isRecording ? 'Recording...' : 'Audio'}
                  </span>
                  {audioUploading && (
                    <span className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 rounded-lg">
                      <span className="text-xs text-[#1a2e1a]">
                        Uploading...
                      </span>
                    </span>
                  )}
                </label>
              </div>

              {/* Uploaded files preview */}
              {(imageLinks.length > 0 || videoLink || voiceLink) && (
                <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Uploaded files:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {imageLinks.map((link, index) => (
                      <div
                        key={index}
                        className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs relative"
                      >
                        <FileImage className="mr-1 text-gray-500" />
                        <span className="truncate max-w-[80px] sm:max-w-[100px]">
                          {link.split("/").pop()}
                        </span>
                        <button
                          type="button"
                          className="ml-2 text-red-500 hover:text-red-700"
                          onClick={() =>
                            setImageLinks((prev) =>
                              prev.filter((_, i) => i !== index)
                            )
                          }
                          title="Remove"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    ))}
                    {videoLink && (
                      <div className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs relative">
                        <FileVideo className="mr-1 text-gray-500" />
                        <span className="truncate max-w-[80px] sm:max-w-[100px]">
                          {videoLink.split("/").pop()}
                        </span>
                        <button
                          type="button"
                          className="ml-2 text-red-500 hover:text-red-700"
                          onClick={() => setVideoLink("")}
                          title="Remove"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    )}
                    {voiceLink && (
                      <div className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs relative">
                        <FileAudio className="mr-1 text-gray-500" />
                        <span className="truncate max-w-[80px] sm:max-w-[100px]">
                          {voiceLink.split("/").pop()}
                        </span>
                        <button
                          type="button"
                          className="ml-2 text-red-500 hover:text-red-700"
                          onClick={() => setVoiceLink("")}
                          title="Remove"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-1 rounded border bg-app-text-color border-app-border-primary text-app-background hover:bg-app-main-background hover:text-app-text-color font-medium transition-colors mr-3"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={
                createTicketMutation.isPending ||
                !category ||
                !subject ||
                !description
              }
              className={`px-4 py-1 rounded border border-[#1F311C] font-medium
                ${
                  createTicketMutation.isPending ||
                  !category ||
                  !subject ||
                  !description
                    ? "bg-[#1a2e1a] opacity-60 text-white cursor-not-allowed"
                    : "bg-app-background hover:bg-app-text-color text-app-text-color hover:text-app-background"
                }
              `}
            >
              {createTicketMutation.isPending ? (
                <Loader2 className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" />
              ) : null}
              {createTicketMutation.isPending
                ? "Submitting..."
                : "Submit Ticket"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RaiseTicketDialog;
