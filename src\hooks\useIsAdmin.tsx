import { useUserProfile } from "@/api-services/auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import React from "react";

const useIsAdmin = () => {
  const { data: profileData, isLoading } = useUserProfile();
  const router = useRouter();

  // Check if user is admin based on the custom_role field from backend response
  const isAdmin = profileData?.data?.user?.custom_role === "admin";
  
  const ProtectedComponent = ({ children }: { children: React.ReactNode }) => {
    useEffect(() => {
      // If data is loaded and user is NOT admin, redirect to home
      if (!isLoading && !isAdmin) {
        router.push("/home");
      }
    }, [isAdmin, isLoading, router]);

    // Show loading while checking user role
    if (isLoading) {
      return <div>Loading...</div>;
    }

    // If user is NOT admin, don't render the component (will redirect)
    if (!isAdmin) {
      return null;
    }

    // If user IS admin, show the component
    return <>{children}</>;
  };

  return { isAdmin, ProtectedComponent };
};

export default useIsAdmin;
