import { useSidebar } from "@/components/ui/sidebar";
import {
  Cable,
  FileText,
  Folder,
  GalleryHorizontalEnd,
  Headphones,
  PanelsTopLeft,
  Settings,
  Star,
  TvMinimalPlay,
  Wallet,
  Users,
  Trash,
  Gift,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo, useState } from "react";
import { useUserProfile } from "@/api-services/auth";

const useSidebarContainer = () => {
  const {
    state, // 'expanded' or 'collapsed'
    toggleSidebar, // Function to toggle sidebar
  } = useSidebar();

  const [isDialogOpen, setDialogOpen] = useState(false);
  const { data: profileData } = useUserProfile();
  
  // Check if user is admin based on the custom_role field from backend response
  const isAdmin = profileData?.data?.user?.custom_role === "admin";

  const pathname = usePathname();

  const sidebarLinks = useMemo(
    () => {
      const baseLinks = [
        { label: "Home", href: "/home", icon: PanelsTopLeft },
        {
          label: "All Forms",
          href: "/home/<USER>",
          icon: GalleryHorizontalEnd,
        },
        { label: "Settings", href: "/settings", icon: Settings },
        { label: "All Templates", href: "/home/<USER>", icon: FileText },
        // { label: "Connection", href: "/home/<USER>", icon: Cable },
        { label: "Members", href: "/members", icon: Users },
        // { label: "Tutorial", href: "/home/<USER>", icon: TvMinimalPlay },
        { label: "Support", href: "/home/<USER>", icon: Headphones },
        { label: "Trash", href: "/home/<USER>", icon: Trash },
        // { label: "Premium", href: "/home/<USER>", icon: Star },
        { label: "Whats New", href: "/home/<USER>", icon: Gift },
      ];

      // Only add Billing link if user IS admin
      if (isAdmin) {
        baseLinks.splice(4, 0, { label: "Billing", href: "/billing", icon: Wallet });
      }

      return baseLinks;
    },
    [isAdmin]
  );

  const isActive = (href: string) => {
    return (
      pathname === href || (pathname.startsWith(`${href}/`) && href !== "/home")
    );
  };

  const handleOpenDialog = () => setDialogOpen(true);
  const handleCloseDialog = () => setDialogOpen(false);

  return {
    state,
    toggleSidebar,
    sidebarLinks,
    isActive,
    isDialogOpen,
    handleOpenDialog,
    handleCloseDialog,
  };
};

export default useSidebarContainer;
