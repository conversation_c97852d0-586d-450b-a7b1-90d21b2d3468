import { useQuery, useMutation } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1/outlook`;

// Get Integration Actions
async function getIntegrationActions(integrationId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/action/${integrationId}`,
    method: "GET",
  });
}

const useGetIntegrationActions = (integrationId: string) => {
  return useQuery({
    queryKey: ["OUTLOOK_ACTIONS", integrationId],
    queryFn: () => getIntegrationActions(integrationId),
  });
};

// Add New Connection (Outlook)
async function addConnection({
  integrationId,
  name,
  formId,
  formType,
}: {
  integrationId: string;
  name: string;
  formId: string;
  formType: string;
}) {
  const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_URL || "http://localhost:8000";
  const response = await makeRequest({
    endpoint: `${baseEndpoint}/addconnection?integration_id=${integrationId}&name=${encodeURIComponent(
      name
    )}&redirect_uri=${redirectUri}&formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}`,
    method: "GET",
  });

  if (response?.data?.authUrl) {
    window.location.href = response.data.authUrl;
  }

  return response;
}

const useAddConnection = () => {
  return useMutation({
    mutationFn: addConnection,
  });
};

// Get Outlook Connections
async function getConnections(integrationId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/connection/${integrationId}`,
    method: "GET",
  });
}

const useGetConnections = (integrationId: string) => {
  return useQuery({
    queryKey: ["OUTLOOK_CONNECTIONS", integrationId],
    queryFn: () => getConnections(integrationId),
  });
};

// Link Form with Outlook
async function linkForm({
  formId,
  integrationId,
  credentialId,
  actionId,
  columnMappedData,
  emailTemplate,
  subjectTemplate,
  senderName,
}: {
  formId: string;
  integrationId: string;
  credentialId: string;
  actionId: string;
  columnMappedData: Array<{
    id: string;
    name: string;
    title: string;
    key: string;
  }>;
  emailTemplate: string;
  subjectTemplate: string;
  senderName: string;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/linkform`,
    method: "POST",
    data: {
      form_id: formId,
      integration_id: integrationId,
      credential_id: credentialId,
      action_id: actionId,
      column_mapped_data: columnMappedData,
      email_template: emailTemplate,
      subject_template: subjectTemplate,
      sender_name: senderName,
    },
  });
}

const useLinkForm = () => {
  return useMutation({
    mutationFn: linkForm,
  });
};

export { useGetIntegrationActions, useAddConnection, useGetConnections, useLinkForm }; 