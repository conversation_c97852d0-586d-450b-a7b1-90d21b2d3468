@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-roboto), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --font-montserrat: "Montserrat", Arial, Helvetica, sans-serif;
    --font-raleway: "Raleway", Arial, Helvetica, sans-serif;
    --font-courier-prime: "Courier Prime", Arial, Helvetica, sans-serif;
    --font-eb-garamond: "<PERSON><PERSON> Garamond", Arial, Helvetica, sans-serif;
    --font-imprima: "Imprima", Arial, Helvetica, sans-serif;
    --font-lexend: "Lexend", Arial, Helvetica, sans-serif;
    --font-lora: "Lora", Arial, Helvetica, sans-serif;
    --font-merriweather: "Merriweather", Arial, Helvetica, sans-serif;
    --font-nunito: "Nuni<PERSON>", Arial, Helvetica, sans-serif;
    --font-oswald: "<PERSON>", <PERSON><PERSON>, <PERSON>lve<PERSON>, sans-serif;
    --font-pacifico: "Pacifico", <PERSON><PERSON>, Helvetica, sans-serif;
    --font-playfair-display: "Playfair Display", <PERSON><PERSON>, Helvetica, sans-serif;
    --font-roboto: "Roboto", Arial, Helvetica, sans-serif;
    --font-roboto-mono: "Roboto Mono", Arial, Helvetica, sans-serif;

    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 111 26% 11%;
    --foreground: 0 0% 98%;
    --card: 111 26% 11%;
    --card-foreground: 0 0% 98%;
    --popover: 111 26% 11%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.scroller::-webkit-scrollbar {
  display: none;
}

.scroller-style::-webkit-scrollbar {
  width: 5px; /* Thin scrollbar */
  height: 5px;
}

.scroller-style::-webkit-scrollbar-thumb {
  background-color: #1f311c;
  border-radius: 5px;
  cursor: pointer;
}

.scroller-style::-webkit-scrollbar-track {
  background-color: #d2d6d2;
}

.custom-phone-input input {
  @apply bg-app-hero-background focus:ring-0 focus:outline-none placeholder:text-app-text-secondary text-app-text-color;
}

.custom-number-input {
  -moz-appearance: textfield;
}

.custom-number-input::-webkit-inner-spin-button,
.custom-number-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.custom-number-input:hover::-webkit-inner-spin-button,
.custom-number-input:hover::-webkit-outer-spin-button {
  display: none;
}

@media (max-height: 768px) {
  .auth-common {
    bottom: 0px !important;
  }
}

.editor-container > .ql-toolbar:first-of-type {
  display: none !important;
}

/* Select dropdown styling */
select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1em;
  padding-right: 2.5rem;
}

/* Dropdown list styling */
select option {
  padding: 8px;
  min-height: 1.2em;
}

/* Limit dropdown height in Webkit browsers */
select::-webkit-listbox {
  max-height: 300px !important;
}

/* Firefox and other browsers */
select {
  max-height: 300px;
}

/* Scrollbar styling for the dropdown */
select::-webkit-scrollbar {
  width: 8px;
}

select::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

select::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

select::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.hello {
  outline: none;
}

/* Quill Editor Styles */
.ql-editor {
  height: max-content;
  background-color: var(--color-universal);
  color: var(--color-reverse-universal);
}

.dark .ql-editor {
  background-color: var(--color-universal) !important;
}

.ql-toolbar {
  background-color: var(--color-hero-background);
  border-color: var(--color-border);
}

.ql-container {
  border-color: var(--color-border);
  background-color: var(--color-universal);
}

.ql-toolbar button,
.ql-toolbar .ql-picker {
  color: var(--color-reverse-universal);
}

.ql-toolbar button:hover,
.ql-toolbar .ql-picker:hover {
  color: var(--color-reverse-universal);
}

.ql-toolbar button.ql-active,
.ql-toolbar .ql-picker.ql-active {
  color: var(--color-reverse-universal);
}

.ql-toolbar .ql-stroke {
  stroke: var(--color-reverse-universal);
}

.ql-toolbar .ql-fill {
  fill: var(--color-reverse-universal);
}

.ql-toolbar .ql-picker-label {
  color: var(--color-reverse-universal);
}

.ql-toolbar .ql-picker-options {
  background-color: var(--color-universal);
  border-color: var(--color-border);
}

.ql-toolbar .ql-picker-item {
  color: var(--color-reverse-universal);
}

.ql-toolbar .ql-picker-item:hover {
  background-color: var(--color-hero-background);
}

/* Dark mode adjustments */
.dark .ql-editor {
  background-color: hsl(111 26% 11%);
  color: hsl(0 0% 98%);
}

.dark .ql-toolbar {
  background-color: hsl(240 5.9% 10%);
  border-color: hsl(240 3.7% 15.9%);
}

.dark .ql-container {
  border-color: hsl(240 3.7% 15.9%);
  background-color: hsl(111 26% 11%);
}

/* AG Grid cell wrapping override */
.ag-theme-alpine .ag-cell {
  white-space: pre-line !important;
  word-break: break-word !important;
  line-break: anywhere;
}
