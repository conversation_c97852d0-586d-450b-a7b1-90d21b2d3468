import React, { useState, useEffect } from "react";
import { X, Loader2, Network } from "lucide-react";
import {
  useAddCRMConnection,
  useGetIntegrationActions,
  useGetConnections,
  useGetCRMConnectionKey,
  useLinkCRMForm,
  useUpdateCRMForm,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import {
  fetchPipelines,
  fetchCrmLeadSources,
  fetchSalesPersons,
  fetchCrmStages,
  fetchCrmCustomFields,
  fetchContactCustomFields,
  fetchCountries,
  fetchProductCategories,
  fetchProductUnits,
  fetchProductCustomFields,
} from "@/api-services/crm-api";
import { useSearchParams } from "next/navigation";
import { useGetFormFields } from "@/api-services/form_fields";
import { toast } from "react-hot-toast";
import SelectInputCombo, {
  convertTemplateToMapped,
} from "@/components/common/SelectInputCombo";
import SearchableSelect from "@/components/common/SearchableSelect";

interface Action {
  id: string;
  name: string;
  description: string;
  created_at: string;
}

interface Connection {
  id: string;
  name: string;
}

interface CustomField {
  id: number;
  name: string;
  type: string;
  mandatory: boolean;
  dropdown: string[];
}

// Define field configurations for different actions
interface FieldConfig {
  name: string;
  required: boolean;
  type: string;
}

const ACTION_FIELD_CONFIGS: Record<string, FieldConfig[]> = {
  "051b8767-7bf9-4c17-9cc4-1a8c0338187f": [
    // Create Lead
    { name: "firstName", required: true, type: "string" },
    { name: "lastName", required: false, type: "string" },
    { name: "email", required: true, type: "string" },
    { name: "phone", required: true, type: "string" },
    { name: "description", required: false, type: "string" },
    { name: "requirement", required: false, type: "string" },
    { name: "amount", required: false, type: "string" },
    { name: "title", required: true, type: "string" },
    { name: "companyName", required: false, type: "string" },
  ],
  "f3ce3e59-44e8-4a08-95fa-259348b8ba73": [
    // Create Contact
    { name: "firstName", required: true, type: "string" },
    { name: "lastName", required: false, type: "string" },
    { name: "email", required: true, type: "string" },
    { name: "phone", required: true, type: "string" },
    { name: "companyName", required: false, type: "string" },
    { name: "city", required: false, type: "string" },
    { name: "state", required: false, type: "string" },
    { name: "address", required: false, type: "string" },
    { name: "pincode", required: false, type: "string" },
    { name: "dateOfBirth", required: false, type: "string" },
    { name: "dateOfAnniversary", required: false, type: "string" },
  ],
  "adf4cbfe-90d0-447a-b4af-d17c3ca5402d": [
    // Create Product
    { name: "productName", required: true, type: "string" },
    { name: "productCode", required: false, type: "string" },
    { name: "price", required: false, type: "number" },
    { name: "description", required: false, type: "string" },
    { name: "imageUrl", required: false, type: "string" },
    { name: "maxDiscount", required: false, type: "number" },
    { name: "hsnCode", required: false, type: "string" },
  ],
};

interface AutomateCRMConnectionDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: {
    formIntegatedId: string;
    credentialId: string;
    credentialName: string;
    enabled: boolean;
    connectedAt: string;
    metadata: {
      Stage: string;
      Source: string;
      assignedTo: string;
      pipelineId: string;
      createdBy?: string;
      country?: string;
      categoryId?: string;
      unitId?: string;
    };
    mappedData: {
      id: string;
      name: string;
      title: string;
      key: string;
    }[];
    actionId: string;
  }[];
  onRefresh?: () => void;
}

export default function AutomateCRMConnectionDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: AutomateCRMConnectionDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [isLoadingCustomFields, setIsLoadingCustomFields] = useState(false);
  const [formData, setFormData] = useState({
    form_id: "",
    integration_id: "",
    credential_id: "",
    action_id: "",
    Source: "",
    Stage: "",
    assignedTo: "",
    createdBy: "",
    country: "",
    pipelineId: "",
    categoryId: "",
    unitId: "",
    column_mapped_data: [] as any[],
    // Lead fields
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    description: "",
    requirement: "",
    amount: "",
    title: "",
    companyName: "",
    // Contact fields
    city: "",
    state: "",
    address: "",
    pincode: "",
    dateOfBirth: "",
    dateOfAnniversary: "",
    // Product fields
    productName: "",
    productCode: "",
    price: "",
    imageUrl: "",
    maxDiscount: "",
    hsnCode: "",
    // Templates
    firstNameTemplate: "",
    lastNameTemplate: "",
    emailTemplate: "",
    phoneTemplate: "",
    descriptionTemplate: "",
    requirementTemplate: "",
    amountTemplate: "",
    titleTemplate: "",
    companyNameTemplate: "",
    cityTemplate: "",
    stateTemplate: "",
    addressTemplate: "",
    pincodeTemplate: "",
    dateOfBirthTemplate: "",
    dateOfAnniversaryTemplate: "",
    productNameTemplate: "",
    productCodeTemplate: "",
    priceTemplate: "",
    imageUrlTemplate: "",
    maxDiscountTemplate: "",
    hsnCodeTemplate: "",
    customFields: {} as Record<string, string>,
  });
  const [crmData, setCrmData] = useState<{
    pipelines: any[];
    sources: any[];
    salesPersons: any[];
    stages: any[];
    countries: any[];
    categories: any[];
    units: any[];
  }>({
    pipelines: [],
    sources: [],
    salesPersons: [],
    stages: [],
    countries: [],
    categories: [],
    units: [],
  });
  const [isSaving, setIsSaving] = useState(false);

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  const addCRMConnectionMutation = useAddCRMConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { data: connectionKeyData } =
    useGetCRMConnectionKey(selectedConnection);
  const { mutate: linkCRMForm, isPending: isLinking } = useLinkCRMForm();
  const { data: formFields, isLoading } = useGetFormFields(formId!);

  const { mutate: updateCRMForm, isPending: isUpdating } = useUpdateCRMForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;

  // Add loading state for stages
  const [isLoadingStages, setIsLoadingStages] = useState(false);

  const { mutate: disconnectIntegration, isPending: isDisconnecting } =
    useDisconnectIntegration();

  // Get current action field configuration
  const getCurrentActionFields = () => {
    return ACTION_FIELD_CONFIGS[selectedAction] || [];
  };

  // Check if field is required for current action
  const isFieldRequired = (fieldName: string) => {
    const fields = getCurrentActionFields();
    const field = fields.find((f) => f.name === fieldName);
    return field?.required || false;
  };

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
    }
  }, [initialActionId]);

  useEffect(() => {
    const fetchCRMData = async () => {
      if (connectionKeyData?.data?.key) {
        const apiKey = connectionKeyData.data.key;
        try {
          // Fetch common data for all actions
          const [salesPersons, countries] = await Promise.all([
            fetchSalesPersons(apiKey),
            fetchCountries(),
          ]);

          setCrmData((prev) => ({
            ...prev,
            salesPersons: salesPersons?.data?.data || [],
            countries: countries?.data?.data || [],
          }));

          // Fetch lead-specific data only for create lead action
          if (selectedAction === "051b8767-7bf9-4c17-9cc4-1a8c0338187f") {
            const [pipelines, sources] = await Promise.all([
              fetchPipelines(apiKey),
              fetchCrmLeadSources(apiKey),
            ]);

            setCrmData((prev) => ({
              ...prev,
              pipelines: pipelines?.data?.data || [],
              sources: sources?.data?.data || [],
            }));
          } else if (
            selectedAction === "adf4cbfe-90d0-447a-b4af-d17c3ca5402d"
          ) {
            // Create Product - fetch categories and units
            const [categories, units] = await Promise.all([
              fetchProductCategories(apiKey),
              fetchProductUnits(apiKey),
            ]);

            setCrmData((prev) => ({
              ...prev,
              categories: categories?.data?.data || [],
              units: units?.data?.data || [],
            }));
          } else {
            // Reset lead-specific data for other actions
            setCrmData((prev) => ({
              ...prev,
              pipelines: [],
              sources: [],
              stages: [],
            }));
          }
        } catch (error) {
          console.error("Error fetching CRM data:", error);
        }
      }
    };

    if (selectedConnection) {
      fetchCRMData();
    }
  }, [selectedConnection, connectionKeyData, selectedAction]);

  // Utility to check if a template references only valid fields
  function isTemplateValid(template: string, fields: any[]): boolean {
    const regex = /\{\{([a-f0-9-]+)(?:\.[^\}]+)?\}\}/gi;
    let match;
    while ((match = regex.exec(template)) !== null) {
      const fieldId = match[1];
      if (!fields.some((f) => f.id === fieldId)) {
        return false;
      }
    }
    return true;
  }

  // Helper to build options for SelectInputCombo, handling NAME_INPUT specially
  const buildFieldOptions = (fields: any[]) => {
    const options: {
      id: string;
      name: string;
      title: string;
      index: number;
    }[] = [];
    let logicalIndex = 1;
    fields.forEach((f) => {
      if (f.component === "NAME_INPUT") {
        options.push({
          id: f.id,
          name: "firstName",
          title: f.firstNameTitle || "First Name",
          index: logicalIndex,
        });
        options.push({
          id: f.id,
          name: "lastName",
          title: f.lastNameTitle || "Last Name",
          index: logicalIndex,
        });
        logicalIndex++;
      } else if (f.component === "ADDRESS") {
        options.push({
          id: f.id,
          name: "address",
          title: "Address",
          index: logicalIndex,
        });
        const allowed = f.allowedAddressFields || {
          country: true,
          city: true,
          pincode: true,
          state: true,
        };
        if (allowed.city) {
          options.push({
            id: f.id,
            name: "city",
            title: "City",
            index: logicalIndex,
          });
        }
        if (allowed.state) {
          options.push({
            id: f.id,
            name: "state",
            title: "State",
            index: logicalIndex,
          });
        }
        if (allowed.country) {
          options.push({
            id: f.id,
            name: "country",
            title: "Country",
            index: logicalIndex,
          });
        }
        if (allowed.pincode) {
          options.push({
            id: f.id,
            name: "pincode",
            title: "Pincode",
            index: logicalIndex,
          });
        }
        logicalIndex++;
      } else {
        options.push({
          id: f.id,
          name: f.name,
          title: f.title || f.name,
          index: logicalIndex,
        });
        logicalIndex++;
      }
    });
    return options;
  };

  // Helper to convert backend format to template format (handles multiple fields in a string)
  const convertMappedToTemplate = (key: string, options: any[]): string => {
    if (!key) return "";
    // Handles keys like {{fieldId.name.firstName}}, {{fieldId.address.city}}, {{fieldId}}, etc.
    // 1. Handle keys with dot notation
    let result = key.replace(
      /\{\{([^.\}]+)(?:\.[^.\}]+)*\.([^.\}]+)\}\}/g,
      (match, fieldId, fieldName) => {
        const opt = options.find(
          (opt) => opt.id === fieldId && opt.name === fieldName
        );
        if (!opt) return match;
        return `{{${opt.index}.${fieldName}}}`;
      }
    );
    // 2. Handle keys with only fieldId (no dot)
    result = result.replace(/\{\{([a-f0-9-]+)\}\}/gi, (match, fieldId) => {
      const opt = options.find((opt) => opt.id === fieldId);
      if (!opt) return match;
      return `{{${opt.index}.${opt.name}}}`;
    });
    return result;
  };

  // Update the useEffect that handles existing connections
  useEffect(() => {
    if (existingConnections.length > 0 && formFields?.data?.fields) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      setSelectedConnection(connection.credentialId);
      setConnectionName(connection.credentialName);
      setSelectedAction(connection.actionId);

      setFormData((prev) => ({
        ...prev,
        Source: connection.metadata.Source || "",
        Stage: connection.metadata.Stage || "",
        assignedTo: connection.metadata.assignedTo || "",
        pipelineId: connection.metadata.pipelineId || "",
        createdBy: connection.metadata.createdBy || "",
        country: connection.metadata.country || "",
      }));

      const fields = formFields.data.fields;
      const options = buildFieldOptions(fields);
      const initialCustomFields: Record<string, string> = {};

      connection.mappedData.forEach((mappedField) => {
        const templateValue = convertMappedToTemplate(mappedField.key, options);
        switch (mappedField.name) {
          case "firstName":
            setFormData((prev) => ({
              ...prev,
              firstNameTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "lastName":
            setFormData((prev) => ({
              ...prev,
              lastNameTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "email":
            setFormData((prev) => ({
              ...prev,
              emailTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "phone":
            setFormData((prev) => ({
              ...prev,
              phoneTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "description":
            setFormData((prev) => ({
              ...prev,
              descriptionTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "requirement":
            setFormData((prev) => ({
              ...prev,
              requirementTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "amount":
            setFormData((prev) => ({
              ...prev,
              amountTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "title":
            setFormData((prev) => ({
              ...prev,
              titleTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "companyName":
            setFormData((prev) => ({
              ...prev,
              companyNameTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "city":
            setFormData((prev) => ({
              ...prev,
              cityTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "state":
            setFormData((prev) => ({
              ...prev,
              stateTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "address":
            setFormData((prev) => ({
              ...prev,
              addressTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "pincode":
            setFormData((prev) => ({
              ...prev,
              pincodeTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "productName":
            setFormData((prev) => ({
              ...prev,
              productNameTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "productCode":
            setFormData((prev) => ({
              ...prev,
              productCodeTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "price":
            setFormData((prev) => ({
              ...prev,
              priceTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "imageUrl":
            setFormData((prev) => ({
              ...prev,
              imageUrlTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "maxDiscount":
            setFormData((prev) => ({
              ...prev,
              maxDiscountTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          case "hsnCode":
            setFormData((prev) => ({
              ...prev,
              hsnCodeTemplate: isTemplateValid(mappedField.key, fields)
                ? templateValue
                : "",
            }));
            break;
          default:
            if (
              mappedField.id &&
              mappedField.id.toString().startsWith("customfield_")
            ) {
              const fieldId = mappedField.id
                .toString()
                .replace("customfield_", "");
              const customField = customFields.find(
                (f) => f.id.toString() === fieldId
              );
              if (customField) {
                initialCustomFields[customField.name] = isTemplateValid(
                  mappedField.key,
                  fields
                )
                  ? templateValue
                  : "";
              }
            }
            break;
        }
      });
      setFormData((prev) => ({
        ...prev,
        customFields: initialCustomFields,
      }));
    }
  }, [existingConnections, formFields?.data?.fields, customFields]);

  // Update the stages fetching useEffect
  useEffect(() => {
    const fetchStages = async () => {
      if (
        formData.pipelineId &&
        connectionKeyData?.data?.key &&
        selectedAction === "051b8767-7bf9-4c17-9cc4-1a8c0338187f"
      ) {
        setIsLoadingStages(true);
        try {
          const stages = await fetchCrmStages(
            connectionKeyData.data.key,
            formData.pipelineId
          );
          setCrmData((prev) => ({
            ...prev,
            stages: stages?.data?.data || [],
          }));
        } catch (error) {
          console.error("Error fetching stages:", error);
        } finally {
          setIsLoadingStages(false);
        }
      } else {
        // Reset stages when pipeline is not selected or not create lead action
        setCrmData((prev) => ({
          ...prev,
          stages: [],
        }));
        setFormData((prev) => ({
          ...prev,
          Stage: "",
        }));
      }
    };

    fetchStages();
  }, [formData.pipelineId, connectionKeyData, selectedAction]);

  // Update the custom fields useEffect
  const fetchCustomFields = async () => {
    if (!connectionKeyData?.data?.key) return;

    try {
      let customFieldsData;

      if (selectedAction === "051b8767-7bf9-4c17-9cc4-1a8c0338187f") {
        // Create Lead - use pipeline-based custom fields
        if (!formData.pipelineId) return;
        customFieldsData = await fetchCrmCustomFields(
          connectionKeyData.data.key,
          formData.pipelineId
        );
      } else if (selectedAction === "f3ce3e59-44e8-4a08-95fa-259348b8ba73") {
        // Create Contact - use contact custom fields
        customFieldsData = await fetchContactCustomFields(
          connectionKeyData.data.key
        );
      } else if (selectedAction === "adf4cbfe-90d0-447a-b4af-d17c3ca5402d") {
        // Create Product - use product custom fields
        customFieldsData = await fetchProductCustomFields(
          connectionKeyData.data.key
        );
      } else {
        // No custom fields for other actions
        setCustomFields([]);
        return;
      }

      if (customFieldsData?.existingCustomField) {
        setCustomFields(customFieldsData.existingCustomField);

        // Initialize custom fields in formData
        const initialCustomFields: Record<string, string> = {};
        customFieldsData.existingCustomField.forEach((field: CustomField) => {
          initialCustomFields[field.name] =
            formData.customFields[field.name] || "";
        });
        setFormData((prev) => ({
          ...prev,
          customFields: initialCustomFields,
        }));
      } else {
        console.warn("No custom fields data received");
        setCustomFields([]);
      }
    } catch (error) {
      console.error("Error fetching custom fields:", error);
      toast.error("Failed to fetch custom fields");
      setCustomFields([]);
    }
  };

  useEffect(() => {
    fetchCustomFields();
  }, [formData.pipelineId, connectionKeyData?.data?.key, selectedAction]);

  useEffect(() => {
    if (saveSuccess && connectionName && connections.length > 0) {
      const newConnection = connections.find(
        (conn: Connection) => conn.name === connectionName
      );
      if (newConnection) {
        setConnectionType("existing");
        setSelectedConnection(newConnection.id);
      }
    }
  }, [connections, connectionName, saveSuccess]);

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: () => {
          toast.success("CRM disconnected successfully!");
          onRefresh?.();
          onClose();
        },
        onError: (error: Error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect CRM. Please try again.");
        },
      }
    );
  };

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
  };

  const handleFormDataChange = async (
    field: keyof typeof formData,
    value: any
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // If pipeline is changed, reset stage value
    if (field === "pipelineId") {
      setFormData((prev) => ({ ...prev, Stage: "" })); // Reset stage when pipeline changes
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName || !apiKey) return;

    setIsAddingConnection(true);
    try {
      const response = await addCRMConnectionMutation.mutateAsync({
        integrationId,
        name: connectionName,
        key: apiKey,
      });

      if (response?.success) {
        setSaveSuccess(true);
        setSuccessMessage(
          `Connection "${connectionName}" added successfully! You can now configure the CRM settings.`
        );
        await refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleSave = async () => {
    if (connectionType === "existing") {
      setIsSaving(true);
      try {
        const columnMappedData: any[] = [];
        const fields = formFields?.data?.fields || [];
        const options = buildFieldOptions(fields);
        const currentActionFields = getCurrentActionFields();

        // Handle fields based on current action
        currentActionFields.forEach((fieldConfig) => {
          const templateField = `${fieldConfig.name}Template`;
          const templateValue = formData[
            templateField as keyof typeof formData
          ] as string;

          if (templateValue) {
            columnMappedData.push({
              id: fieldConfig.name,
              name: fieldConfig.name,
              title:
                fieldConfig.name.charAt(0).toUpperCase() +
                fieldConfig.name.slice(1),
              key: convertTemplateToMapped(templateValue, options, fields),
            });
          }
        });

        // Add custom fields to columnMappedData
        Object.entries(formData.customFields).forEach(
          ([fieldName, template]) => {
            if (template) {
              const customField = customFields.find(
                (f) => f.name === fieldName
              );
              if (customField) {
                columnMappedData.push({
                  id: `customfield_${customField.id}`,
                  name: customField.name,
                  title: customField.name,
                  key: convertTemplateToMapped(template, options, fields),
                });
              }
            }
          }
        );

        if (existingConnections.length > 0) {
          // Update existing connection
          const updateData: any = {
            form_integration_id: existingConnections[0].formIntegatedId,
            action_id: selectedAction,
            column_mapped_data: columnMappedData,
          };

          // Add action-specific metadata
          if (selectedAction === "051b8767-7bf9-4c17-9cc4-1a8c0338187f") {
            // Create Lead
            updateData.pipelineId = formData.pipelineId;
            updateData.Source = formData.Source;
            updateData.Stage = formData.Stage;
            updateData.assignedTo = formData.assignedTo;
          } else if (
            selectedAction === "f3ce3e59-44e8-4a08-95fa-259348b8ba73"
          ) {
            // Create Contact
            updateData.assignedTo = formData.assignedTo;
            updateData.createdBy = formData.createdBy;
            updateData.country = formData.country;
          } else if (
            selectedAction === "adf4cbfe-90d0-447a-b4af-d17c3ca5402d"
          ) {
            // Create Product
            updateData.categoryId = formData.categoryId;
            updateData.unitId = formData.unitId;
          }

          updateCRMForm(updateData, {
            onSuccess: () => {
              toast.success("CRM updated successfully!");
              onRefresh?.();
              onClose();
            },
            onError: (error: Error) => {
              console.error("Error updating CRM:", error);
              toast.error("Failed to update CRM. Please try again.");
            },
          });
        } else {
          // Create new connection
          const linkFormData: any = {
            form_id: formId || "",
            integration_id: integrationId,
            credential_id: selectedConnection,
            action_id: selectedAction,
            column_mapped_data: columnMappedData,
          };

          // Add action-specific metadata
          if (selectedAction === "051b8767-7bf9-4c17-9cc4-1a8c0338187f") {
            // Create Lead
            linkFormData.pipelineId = formData.pipelineId;
            linkFormData.Source = formData.Source;
            linkFormData.Stage = formData.Stage;
            linkFormData.assignedTo = formData.assignedTo;
          } else if (
            selectedAction === "f3ce3e59-44e8-4a08-95fa-259348b8ba73"
          ) {
            // Create Contact
            linkFormData.assignedTo = formData.assignedTo;
            linkFormData.createdBy = formData.createdBy;
            linkFormData.country = formData.country;
          } else if (
            selectedAction === "adf4cbfe-90d0-447a-b4af-d17c3ca5402d"
          ) {
            // Create Product
            linkFormData.categoryId = formData.categoryId;
            linkFormData.unitId = formData.unitId;
          }

          linkCRMForm(linkFormData, {
            onSuccess: (response: any) => {
              if (response?.success) {
                setSaveSuccess(true);
                setSuccessMessage("CRM linked successfully!");
                onRefresh?.();
                onClose();
              }
            },
            onError: (error: Error) => {
              console.error("Error linking CRM form:", error);
              toast.error("Failed to link CRM. Please try again.");
            },
          });
        }
      } catch (error) {
        console.error("Error preparing form data:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSaving(false);
      }
    }
  };

  const isFormValid = () => {
    const currentActionFields = getCurrentActionFields();
    const requiredFields = currentActionFields.filter(
      (field) => field.required
    );

    const hasRequiredFields = requiredFields.every((field) => {
      const templateField = `${field.name}Template`;
      return formData[templateField as keyof typeof formData];
    });

    const hasRequiredCustomFields = customFields
      .filter((field) => field.mandatory)
      .every((field) => formData.customFields[field.name]);

    // Different validation for different actions
    let hasBasicRequirements = false;

    if (selectedAction === "051b8767-7bf9-4c17-9cc4-1a8c0338187f") {
      // Create Lead - requires pipeline, stage, source, assignedTo
      hasBasicRequirements = Boolean(
        selectedConnection &&
          formData.assignedTo &&
          formData.pipelineId &&
          formData.Stage &&
          formData.Source &&
          hasRequiredFields &&
          hasRequiredCustomFields
      );
    } else if (selectedAction === "f3ce3e59-44e8-4a08-95fa-259348b8ba73") {
      // Create Contact - requires assignedTo, createdBy, country
      hasBasicRequirements = Boolean(
        selectedConnection &&
          formData.assignedTo &&
          formData.createdBy &&
          formData.country &&
          hasRequiredFields &&
          hasRequiredCustomFields
      );
    } else if (selectedAction === "adf4cbfe-90d0-447a-b4af-d17c3ca5402d") {
      // Create Product - requires categoryId, unitId, and action fields
      hasBasicRequirements = Boolean(
        selectedConnection &&
          formData.categoryId &&
          formData.unitId &&
          hasRequiredFields
      );
    }

    return hasBasicRequirements && !isSaving;
  };

  // Render field based on action type
  const renderActionFields = () => {
    const currentActionFields = getCurrentActionFields();

    return currentActionFields.map((fieldConfig) => {
      const templateField = `${fieldConfig.name}Template`;
      const templateValue = formData[
        templateField as keyof typeof formData
      ] as string;

      return (
        <div key={fieldConfig.name}>
          <label className="block text-sm font-medium mb-1">
            {fieldConfig.name.charAt(0).toUpperCase() +
              fieldConfig.name.slice(1)}
            {fieldConfig.required && (
              <span className="text-red-500 ml-1">*</span>
            )}
          </label>
          <SelectInputCombo
            options={buildFieldOptions(formFields?.data?.fields || [])}
            value={templateValue || ""}
            onChange={(val) =>
              handleFormDataChange(templateField as keyof typeof formData, val)
            }
            placeholder={`Type or insert fields for ${fieldConfig.name}`}
          />
        </div>
      );
    });
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div className="fixed inset-0 bg-black/50 z-[199] transition-opacity" />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect Automate Sales CRM
                  </h2>
                  <span className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </span>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">Automate Sales CRM</h3>

              {saveSuccess && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center justify-between">
                  <p className="text-sm text-green-800">{successMessage}</p>
                  <button
                    onClick={() => setSaveSuccess(false)}
                    className="text-green-600 hover:text-green-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action) => (
                      <option key={action.id} value={action.id}>
                        {action.name.charAt(0).toUpperCase() +
                          action.name.slice(1)}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>

                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>

                    {connectionType === "new" ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Connection Name
                          </label>
                          <input
                            type="text"
                            value={connectionName}
                            onChange={(e) => setConnectionName(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter connection name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            API Key
                          </label>
                          <input
                            type="text"
                            value={apiKey}
                            onChange={(e) => setApiKey(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your API key"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter the API Key here. To obtain the API Key, log
                            in to your Automate Sales CRM account.
                          </p>
                        </div>
                        <button
                          onClick={handleAddConnection}
                          disabled={
                            !connectionName || !apiKey || isAddingConnection
                          }
                          className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                            !connectionName || !apiKey || isAddingConnection
                              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                              : "bg-app-text-color text-app-background hover:bg-opacity-90"
                          }`}
                        >
                          {isAddingConnection ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <Network className="h-4 w-4" />
                              Add New Connection
                            </>
                          )}
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Select Connection
                          </label>
                          <select
                            value={selectedConnection}
                            onChange={handleConnectionSelect}
                            disabled={!hasValidConnections}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                          >
                            <option value="">Select a connection</option>
                            {connections.map((connection) => (
                              <option key={connection.id} value={connection.id}>
                                {connection.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {selectedConnection && (
                          <div className="space-y-4">
                            {/* Default fields based on action type */}
                            {selectedAction ===
                              "051b8767-7bf9-4c17-9cc4-1a8c0338187f" && (
                              <>
                                {/* Create Lead specific fields */}
                                <div>
                                  <SearchableSelect
                                    label="Pipeline"
                                    required
                                    options={crmData.pipelines.map(
                                      (pipeline) => ({
                                        id: pipeline.id.toString(),
                                        name: pipeline.name,
                                      })
                                    )}
                                    value={formData.pipelineId}
                                    onChange={(value) =>
                                      handleFormDataChange("pipelineId", value)
                                    }
                                    placeholder="Select pipeline"
                                  />
                                </div>

                                {/* Stage select - only show if pipeline is selected */}
                                {formData.pipelineId && (
                                  <div>
                                    <label className="block text-sm font-medium mb-1">
                                      Stage
                                      <span className="text-red-500 ml-1">
                                        *
                                      </span>
                                    </label>
                                    <select
                                      value={formData.Stage}
                                      onChange={(e) =>
                                        handleFormDataChange(
                                          "Stage",
                                          e.target.value
                                        )
                                      }
                                      disabled={isLoadingStages}
                                      className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                    >
                                      <option value="" className="truncate">
                                        Select stage
                                      </option>
                                      {crmData.stages.map((stage) => (
                                        <option
                                          key={stage.id}
                                          value={stage.id}
                                          className="truncate"
                                        >
                                          {stage.name}
                                        </option>
                                      ))}
                                    </select>
                                    {isLoadingStages && (
                                      <div className="mt-1 text-sm text-gray-500">
                                        Loading stages...
                                      </div>
                                    )}
                                  </div>
                                )}

                                <div>
                                  <SearchableSelect
                                    label="Source"
                                    required
                                    options={crmData.sources.map((source) => ({
                                      id: source.id,
                                      name: source.name,
                                    }))}
                                    value={formData.Source}
                                    onChange={(value) =>
                                      handleFormDataChange("Source", value)
                                    }
                                    placeholder="Select source"
                                  />
                                </div>
                              </>
                            )}

                            {/* Common field for all actions */}
                            {selectedAction !==
                              "adf4cbfe-90d0-447a-b4af-d17c3ca5402d" && (
                              <div>
                                <SearchableSelect
                                  label="Assigned To"
                                  required
                                  options={crmData.salesPersons.map(
                                    (person) => ({
                                      id: person.id,
                                      name: person.name,
                                    })
                                  )}
                                  value={formData.assignedTo}
                                  onChange={(value) =>
                                    handleFormDataChange("assignedTo", value)
                                  }
                                  placeholder="Select sales person"
                                />
                              </div>
                            )}

                            {/* Create Contact specific fields */}
                            {selectedAction ===
                              "f3ce3e59-44e8-4a08-95fa-259348b8ba73" && (
                              <>
                                <div>
                                  <SearchableSelect
                                    label="Created By"
                                    required
                                    options={crmData.salesPersons.map(
                                      (person) => ({
                                        id: person.user_id,
                                        name: person.name,
                                      })
                                    )}
                                    value={formData.createdBy}
                                    onChange={(value) =>
                                      handleFormDataChange("createdBy", value)
                                    }
                                    placeholder="Select sales person"
                                  />
                                </div>

                                <div>
                                  <SearchableSelect
                                    label="Country"
                                    required
                                    options={crmData.countries.map(
                                      (country) => ({
                                        id: country.id,
                                        name: country.name,
                                      })
                                    )}
                                    value={formData.country}
                                    onChange={(value) =>
                                      handleFormDataChange("country", value)
                                    }
                                    placeholder="Select country"
                                  />
                                </div>
                              </>
                            )}

                            {/* Create Product specific fields */}
                            {selectedAction ===
                              "adf4cbfe-90d0-447a-b4af-d17c3ca5402d" && (
                              <>
                                <div>
                                  <SearchableSelect
                                    label="Category"
                                    required
                                    options={crmData.categories.map(
                                      (category) => ({
                                        id: category.id.toString(),
                                        name: category.name,
                                      })
                                    )}
                                    value={formData.categoryId}
                                    onChange={(value) =>
                                      handleFormDataChange("categoryId", value)
                                    }
                                    placeholder="Select category"
                                  />
                                </div>

                                <div>
                                  <SearchableSelect
                                    label="Unit"
                                    required
                                    options={crmData.units.map((unit) => ({
                                      id: unit.id,
                                      name: unit.name,
                                    }))}
                                    value={formData.unitId}
                                    onChange={(value) =>
                                      handleFormDataChange("unitId", value)
                                    }
                                    placeholder="Select unit"
                                  />
                                </div>
                              </>
                            )}

                            {/* Dynamic action fields */}
                            <div className="space-y-4">
                              <h3 className="text-lg font-semibold text-app-text-color capitalize">
                                {
                                  actions.find((a) => a.id === selectedAction)
                                    ?.name
                                }{" "}
                                Fields
                              </h3>
                              {renderActionFields()}
                            </div>

                            {/* Custom Fields Section */}
                            {customFields.length > 0 && (
                              <div className="space-y-4">
                                {customFields.length !== 0 && (
                                  <h3 className="text-lg font-semibold text-app-text-color">
                                    Custom Fields
                                  </h3>
                                )}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  {customFields.map((field) => (
                                    <div key={field.id} className="space-y-2">
                                      <label className="block text-sm font-medium text-app-text-color capitalize">
                                        {field.name}
                                        {field.mandatory && (
                                          <span className="text-red-500 ml-1">
                                            *
                                          </span>
                                        )}
                                      </label>
                                      <SelectInputCombo
                                        options={buildFieldOptions(
                                          formFields?.data?.fields || []
                                        )}
                                        value={
                                          formData.customFields[field.name] ||
                                          ""
                                        }
                                        onChange={(val) =>
                                          setFormData((prev) => ({
                                            ...prev,
                                            customFields: {
                                              ...prev.customFields,
                                              [field.name]: val,
                                            },
                                          }))
                                        }
                                        placeholder={`Type or insert fields for ${field.name}`}
                                      />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {existingConnections.length > 0 ? (
                  <>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        isDisconnecting
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-red-500 text-white hover:bg-red-600"
                      }`}
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        "Disconnect"
                      )}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={!isFormValid()}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !isFormValid()
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Update"
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleSave}
                    disabled={!isFormValid()}
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      !isFormValid()
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
