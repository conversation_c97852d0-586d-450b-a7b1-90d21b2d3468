import { useState, useEffect, useRef } from "react";

interface TableFilterProps {
  data: Record<string, any>[];
  onFilterChange?: (selectedKeys: string[]) => void;
  numberColumns?: string[];
}

function TableFilter({ data, onFilterChange, numberColumns = [] }: TableFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [numberFilters, setNumberFilters] = useState<Record<string, { type: string; value: string; value2?: string }>>({});

  const numberFilterTypes = [
    "Greater than",
    "Greater than or equal to",
    "Less than",
    "Less than or equal to",
    "Equal to",
    "Not equal to",
    "Between",
    "Not between",
  ];

  // Get keys from first element of data
  const keys = data?.length > 0 ? Object.keys(data[0]) : [];

  const handleCheckboxChange = (key: string) => {
    const newSelectedKeys = selectedKeys.includes(key)
      ? selectedKeys.filter((k) => k !== key)
      : [...selectedKeys, key];

    setSelectedKeys(newSelectedKeys);
    onFilterChange?.(newSelectedKeys);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={dropdownRef}>

      <button
        onClick={() => setIsOpen(!isOpen)}
        className="px-4 py-2 border border-app-border-primary rounded-md bg-app-main-background text-app-text-color"
      >
        Filter Columns ({selectedKeys.length})
      </button>
   

      {isOpen && (
        <div className="absolute z-10 mt-2 w-[300px] bg-app-main-background border border-app-border-primary rounded-md shadow-lg cursor-pointer">
          <div className="max-h-60 overflow-y-auto">
            {keys.map((key) => (
              <div
                key={key}
                className="flex flex-col px-4 py-2 hover:bg-app-border-primary text-app-text-color hover:text-app-background"
              >
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedKeys.includes(key)}
                    onChange={() => handleCheckboxChange(key)}
                    className="mr-3"
                  />
                  <span className="truncate">{key}</span>
                </div>
                {/* Advanced filter for number columns */}
                {numberColumns.includes(key) && selectedKeys.includes(key) && (
                  <div className="mt-2 flex flex-col gap-2">
                    <select
                      className="border rounded p-1 text-xs"
                      value={numberFilters[key]?.type || numberFilterTypes[0]}
                      onChange={e => {
                        setNumberFilters(prev => ({
                          ...prev,
                          [key]: {
                            ...prev[key],
                            type: e.target.value,
                          },
                        }));
                      }}
                    >
                      {numberFilterTypes.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                    {/* Value input(s) */}
                    {numberFilters[key]?.type === 'Between' || numberFilters[key]?.type === 'Not between' ? (
                      <div className="flex gap-2">
                        <input
                          type="number"
                          className="border rounded p-1 text-xs w-1/2"
                          placeholder="Min"
                          value={numberFilters[key]?.value || ''}
                          onChange={e => setNumberFilters(prev => ({
                            ...prev,
                            [key]: {
                              ...prev[key],
                              value: e.target.value,
                            },
                          }))}
                        />
                        <input
                          type="number"
                          className="border rounded p-1 text-xs w-1/2"
                          placeholder="Max"
                          value={numberFilters[key]?.value2 || ''}
                          onChange={e => setNumberFilters(prev => ({
                            ...prev,
                            [key]: {
                              ...prev[key],
                              value2: e.target.value,
                            },
                          }))}
                        />
                      </div>
                    ) : (
                      <input
                        type="number"
                        className="border rounded p-1 text-xs"
                        placeholder="Value"
                        value={numberFilters[key]?.value || ''}
                        onChange={e => setNumberFilters(prev => ({
                          ...prev,
                          [key]: {
                            ...prev[key],
                            value: e.target.value,
                          },
                        }))}
                      />
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default TableFilter;
