"use client";
import React, { useState } from "react";
import { Eye, Trash2 } from "lucide-react";
import RaiseTicketDialog from "@/components/support/raiseTicketDialog";
import TicketDetailsDialog from "@/components/support/TicketDetailsDialog";
import { useGetAllTickets, useDeleteTicket } from "@/api-services/support";
import { useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import AppPagination from "@/components/common/app-pagination";

interface Ticket {
  id: string;
  subject: string;
  created_at: string;
  status: "Closed" | "Inprogress" | "Pending";
}

const TICKET_STATUS = {
  Closed: { label: "Closed", color: "bg-green-100 text-green-700" },
  Inprogress: { label: "In progress", color: "bg-yellow-100 text-yellow-700" },
  Pending: { label: "Pending", color: "bg-red-100 text-red-700" },
};

const STATUS_MAP: Record<string, Ticket["status"]> = {
  inprogress: "Inprogress",
  pending: "Pending",
  closed: "Closed",
};

const TABS = [
  { key: "all", label: "All" },
  { key: "inprogress", label: "In-Progress" },
  { key: "pending", label: "Pending" },
  { key: "closed", label: "Closed" },
];

const Page = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [showRaiseTicket, setShowRaiseTicket] = useState(false);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const queryClient = useQueryClient();

  const { data: tickets, isLoading, error } = useGetAllTickets();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [deleteTicketId, setDeleteTicketId] = useState<string | null>(null);
  const deleteTicketMutation = useDeleteTicket(deleteTicketId || "");

  console.log(tickets, "tickets");

  const allFilteredTickets = React.useMemo(() => {
    const ticketsData = tickets?.data?.tickets || [];
    return activeTab === "all"
      ? ticketsData
      : ticketsData.filter((t: Ticket) => t.status === STATUS_MAP[activeTab]);
  }, [tickets?.data, activeTab]);

  // Apply pagination to filtered tickets
  const paginatedTickets = React.useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return allFilteredTickets.slice(startIndex, startIndex + itemsPerPage);
  }, [allFilteredTickets, currentPage, itemsPerPage]);

  // Reset to first page when tab changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  const getTicketCount = (status: string) => {
    const ticketsData = tickets?.data?.tickets || [];
    if (status === "all") return ticketsData.length;
    return ticketsData.filter((t: Ticket) => t.status === STATUS_MAP[status])
      .length;
  };

  return (
    <div>
      <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
        <h1 className="text-3xl font-semibold text-left text-app-text-color">
          Support
        </h1>

        {/* Help Card */}
        <div className="flex flex-col md:flex-row items-center justify-between bg-app-background border rounded-2xl p-8 mb-12 gap-8">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-app-text-color mb-2">
              Get the Help You Need
            </h2>
            <p className="text-app-text-secondary mb-6 text-base">
              Find answers, get support, or talk to our team — anytime you need.
            </p>
            <div className="flex items-center gap-4 flex-wrap">
              <button
                className="border px-4 py-1 rounded bg-app-text-color border-app-border-primary text-app-background hover:bg-app-main-background hover:text-app-text-color font-medium shadow-sm transition-all"
                onClick={() => setShowRaiseTicket(true)}
              >
                Raise a Ticket
              </button>
              <span className="text-app-text-secondary text-sm">OR</span>
              <button
                className="border px-4 py-1 rounded bg-app-main-background hover:bg-app-text-color border-[#1F311C] hover:text-app-background text-app-text-color font-medium shadow-sm transition-all"
                onClick={() => {
                  if (typeof window !== "undefined" && window.$crisp) {
                    window.$crisp.push(["do", "chat:open"]);
                  }
                }}
              >
                Chat with us
              </button>
            </div>
          </div>
          <div className="mt-6 md:mt-0 md:ml-8">
            {/* Placeholder illustration */}
            <Image
              src="/support.png"
              alt="Support Illustration"
              className="w-32 h-32 object-contain"
              quality={100}
              width={100}
              height={100}
            />
          </div>
        </div>
      </div>

      {/* Tickets Section */}
      <div className="bg-app-background border rounded-2xl p-6">
        <h2 className="text-xl font-semibold text-app-text-color mb-6">
          Tickets
        </h2>
        {/* Tabs */}
        <div className="flex gap-6 border-b mb-6">
          {TABS.map((tab) => (
            <button
              key={tab.key}
              className={`pb-2 font-semibold text-base border-b-2 transition-colors ${
                activeTab === tab.key
                  ? "border-app-border-primary text-app-text-color"
                  : "border-transparent text-app-text-secondary"
              }`}
              onClick={() => setActiveTab(tab.key)}
            >
              <div className="flex items-center gap-2">
                {tab.label}
                <span
                  className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                    activeTab === tab.key
                      ? "bg-app-hero-background text-app-text-color"
                      : "bg-app-text-color text-app-background"
                  }`}
                >
                  {getTicketCount(tab.key)}
                </span>
              </div>
            </button>
          ))}
        </div>
        {/* Table */}
        <div className="overflow-x-auto">
          <div className="max-h-[400px] overflow-y-auto scroller-style">
            <table className="min-w-full text-sm">
              <thead className="sticky top-0 text-app-text-secondary bg-app-background z-10">
                <tr className="text-left border-b">
                  <th className="py-3 px-3 font-semibold w-[120px]">
                    Ticket ID
                  </th>
                  <th className="py-3 px-3 font-semibold w-[180px]">Subject</th>
                  <th className="py-3 px-3 font-semibold w-[160px]">
                    Created on
                  </th>
                  <th className="py-3 px-3 font-semibold w-[120px]">Status</th>
                  <th className="py-3 px-3 font-semibold w-[120px]">Action</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td
                      colSpan={5}
                      className="text-center py-8 text-app-text-secondary"
                    >
                      Loading tickets...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={5} className="text-center py-8 text-red-500">
                      Failed to load tickets. Please try again.
                    </td>
                  </tr>
                ) : allFilteredTickets.length === 0 ? (
                  <tr>
                    <td
                      colSpan={5}
                      className="text-center py-8 text-app-text-secondary"
                    >
                      No tickets found.
                    </td>
                  </tr>
                ) : (
                  paginatedTickets?.map((ticket: Ticket, idx: number) => {
                    const statusKey =
                      ticket.status as keyof typeof TICKET_STATUS;
                    return (
                      <tr
                        key={ticket?.id}
                        className="border-b last:border-0 hover:bg-app-sidebar-hover transition-all"
                      >
                        <td className="py-3 px-3 font-mono text-app-text-secondary">
                          {ticket?.id}
                        </td>
                        <td className="py-3 px-3">{ticket?.subject}</td>
                        <td className="py-3 px-3">
                          {new Date(ticket?.created_at).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-3">
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-semibold ${TICKET_STATUS[statusKey]?.color}`}
                          >
                            {TICKET_STATUS[statusKey]?.label}
                          </span>
                        </td>
                        <td className="py-3 px-3">
                          <div className="flex gap-3 items-center">
                            <button
                              className="text-app-text-color hover:text-[#1a2e1a] p-2 rounded-full hover:bg-[#f3f3f3] transition-all"
                              onClick={() => {
                                setSelectedTicket(ticket);
                                setShowTicketDetails(true);
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-[#fbeaea] transition-all"
                              onClick={() => {
                                setDeleteTicketId(ticket.id);
                                setShowDeleteConfirm(true);
                              }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
        
        {/* Pagination */}
        <AppPagination
          currentPage={currentPage}
          totalItems={allFilteredTickets.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          showInfo={true}
        />
      </div>

      {/* Raise Ticket Dialog */}
      <RaiseTicketDialog
        open={showRaiseTicket}
        onClose={() => setShowRaiseTicket(false)}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ["tickets"] });
          setShowRaiseTicket(false);
        }}
      />
      {/* Ticket Details Dialog */}
      <TicketDetailsDialog
        open={showTicketDetails}
        onClose={() => setShowTicketDetails(false)}
        ticket={selectedTicket}
      />
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-xl shadow-xl p-8 max-w-sm w-full">
            <h3 className="text-lg font-bold text-[#1a2e1a] mb-4">
              Delete Ticket
            </h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete this ticket? This action cannot be
              undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeleteTicketId(null);
                }}
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded-lg font-medium bg-red-600 text-white hover:bg-red-700 transition-colors"
                onClick={async () => {
                  if (!deleteTicketId) return;
                  setDeleting(true);
                  await deleteTicketMutation.mutateAsync();
                  setDeleting(false);
                  setShowDeleteConfirm(false);
                  setDeleteTicketId(null);
                  queryClient.invalidateQueries({ queryKey: ["tickets"] });
                }}
                disabled={deleting}
              >
                {deleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Page;
