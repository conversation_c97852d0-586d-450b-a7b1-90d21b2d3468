// types.ts
export interface Form {
  id: number;
  title: string;
}

export interface FormListContainerProps {
  title: string;
  formsList: Form[];
}

export interface FormCanvasProps {
  activeAction: "build" | "settings" | "responses" | "analytics";
  setActiveAction: (
    action: "build" | "settings" | "responses" | "analytics"
  ) => void;
}

export interface Option {
  id: string;
  name: string;
  title: string;
  index?: number;
}

export type Field = {
  id: string;
  formTitle?: string; // Form title
  formDescription?: string; // Form description
  name: string;
  type: string;
  icon: any;
  component: any;
  title?: string;
  description?: string;
  isRequired?: boolean;
  placeholder?: string;
  validationType?:
    | "Greater than"
    | "Greater than or equal to"
    | "Less than"
    | "Less than or equal to"
    | "Equal to"
    | "Not equal to"
    | "Between"
    | "Not between"
    | "Contains"
    | "Doesn't contain"
    | "Maximum character count"
    | "Minimum character count";
  validationValue?: number | string | null; // Single value for most cases
  validationValue2?: number | null;
  allowedAddressFields?: {
    // For AddressInput
    country: boolean;
    state: boolean;
    city: boolean;
    pincode: boolean;
  };
  timeLimit?: "AM" | "PM" | "Both"; // For TimeInput
  durationEnabled?: boolean;
  duration?: string;
  button?: boolean;
  titleMedia?: string; // For CheckboxInput (URL or ID of the title media)
  options?: {
    id: string;
    text: string;
    editable?: boolean;
    isOther?: boolean;
    media?: string;
  }[];
  firstNameTitle?: string;
  lastNameTitle?: string;
  isFirstNameRequired?: boolean;
  isLastNameRequired?: boolean;
  firstNamePlaceholder?: string;
  lastNamePlaceholder?: string;
  allowedFileTypes?: {
    // For UploadInput
    document: boolean;
    image: boolean;
    audio: boolean;
    video: boolean;
  };
  minSize?: number; // For UploadInput (in MB)
  maxSize?: number; // For UploadInput (in MB)
  dateFormat?: string;
  includeCountryCode?: boolean;
  rating?: number;
  maxDuration?: number;
  isHide: boolean;
  isDisable: boolean;
};

export interface Integration {
  id: string;
  name: string;
  description: string;
  status: string;
  actionText: string;
  icon: "email" | "whatsapp" | "task" | "crm" | "sheets" | "pabbly" | "webhook";
  isConnected: boolean;
  url: null | string;
}
