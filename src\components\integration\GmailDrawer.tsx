import React, { useState, useEffect } from "react";
import { X, Loader2 } from "lucide-react";
import {
  useGetIntegrationActions,
  useAddConnection,
  useGetConnections,
  useLinkForm,
} from "@/api-services/gmail-integration";
import { useDisconnectIntegration } from "@/api-services/integration";
import { useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import SelectInputCombo from "@/components/common/SelectInputCombo";
import RichTextEditorWithMapping from "@/components/common/RichTextEditorWithMapping";
import { useGetFormFields } from "@/api-services/form_fields";
import { Option } from "@/types/types";

interface Action {
  id: string;
  name: string;
  description: string;
}

interface Connection {
  id: string;
  name: string;
}

interface ExistingConnection {
  id: string;
  name: string;
  enabled: boolean;
  created_at: string;
  auth_type: string;
  formIntegatedId?: string;
  credentialId?: string;
  actionId?: string;
  mappedData?: Array<{
    id: string;
    key: string;
    name: string;
    title: string;
  }>;
}

interface GmailDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: ExistingConnection[];
  onRefresh?: () => void;
}

export default function GmailDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: GmailDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const [toTemplate, setToTemplate] = useState("");
  const [ccTemplate, setCcTemplate] = useState("");
  const [bccTemplate, setBccTemplate] = useState("");
  const [subjectTemplate, setSubjectTemplate] = useState("");
  const [emailTemplate, setEmailTemplate] = useState("");

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");
  const credentialId = searchParams.get("credential_id");
  const successState = searchParams.get("success");

  const addConnectionMutation = useAddConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { mutate: disconnectIntegration, isPending } =
    useDisconnectIntegration();
  const { data: formFields } = useGetFormFields(formId || "");
  const linkFormMutation = useLinkForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;
  const hasExistingConnection = existingConnections.length > 0;

  // Utility to check if a template references only valid fields
  function isTemplateValid(template: string, fields: any[]): boolean {
    const regex = /\{\{([a-f0-9-]+)(?:\.[^\}]+)?\}\}/gi;
    let match;
    while ((match = regex.exec(template)) !== null) {
      const fieldId = match[1];
      if (!fields.some((f) => f.id === fieldId)) {
        return false;
      }
    }
    return true;
  }

  // Helper to build options for SelectInputCombo and RichTextEditor
  const buildFieldOptions = (fields: any[]) => {
    const options: {
      id: string;
      name: string;
      title: string;
      index: number;
    }[] = [];
    let logicalIndex = 1;
    fields.forEach((f) => {
      if (f.component === "NAME_INPUT") {
        options.push({
          id: f.id,
          name: "firstName",
          title: f.firstNameTitle || "First Name",
          index: logicalIndex,
        });
        options.push({
          id: f.id,
          name: "lastName",
          title: f.lastNameTitle || "Last Name",
          index: logicalIndex,
        });
        logicalIndex++;
      } else if (f.component === "ADDRESS") {
        options.push({
          id: f.id,
          name: "address",
          title: "Address",
          index: logicalIndex,
        });
        const allowed = f.allowedAddressFields || {
          country: true,
          city: true,
          pincode: true,
          state: true,
        };
        if (allowed.city) {
          options.push({
            id: f.id,
            name: "city",
            title: "City",
            index: logicalIndex,
          });
        }
        if (allowed.state) {
          options.push({
            id: f.id,
            name: "state",
            title: "State",
            index: logicalIndex,
          });
        }
        if (allowed.country) {
          options.push({
            id: f.id,
            name: "country",
            title: "Country",
            index: logicalIndex,
          });
        }
        if (allowed.pincode) {
          options.push({
            id: f.id,
            name: "pincode",
            title: "Pincode",
            index: logicalIndex,
          });
        }
        logicalIndex++;
      } else {
        options.push({
          id: f.id,
          name: f.name,
          title: f.title || f.name,
          index: logicalIndex,
        });
        logicalIndex++;
      }
    });
    return options;
  };

  // Helper to build options for email recipient fields (To, CC, BCC)
  const buildEmailFieldOptions = (fields: any[]) => {
    const options: {
      id: string;
      name: string;
      title: string;
      index: number;
    }[] = [];
    fields.forEach((f, index) => {
      if (f.component === "EMAIL") {
        options.push({
          id: f.id,
          name: f.name,
          title: f.title || f.fieldName || f.name,
          index: index + 1,
        });
      }
    });
    return options;
  };

  // Helper to convert backend format to template format
  const convertMappedToTemplate = (key: string, options: Option[]): string => {
    if (!key) return "";
    return key.replace(
      /\{\{([^.]+)\.([^.]+)\}\}/g,
      (match, fieldId, fieldName) => {
        const opt = options.find(
          (opt) => opt.id === fieldId && opt.name === fieldName
        );
        if (!opt) return match;
        return `{{${opt.index}.${fieldName}}}`;
      }
    );
  };

  // Helper to convert template format to backend format
  const convertTemplateToMapped = (
    template: string,
    options: Option[]
  ): string => {
    return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
      const opt = options.find(
        (opt) => String(opt.index) === idx && opt.name === name
      );
      if (!opt) return match;
      return `{{${opt.id}.${opt.name}}}`;
    });
  };

  // Pre-fill form fields when there's an existing connection
  useEffect(() => {
    if (
      hasExistingConnection &&
      existingConnections[0] &&
      formFields?.data?.fields
    ) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      if (connection.credentialId) {
        setSelectedConnection(connection.credentialId);
      }
      if (connection.actionId) {
        setSelectedAction(connection.actionId);
      }
      if (connection.mappedData) {
        const fields = formFields.data.fields;
        const options = buildFieldOptions(fields);
        const emailOptions = buildEmailFieldOptions(fields);
        const toField = connection.mappedData.find((m) => m.id === "to");
        const ccField = connection.mappedData.find((m) => m.id === "cc");
        const bccField = connection.mappedData.find((m) => m.id === "bcc");
        const subjectField = connection.mappedData.find(
          (m) => m.id === "subject"
        );
        const bodyField = connection.mappedData.find((m) => m.id === "body");

        setToTemplate(
          toField?.key && isTemplateValid(toField.key, fields)
            ? convertMappedToTemplate(toField.key, emailOptions)
            : ""
        );
        setCcTemplate(
          ccField?.key && isTemplateValid(ccField.key, fields)
            ? convertMappedToTemplate(ccField.key, emailOptions)
            : ""
        );
        setBccTemplate(
          bccField?.key && isTemplateValid(bccField.key, fields)
            ? convertMappedToTemplate(bccField.key, emailOptions)
            : ""
        );
        setSubjectTemplate(
          subjectField?.key && isTemplateValid(subjectField.key, fields)
            ? convertMappedToTemplate(subjectField.key, options)
            : ""
        );
        setEmailTemplate(
          bodyField?.key && isTemplateValid(bodyField.key, fields)
            ? convertMappedToTemplate(bodyField.key, options) // Convert to display format
            : ""
        );
      }
    } else {
      // Reset when no existing connection
      setToTemplate("");
      setCcTemplate("");
      setBccTemplate("");
      setSubjectTemplate("");
      setEmailTemplate("");
    }
  }, [hasExistingConnection, existingConnections, formFields]);

  // Reset form state when drawer opens for a new connection
  useEffect(() => {
    if (isOpen && !hasExistingConnection) {
      setConnectionType("new");
      setConnectionName("");
      setSelectedConnection("");
      setSelectedAction("");
      setToTemplate("");
      setCcTemplate("");
      setBccTemplate("");
      setSubjectTemplate("");
      setEmailTemplate("");
    }
  }, [isOpen, hasExistingConnection]);

  // Handle connection selection
  useEffect(() => {
    if (
      successState === "true" &&
      credentialId &&
      connections.length > 0 &&
      actions.length > 0
    ) {
      setSelectedConnection(credentialId);
      setConnectionType("existing");
      const savedAction = localStorage.getItem("selectedGmailAction");
      if (savedAction && actions.some((a) => a.id === savedAction)) {
        setSelectedAction(savedAction);
      } else if (actions.length > 0) {
        setSelectedAction(actions[0].id);
      }
    }
  }, [successState, credentialId, connections, actions]);

  // Handle initial action ID
  useEffect(() => {
    if (initialActionId) {
      const selectedActionObj = actions.find((a) => a.id === initialActionId);
      if (selectedActionObj) {
        setSelectedAction(initialActionId);
        localStorage.setItem("selectedGmailAction", initialActionId);
        localStorage.setItem("selectedGmailActionName", selectedActionObj.name);
      }
    }
  }, [initialActionId, actions]);

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
    if (successState !== "true" && connectionId) {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set("credential_id", connectionId);
      window.history.pushState({}, "", newUrl);
    }
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
    if (actionId) {
      const selectedActionObj = actions.find((a) => a.id === actionId);
      if (selectedActionObj) {
        localStorage.setItem("selectedGmailAction", actionId);
        localStorage.setItem("selectedGmailActionName", selectedActionObj.name);
      }
    } else {
      localStorage.removeItem("selectedGmailAction");
      localStorage.removeItem("selectedGmailActionName");
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName || !selectedAction) return;
    setIsAddingConnection(true);
    try {
      const response = await addConnectionMutation.mutateAsync({
        integrationId,
        formId: formId || "",
        formType: formType || "",
        name: connectionName,
        actionId: selectedAction,
      });
      if (response.data?.data?.id) {
        setSelectedConnection(response.data.data.id);
        setConnectionType("existing");
        refetchConnections();
        if (selectedAction) {
          const selectedActionObj = actions.find(
            (a) => a.id === selectedAction
          );
          if (selectedActionObj) {
            localStorage.setItem("selectedGmailAction", selectedAction);
            localStorage.setItem(
              "selectedGmailActionName",
              selectedActionObj.name
            );
          }
        }
      }
    } catch (error) {
      console.error("Error adding connection:", error);
      toast.error("Failed to add connection. Please try again.");
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;
    setIsDisconnecting(true);
    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: async () => {
          await onRefresh?.();
          toast.success("Gmail disconnected successfully!");
          setSelectedAction("");
          setSelectedConnection("");
          setConnectionType("new");
          setConnectionName("");
          setToTemplate("");
          setCcTemplate("");
          setBccTemplate("");
          setSubjectTemplate("");
          setEmailTemplate("");
          localStorage.removeItem("selectedGmailAction");
          localStorage.removeItem("selectedGmailActionName");
        },
        onError: (error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect Gmail. Please try again.");
        },
        onSettled: () => {
          setIsDisconnecting(false);
        },
      }
    );
  };

  const handleLinkForm = async () => {
    if (
      !selectedConnection ||
      !selectedAction ||
      !toTemplate ||
      !subjectTemplate
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsLinking(true);
    try {
      const fields = formFields?.data?.fields || [];
      const options = buildFieldOptions(fields);
      const emailOptions = buildEmailFieldOptions(fields);

      const mappedData = [
        {
          id: "to",
          name: "to",
          title: "To",
          key: convertTemplateToMapped(toTemplate, emailOptions),
        },
        {
          id: "cc",
          name: "cc",
          title: "CC",
          key: convertTemplateToMapped(ccTemplate, emailOptions),
        },
        {
          id: "bcc",
          name: "bcc",
          title: "BCC",
          key: convertTemplateToMapped(bccTemplate, emailOptions),
        },
        {
          id: "subject",
          name: "subject",
          title: "Subject",
          key: convertTemplateToMapped(subjectTemplate, options),
        },
        {
          id: "body",
          name: "body",
          title: "Body",
          key: emailTemplate, // Already in backend format
        },
      ];

      await linkFormMutation.mutateAsync({
        formId: formId || "",
        integrationId,
        credentialId: selectedConnection,
        actionId: selectedAction,
        columnMappedData: mappedData,
        emailTemplate: emailTemplate,
        subjectTemplate: "",
        senderName: "",
      });
      toast.success("Form linked successfully!");
      onClose();
      onRefresh?.();
    } catch (error) {
      console.error("Error linking form:", error);
      toast.error("Failed to link form. Please try again.");
    } finally {
      setIsLinking(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/50 z-[199] transition-opacity" />
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    {hasExistingConnection
                      ? "Edit Gmail Integration"
                      : "Connect Gmail Account"}
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>
            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">Gmail</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action) => (
                      <option key={action.id} value={action.id}>
                        {action.name}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>
                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>
                    <div className="space-y-4">
                      {connectionType === "new" ? (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              Connection Name
                            </label>
                            <input
                              type="text"
                              value={connectionName}
                              onChange={(e) =>
                                setConnectionName(e.target.value)
                              }
                              className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              placeholder="Enter connection name"
                            />
                          </div>
                          <button
                            onClick={handleAddConnection}
                            disabled={!connectionName || isAddingConnection}
                            className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                              !connectionName || isAddingConnection
                                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                                : "bg-app-text-color text-app-background hover:bg-opacity-90"
                            }`}
                          >
                            {isAddingConnection ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin" />
                                Adding...
                              </>
                            ) : (
                              "Add New Connection"
                            )}
                          </button>
                        </div>
                      ) : (
                        <>
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              Select Connection
                            </label>
                            <select
                              value={selectedConnection}
                              onChange={handleConnectionSelect}
                              disabled={!hasValidConnections}
                              className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            >
                              <option value="">Select a connection</option>
                              {connections.map((connection) => (
                                <option
                                  key={connection.id}
                                  value={connection.id}
                                >
                                  {connection.name}
                                </option>
                              ))}
                            </select>
                          </div>
                          {selectedConnection && (
                            <>
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  To{" "}
                                  <span className="text-red-500 ml-1">*</span>
                                </label>
                                <SelectInputCombo
                                  options={buildEmailFieldOptions(
                                    formFields?.data?.fields || []
                                  )}
                                  value={toTemplate}
                                  onChange={setToTemplate}
                                  placeholder="Type or insert email fields for recipient"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  CC
                                </label>
                                <SelectInputCombo
                                  options={buildEmailFieldOptions(
                                    formFields?.data?.fields || []
                                  )}
                                  value={ccTemplate}
                                  onChange={setCcTemplate}
                                  placeholder="Type or insert email fields for CC recipients"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  BCC
                                </label>
                                <SelectInputCombo
                                  options={buildEmailFieldOptions(
                                    formFields?.data?.fields || []
                                  )}
                                  value={bccTemplate}
                                  onChange={setBccTemplate}
                                  placeholder="Type or insert email fields for BCC recipients"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  Subject{" "}
                                  <span className="text-red-500 ml-1">*</span>
                                </label>
                                <SelectInputCombo
                                  options={buildFieldOptions(
                                    formFields?.data?.fields || []
                                  )}
                                  value={subjectTemplate}
                                  onChange={setSubjectTemplate}
                                  placeholder="Type or insert fields for email subject"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  Email Template
                                </label>
                                <RichTextEditorWithMapping
                                  value={emailTemplate}
                                  onChange={setEmailTemplate}
                                  options={buildFieldOptions(
                                    formFields?.data?.fields || []
                                  )}
                                  placeholder="Type your email content here..."
                                />
                              </div>
                            </>
                          )}
                        </>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {hasExistingConnection && (
                  <button
                    onClick={handleDisconnect}
                    disabled={isDisconnecting}
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      isDisconnecting
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-red-500 text-white hover:bg-red-600"
                    }`}
                  >
                    {isDisconnecting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Disconnecting...
                      </>
                    ) : (
                      "Disconnect"
                    )}
                  </button>
                )}
                <button
                  onClick={hasExistingConnection ? handleLinkForm : onClose}
                  disabled={
                    hasExistingConnection
                      ? !selectedAction ||
                        !selectedConnection ||
                        isAddingConnection ||
                        isLinking ||
                        !toTemplate ||
                        !subjectTemplate
                      : false
                  }
                  className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                    hasExistingConnection
                      ? !selectedAction ||
                        !selectedConnection ||
                        isAddingConnection ||
                        isLinking ||
                        !toTemplate ||
                        !subjectTemplate
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      : "border border-gray-300 hover:bg-gray-100 transition-colors"
                  }`}
                >
                  {hasExistingConnection ? (
                    isLinking ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      "Update"
                    )
                  ) : (
                    "Cancel"
                  )}
                </button>
                {!hasExistingConnection && (
                  <button
                    onClick={handleLinkForm}
                    disabled={
                      !selectedAction ||
                      (connectionType === "new" && !connectionName) ||
                      (connectionType === "existing" && !selectedConnection) ||
                      isAddingConnection ||
                      isLinking ||
                      !toTemplate ||
                      !subjectTemplate
                    }
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      !selectedAction ||
                      (connectionType === "new" && !connectionName) ||
                      (connectionType === "existing" && !selectedConnection) ||
                      isAddingConnection ||
                      isLinking ||
                      !toTemplate ||
                      !subjectTemplate
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isLinking ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Linking...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
