import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1`;
async function getSubscription() {
  return makeRequest({
    endpoint: `${baseEndpoint}/subscription`,
    method: "GET",
  });
}

const useGetSubscription = () => {
  return useQuery({
    queryKey: [QueryKeys.SUBSCRIPTION],
    queryFn: () => getSubscription(),
  });
};

export { useGetSubscription };
