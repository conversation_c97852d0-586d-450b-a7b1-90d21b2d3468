import React, { Suspense, useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const TimeInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  isRequired,
  title,
  description,
  component,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField, fields } = useAppStore();
  const [time, setTime] = useState(value?.split("_")?.[0] || "");
  const [ampm, setAmpm] = useState(value?.split("_")?.[1] || "am");

  const currentField = fields.find((field) => field.id === id);
  const timeLimit = currentField?.timeLimit || "Both";
  const durationEnabled = currentField?.durationEnabled || false;

  useGetConditionById(id, time + "_" + ampm);

  useEffect(() => {
    if (value) {
      const timeValue = value.slice(0, -2);
      const ampmValue = value.slice(-2);
      setTime(timeValue);
      setAmpm(ampmValue);
    }
  }, [value]);

  // Update AM/PM when time limit changes
  useEffect(() => {
    if (timeLimit === "AM") {
      setAmpm("am");
    } else if (timeLimit === "PM") {
      setAmpm("pm");
    }
  }, [timeLimit]);

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = e.target.value;
    setTime(newTime);

    if (timeLimit === "Both") {
      const [hours] = newTime.split(":");
      const newAmpm = parseInt(hours) >= 12 ? "pm" : "am";
      setAmpm(newAmpm);
    }

    onChange?.(`${newTime}_${ampm}`);
  };

  const handleAmpmChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newAmpm = e.target.value;
    setAmpm(newAmpm);
    onChange?.(`${time}_${newAmpm}`);
  };

  const getTimeInputProps = () => {
    if (timeLimit === "AM") {
      return {
        min: "00:00",
        max: "11:59",
      };
    } else if (timeLimit === "PM") {
      return {
        min: "12:00",
        max: "23:59",
      };
    }
    return {
      min: "00:00",
      max: "23:59",
    };
  };

  const getAmpmOptions = () => {
    if (timeLimit === "AM") {
      return [
        <option key="am" value="am">
          AM
        </option>,
      ];
    } else if (timeLimit === "PM") {
      return [
        <option key="pm" value="pm">
          PM
        </option>,
      ];
    }
    return [
      <option key="am" value="am">
        AM
      </option>,
      <option key="pm" value="pm">
        PM
      </option>,
    ];
  };

  // Function to check if a time is within the allowed range
  const isTimeInRange = (timeStr: string) => {
    if (timeLimit === "Both") return true;

    const [hours] = timeStr.split(":");
    const hour = parseInt(hours);

    if (timeLimit === "AM") return hour < 12;
    if (timeLimit === "PM") return hour >= 12;

    return true;
  };

  if (isHide && isPreview) return null;

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <input
          type="text"
          className="hidden"
          value={`${time} ${ampm}`}
          name={`${id}_time`}
          disabled={isDisable}
        />
        <div className="relative w-full mt-2 flex items-center gap-2">
          <Input
            type="time"
            className="font-medium bg-app-hero-background text-right w-fit"
            value={time}
            required={isRequired}
            onChange={handleTimeChange}
            disabled={isDisable}
            {...getTimeInputProps()}
          />
          {!durationEnabled && (
            <select
              value={ampm}
              onChange={handleAmpmChange}
              className="border rounded px-2 py-2 pr-8 bg-app-hero-background"
              disabled={isDisable}
            >
              {getAmpmOptions()}
            </select>
          )}
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default TimeInput;
