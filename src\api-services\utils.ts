import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";

const headers = {
  "Content-Type": "application/json",
};

// List of endpoints that should not trigger login redirect
const NO_REDIRECT_ENDPOINTS = [
  "/v1/form/webhook",
  "/v1/users/login",
  "/v1/wallet/plans",
  "/v1/users/profile",
  "/v1/wallet/balance",
  "/v1/forms/public",
  "/v1/formsetting/thankyoupage",
];

async function makeRequest({
  endpoint,
  method,
  data,
  isFileUpload,
  headers: customHeaders,
}: {
  endpoint: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  data?: Record<string, any> | FormData;
  isFileUpload?: boolean;
  headers?: Record<string, string>;
}) {
  const baseUrl =
    process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001/api";
  const url = `${baseUrl}${endpoint}`;

  console.log("Making request to:", url); // Debug log

  // Get the current session and access token
  const {
    data: { session },
  } = await supabase.auth.getSession();
  const accessToken = session?.access_token;

  const options: RequestInit = {
    method,
    headers: isFileUpload
      ? {
          ...customHeaders,
          ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        }
      : {
          ...headers,
          "Content-Type": "application/json",
          ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
          ...customHeaders,
        },
    credentials: "include", // This ensures cookies are sent with the request
  };

  if (data) {
    options.body = isFileUpload ? (data as FormData) : JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    let responseData;

    // Try to parse JSON response
    try {
      responseData = await response.json();
    } catch (e) {
      // If response is not JSON, create a generic error
      responseData = {
        message: "Server returned an invalid response",
        status: response.status,
      };
    }

    // Check if this endpoint should skip redirect
    const shouldSkipRedirect = NO_REDIRECT_ENDPOINTS.some((path) =>
      endpoint.startsWith(path)
    );

    // Handle authentication errors (token expired)
    if (
      (response.status === 401 || response.status === 403) &&
      !shouldSkipRedirect
    ) {
      throw new Error("Authentication failed");
    }

    if (!response.ok) {
      return {
        success: false,
        error:
          responseData.error ||
          responseData.message ||
          "Operation failed. Please try again.",
        status: response.status,
        data: null,
      };
    }

    return responseData;
  } catch (error) {
    // If it's an authentication error and not a no-redirect endpoint, let it propagate
    if (
      error instanceof Error &&
      !NO_REDIRECT_ENDPOINTS.some((path) => endpoint.startsWith(path)) &&
      (error.message.includes("Authentication failed") ||
        error.message.includes("Session expired"))
    ) {
      throw error;
    }

    // For other errors, just throw them without redirecting
    throw error;
  }
}

const QueryKeys = {
  FORM_FIELDS: (id: string) => ["form-fields", id],
  FORM_ELEMENTS: ["form-elements"],
  // USER_FORMS: ["user-forms"],
  USER_PROFILE: ["userProfile"],
  USER_FORMS: (limit: number, offset: number) => ["user-forms", limit, offset],
  SEARCH_FORMS: (query: string, limit: number, offset: number) => [
    "search-forms",
    query,
    limit,
    offset,
  ],
  UPDATE_FORM: (id: string) => ["update-form", id],
  INTEGRATIONS: ["integrations"],
  FORM_DETAILS: (id: string) => ["form-details", id],
  FORM_PUBLIC: (id: string) => ["form-public", id],
  FORM_CACHE_UPDATE: (id: string) => ["form-cache-update", id],
  GOOGLE_SHEET_AUTH: (id: string, type: string) => [
    "google-sheet-auth",
    id,
    type,
  ],
  USER_SHEETS: ["user-sheets"],
  CREATE_SHEET: ["create-sheet"],
  THEMES: ["form-themes"],
  GOOGLE_SHEET_ACTIONS: (id: string) => ["google-sheet-actions", id],
  GOOGLE_SHEET_CONNECTIONS: (id: string) => ["google-sheet-connections", id],
  WORKSPACE_MEMBERS: ["workspace-members"] as const,
  ROLE_BASED_ON_WORKSPACE: (workspace_id: string) => [
    "role-based-on-workspace",
    workspace_id,
  ],
  WEBHOOKS: (formId: string) => ["webhooks", formId],
  TIMEZONES: ["timezones"],
  SUBSCRIPTION: ["subscription"],
  REFERRAL: ["referral"],
  SLACK_ACTIONS: (id: string) => ["slack-actions", id],
  SLACK_CONNECTIONS: (id: string) => ["slack-connections", id],
};

const checkAuth = async () => {
  try {
    const session = await supabase.auth.getSession();
    if (session.data.session) {
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error checking authentication:", error);
    return false;
  }
};

// Function to check if token is expired or about to expire
// This can be used proactively before making request

export { makeRequest, QueryKeys, checkAuth };
