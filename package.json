{"name": "form-builder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fontsource/roboto": "^5.1.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.10.0", "@lexical/headless": "^0.23.1", "@lexical/link": "^0.23.1", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.0", "@tanstack/react-query": "^5.66.9", "@types/js-cookie": "^3.0.6", "ag-grid-community": "^33.1.1", "ag-grid-react": "^33.1.1", "axios": "^1.7.9", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crisp-sdk-web": "^1.0.25", "crypto": "^1.0.1", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "input-otp": "^1.4.2", "isomorphic.js": "^0.2.5", "lib0": "^0.2.99", "lucide-react": "^0.473.0", "next": "15.1.5", "next-qrcode": "^2.5.1", "posthog-js": "^1.245.0", "posthog-node": "^4.17.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.11", "react-quill-new": "^3.4.6", "react-razorpay": "^3.0.1", "react-resizable-panels": "^2.1.7", "react-signature-canvas": "^1.0.7", "recharts": "^2.15.3", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "uuid": "^11.0.5", "vaul": "^1.1.2", "xlsx": "^0.18.5", "yjs": "^13.6.23", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dnd": "^2.0.36", "@types/react-dom": "^18.3.5", "postcss": "^8", "prisma": "^6.9.0", "tailwindcss": "^3.4.1", "typescript": "^5"}, "engines": {"node": ">=18.18.0"}}