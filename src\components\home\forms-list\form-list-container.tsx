"use client";
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import FormsList from "./forms-list";
import BorderList from "./border-list";
import { useFormListContainer } from "@/hooks/useFormListContainer";
import {
  LayoutGrid,
  LayoutList,
  SearchIcon,
  Loader2,
  Users,
  X,
} from "lucide-react";
import { useGetUserForms, useSearchForms } from "@/api-services/form";
import { useUserProfile } from "@/api-services/auth";
import { MoveToFolder } from "./move-to";
import Loader from "@/components/common/loader";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import AppPagination from "@/components/common/app-pagination";

const LIMIT = 12;

const FormListContainer = ({ title }: any) => {
  const [params, setParams] = React.useState({
    limit: 12,
    offset: 0,
  });
  const { selectedTab, setSelectedTab, searchQuery, setSearchQuery } =
    useFormListContainer();

  // Add state for shared forms filter
  const [showSharedOnly, setShowSharedOnly] = React.useState(false);

  // Debounce search query to avoid too many API calls
  const [debouncedSearchQuery, setDebouncedSearchQuery] = React.useState(searchQuery);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      // Reset offset when search query changes
      if (searchQuery !== debouncedSearchQuery) {
        setParams(prev => ({ ...prev, offset: 0 }));
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, debouncedSearchQuery]);

  // Reset offset when shared filter changes
  React.useEffect(() => {
    setParams(prev => ({ ...prev, offset: 0 }));
  }, [showSharedOnly]);

  // Get user profile to access user_id
  const { data: userProfile } = useUserProfile();
  const userId = userProfile?.data?.user?.user_id;

  // Use search API when there's a search query, otherwise use regular getUserForms
  const shouldSearch = debouncedSearchQuery.trim().length > 0;

  // When showing shared forms, we need to fetch more data to filter properly
  const apiParams = React.useMemo(() => {
    if (showSharedOnly) {
      // Fetch a larger set to ensure we have enough shared forms
      return { limit: 100, offset: 0 };
    }
    return params;
  }, [params, showSharedOnly]);

  const {
    data: formsListResponse,
    isLoading: isLoadingForms,
    isFetching: isFetchingForms,
  } = useGetUserForms(apiParams, { enabled: !shouldSearch });

  const {
    data: searchResponse,
    isLoading: isLoadingSearch,
    isFetching: isFetchingSearch,
  } = useSearchForms(debouncedSearchQuery, apiParams, { enabled: shouldSearch });

  const isLoading = shouldSearch ? isLoadingSearch : isLoadingForms;
  const isFetching = shouldSearch ? isFetchingSearch : isFetchingForms;
  
  // Get data from the appropriate response
  const rawFormsList = shouldSearch 
    ? (searchResponse as any)?.data?.forms || [] 
    : (formsListResponse as any)?.data?.forms || [];

  // Apply client-side filtering for shared forms
  const allFilteredForms = React.useMemo(() => {
    if (!showSharedOnly || !userId) {
      return rawFormsList;
    }
    
    // Filter to show only forms created by others (shared forms)
    return rawFormsList.filter((item: any) => item.created_by_id !== userId);
  }, [rawFormsList, showSharedOnly, userId]);

  // Apply pagination to filtered results when showing shared forms
  const formsList = React.useMemo(() => {
    if (!showSharedOnly) {
      return allFilteredForms;
    }
    // For shared forms, apply client-side pagination
    const startIndex = params.offset;
    const endIndex = startIndex + LIMIT;
    return allFilteredForms.slice(startIndex, endIndex);
  }, [allFilteredForms, showSharedOnly, params.offset]);

  // Calculate total count based on filtered results
  const MAX_OFFSET = showSharedOnly 
    ? allFilteredForms.length 
    : (shouldSearch 
        ? (searchResponse as any)?.data?.total_count || 0 
        : (formsListResponse as any)?.data?.total_count || 0);

  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * LIMIT;
    setParams((prevParams) => ({
      ...prevParams,
      offset: newOffset,
    }));
  };

  const currentPage = Math.floor(params.offset / LIMIT) + 1;

  const handleSharedToggle = () => {
    setShowSharedOnly(!showSharedOnly);
  };

  // Handle clearing search query
  const handleClearSearch = () => {
    setSearchQuery("");
  };

  // Remove the full-screen loader for initial loading
  // We'll handle loading states within each tab content

  return (
    <div className="flex flex-col w-full overflow-auto space-y-5 py-2">
      <div className="flex flex-row items-center justify-between max-[680px]:items-start gap-2">
        <div className=" flex flex-row items-center gap-4 max-[680px]:flex-col max-[680px]:items-start max-[680px]:gap-0.5">
          <h3 className="text-2xl font-semibold text-app-text-color">
            {title}
          </h3>
          <div className="relative">
            <SearchIcon className="absolute p-0.5 bottom-2 left-2 text-app-text-secondary" />
            <Input
              placeholder="Search Forms..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10 max-w-[300px] w-full font-medium bg-app-background placeholder:text-app-text-secondary text-app-text-color border-app-border-primary"
            />
            {searchQuery && (
              <button
                onClick={handleClearSearch}
                className="absolute right-2 bottom-2 p-1 hover:bg-app-hero-background rounded-full transition-colors"
                type="button"
              >
                <X className="h-4 w-4 text-app-text-secondary hover:text-app-text-color" />
              </button>
            )}
          </div>
          <Button
            variant={showSharedOnly ? "default" : "outline"}
            size="sm"
            onClick={handleSharedToggle}
            className={`flex items-center gap-2 transition-all duration-200 ${
              showSharedOnly
                ? "bg-app-text-color text-app-background hover:bg-app-text-secondary border-app-text-color shadow-md"
                : "bg-transparent hover:bg-app-hero-background text-app-text-color border-app-border-primary hover:border-app-text-secondary"
            }`}
          >
            <Users className="h-4 w-4" />
            <span>{showSharedOnly ? "Show All Forms" : "Shared with me"}</span>
          </Button>
          <MoveToFolder />
        </div>
        <div>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="flex rounded-md dark:bg-app-hero-background bg-app-hero-background">
              <TabsTrigger
                value="list"
                className={`p-2 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "list"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutList />
                <span>List</span>
              </TabsTrigger>
              <TabsTrigger
                value="grid"
                className={`p-1.5 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "grid"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutGrid />
                <span>Grid</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>{" "}
      </div>
      <div className="">
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsContent value="list">
            {isLoading || isFetching ? (
              <div className="flex items-center justify-center w-full h-[200px]">
                <Loader2 className="w-8 h-8 animate-spin text-app-text-color" />
              </div>
            ) : (
              <FormsList
                params={params}
                formsList={formsList}
              />
            )}
          </TabsContent>
          <TabsContent value="grid">
            {isLoading || isFetching ? (
              <div className="flex items-center justify-center w-full h-[200px]">
                <Loader2 className="w-8 h-8 animate-spin text-app-text-color" />
              </div>
            ) : (
              <BorderList
                params={params}
                formsList={formsList}
              />
            )}
          </TabsContent>
        </Tabs>
        {showSharedOnly && (
          <div className="flex justify-center mt-4">
            <p className="text-app-text-color font-medium text-sm">
              (Showing shared forms only)
            </p>
          </div>
        )}
        
        <AppPagination
          currentPage={currentPage}
          totalItems={MAX_OFFSET}
          itemsPerPage={LIMIT}
          onPageChange={handlePageChange}
          showInfo={true}
        />
      </div>
    </div>
  );
};

export default FormListContainer;
