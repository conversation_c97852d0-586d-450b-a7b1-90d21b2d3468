"use client";

import { useFormsPublic } from "@/api-services/form";
import { useSubmission } from "@/api-services/form_submission";
import { Button } from "@/components/ui/button";
import { toolContainersElement } from "@/fields/fieldsData";
import {
  formPayloadParser,
  formPayloadParserForMultiPage,
} from "@/utils/formPayloadParser";
import { AlertCircle, Loader } from "lucide-react";
import React, { FormEvent, useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import MultiPageFormView from "@/components/form/MultiPageFormView";
import { validateFormFields } from "@/utils/formValidation";
import { RenderRedirectPage } from "@/components/playground/thank-you-response-select";
import { ContentRenderer } from "@/components/playground/thank-you-response-select";
import Image from "next/image";
import { useAppStore } from "@/state-store/app-state-store";
import { fieldValues } from "@/state-store/globalForCondition";
import Link from "next/link";
import "react-quill-new/dist/quill.snow.css";
import { usePathname } from "next/navigation";

interface FormWrapperProps {
  formId: string;
}

// Utility to decode HTML entities if needed
function decodeHtml(html: string) {
  const txt =
    typeof window !== "undefined" ? document.createElement("textarea") : null;
  if (!txt) return html;
  txt.innerHTML = html;
  return txt.value;
}

const FormWrapper = ({ formId }: FormWrapperProps) => {
  const pathname = usePathname();
  const isCopyPage = pathname.includes("/copy");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isFormDeleted, setIsFormDeleted] = useState(false);
  const { mutate: submission, isPending } = useSubmission();

  const { data: responseData, isLoading, error, refetch } = useFormsPublic(formId);

  const filteredForm = responseData?.data?.form;
  const isMultiPage = filteredForm?.type === "multipage";
  const referrelCode = responseData?.data?.referral_code;

  const {
    setConditions,
    setFields,
    fields,
    thankYouLogicResult,
    setThankYouConditions,
  } = useAppStore();

  const thankyouType = responseData?.data?.thank_you_type;
  const thankyouData = responseData?.data?.thank_you_data;
  const thankyouUrl = responseData?.data?.thank_you_url;

  const trackOfLocalState = useRef(false);

  useEffect(() => {
    if (responseData?.data && !trackOfLocalState.current) {
      const fields = responseData?.data?.fields;
      const condition = responseData?.data?.condition;
      setFields(fields);
      setConditions(condition);
      trackOfLocalState.current = true;
    }
  }, [responseData?.data]);

  useEffect(() => {
    if (responseData?.data?.thank_you_data && thankyouType === "condition") {
      const thankYouData = responseData?.data?.thank_you_data;
      setThankYouConditions(thankYouData);
    }
  }, [responseData?.data?.thank_you_data]);

  const handleSubmit = async (e: FormEvent, fieldIds: (string[] | string)[]) => {
    e.preventDefault();
    
    // First check if the form still exists by refetching
    try {
      const freshData = await refetch();
      if (!freshData.data?.data?.form) {
        setIsFormDeleted(true);
        toast.error("This form no longer exists or has been deleted. Please contact the creator of the form.");
        return;
      }
    } catch (error) {
      setIsFormDeleted(true);
      toast.error("This form no longer exists or has been deleted. Please contact the creator of the form.");
      return;
    }
    
    const formData = new FormData(e.target as HTMLFormElement);
    const formPayloadvalues: Record<string, string> = {};

    formData.forEach((value, key) => {
      formPayloadvalues[key] = value as string;
    });

    const formValues = isMultiPage
      ? formPayloadParserForMultiPage(fieldValues, fieldIds)
      : formPayloadvalues;

    const validationResult = validateFormFields(fields || [], formValues);

    if (!validationResult.isValid) {
      validationResult.errors.forEach((error) => {
        toast.error(error.message);
      });
      return;
    }

    const payload = {
      form_id: formId,
      answers: formPayloadParser(formValues, responseData?.data?.fields || []),
    };

    submission(payload, {
      onSuccess: () => {
        setIsSubmitted(true);
        toast.success("Form submitted successfully!");
      },
      onError: (error: any) => {
        // Check if the error is 404 (form not found)
        const isFormNotFound = (error as any)?.response?.status === 404 || 
                              error?.message?.includes("Form not found") ||
                              error?.message?.includes("not found");
        
        if (isFormNotFound) {
          setIsFormDeleted(true);
          toast.error("This form no longer exists or has been deleted. Please contact the creator of the form.");
          // Don't set isSubmitted to true, so user stays on the form page
        } else {
          toast.error("Something went wrong!");
        }
      },
    });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader className="animate-spin" />
      </div>
    );
  }

  if (error) {
    // Check if the error is 404 (form not found)
    const isFormNotFound = (error as any)?.response?.status === 404 || 
                          error?.message?.includes("Form not found") ||
                          error?.message?.includes("not found");
    
    if (isFormNotFound) {
      return (
        <div className="flex flex-col min-h-screen relative">
          <div className="flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full min-h-screen bg-app-hero-background mt-[1px] text-app-text-color">
            <div className="max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style bg-app-background shadow-lg">
              <div className="flex flex-col items-center text-center w-full gap-6 p-8">
                <div className="bg-app-sidebar-hover-active p-4 rounded-full">
                  <AlertCircle className="w-12 h-12 text-app" />
                </div>
                <div className="space-y-3">
                  <h2 className="text-3xl font-bold text-app-text-secondary">
                    Form Not Found
                  </h2>
                  <p className="text-lg text-app-text-color max-w-md">
                    This form no longer exists or has been deleted. Please contact the creator of the form.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
            <span>Powered by</span>
            <Link
              href={"/signup?ref="}
              target="_blank"
              className="cursor-pointer"
            >
              <Image
                src="/logo.png"
                alt="Automate Business Logo"
                height={100}
                width={100}
                quality={100}
                className="h-8 w-auto ml-2"
              />
            </Link>
          </div>
        </div>
      );
    }
    
    return (
      <div className="flex flex-col min-h-screen relative">
        <div className="flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full min-h-screen bg-app-hero-background mt-[1px] text-app-text-color">
          <div className="max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style bg-app-background shadow-lg">
            <div className="flex flex-col items-center text-center w-full gap-6 p-8">
              <div className="bg-app-sidebar-hover-active p-4 rounded-full">
                <AlertCircle className="w-12 h-12 text-app" />
              </div>
              <div className="space-y-3">
                <h2 className="text-3xl font-bold text-app-text-secondary">
                  Something Went Wrong
                </h2>
                <p className="text-lg text-app-text-color max-w-md">
                  Unable to load the form. Please try again later.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
          <span>Powered by</span>
          <Link
            href={"/signup?ref="}
            target="_blank"
            className="cursor-pointer"
          >
            <Image
              src="/logo.png"
              alt="Automate Business Logo"
              height={100}
              width={100}
              quality={100}
              className="h-8 w-auto ml-2"
            />
          </Link>
        </div>
      </div>
    );
  }

  // Check if form has been deleted
  if (isFormDeleted) {
    return (
      <div className="flex flex-col min-h-screen relative">
        <div className="flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full min-h-screen bg-app-hero-background mt-[1px] text-app-text-color">
          <div className="max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style bg-app-background shadow-lg">
            <div className="flex flex-col items-center text-center w-full gap-6 p-8">
              <div className="bg-app-sidebar-hover-active p-4 rounded-full">
                <AlertCircle className="w-12 h-12 text-app" />
              </div>
              <div className="space-y-3">
                <h2 className="text-3xl font-bold text-app-text-secondary">
                  Form Not Found
                </h2>
                <p className="text-lg text-app-text-color max-w-md">
                  This form no longer exists or has been deleted. Please contact the creator of the form.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
          <span>Powered by</span>
          <Link
            href={"/signup?ref="}
            target="_blank"
            className="cursor-pointer"
          >
            <Image
              src="/logo.png"
              alt="Automate Business Logo"
              height={100}
              width={100}
              quality={100}
              className="h-8 w-auto ml-2"
            />
          </Link>
        </div>
      </div>
    );
  }

  // Check if form is not accepting responses
  if (filteredForm?.automate_form_settings?.[0]?.accept_responses === false) {
    return (
      <div className="flex flex-col min-h-screen relative">
        <div className="flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full min-h-screen bg-app-hero-background mt-[1px] text-app-text-color">
          <div className="max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style bg-app-background shadow-lg">
            <div className="flex flex-col items-center text-center w-full gap-6 p-8">
              <div className="bg-app-sidebar-hover-active p-4 rounded-full">
                <AlertCircle className="w-12 h-12 text-app" />
              </div>
              <div className="space-y-3">
                <h2 className="text-3xl font-bold text-app-text-secondary">
                  Form Unavailable
                </h2>
                <p className="text-lg text-app-text-color max-w-md">
                  This form is no longer available. Please contact the creator
                  of the form.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
          <span>Powered by</span>
          <Link
            href={"/signup?ref="}
            target="_blank"
            className="cursor-pointer"
          >
            <Image
              src="/logo.png"
              alt="Automate Business Logo"
              height={100}
              width={100}
              quality={100}
              className="h-8 w-auto ml-2"
            />
          </Link>
        </div>
      </div>
    );
  }

  const thankuComponents = {
    custom: (value: string) => (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-50/80">
        <div className="min-w-[600px] max-w-[800px] overflow-hidden">
          <ContentRenderer value={value} />
          <div className="mt-6 flex justify-center">
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="w-40 h-12 text-lg"
            >
              Submit Again
            </Button>
          </div>
        </div>
      </div>
    ),
    redirect: (url: string) => <RenderRedirectPage url={url} />,
  };

  const isConditionThankyou = thankyouType === "condition";

  const { action, content } = thankYouLogicResult;
  if (isConditionThankyou && action && content && isSubmitted) {
    const ThankYouComponent = thankuComponents[
      action === "Show custom message" ? "custom" : "redirect"
    ] as (value: string) => React.ReactNode;
    return ThankYouComponent(content);
  }

  const defaultThankYou = (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-50/80">
      <div className="min-w-[600px] max-w-[800px] overflow-hidden">
        <ContentRenderer value="<h1>Thank you for your response</h1><p>Your form has been submitted successfully.</p>" />
        <div className="mt-6 flex justify-center">
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-40 h-12 text-lg bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color rounded-xl"
          >
            Submit Again
          </Button>
        </div>
      </div>
    </div>
  );

  if (isSubmitted) {
    if (
      !thankyouType ||
      !thankuComponents[thankyouType as keyof typeof thankuComponents]
    ) {
      return defaultThankYou;
    }

    const ThankYouComponent =
      thankuComponents[thankyouType as keyof typeof thankuComponents];
    const value = thankyouType === "custom" ? thankyouData : thankyouUrl;

    return ThankYouComponent(value);
  }

  return (
    <div className="flex flex-col min-h-screen relative">
      <div
        className={`flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full  min-h-screen bg-app-hero-background mt-[1px] text-app-text-color ${
          !filteredForm?.bg_image && filteredForm?.bg_color
            ? ""
            : "bg-app-hero-background"
        } `}
        style={{
          fontFamily: `var(--font-${filteredForm?.font_family
            ?.toLowerCase()
            .replace(/\s+/g, "-")})`,
          backgroundColor: !filteredForm?.bg_image
            ? `${filteredForm?.bg_color}33`
            : "",
        }}
      >
        <div
          className={`max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style ${
            isMultiPage ? "pb-2" : "pb-20"
          } ${!filteredForm?.bg_image ? "bg-app-background" : ""}`}
          style={{
            backgroundImage: filteredForm?.bg_image
              ? `url(${filteredForm.bg_image})`
              : "none",
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundColor: !filteredForm?.bg_image
              ? filteredForm?.bg_color
              : "transparent",
            // fontFamily: `var(--font-${filteredForm?.font_family?.toLowerCase().replace(/\s+/g, '-')})`,
          }}
        >
          {isMultiPage ? (
            <MultiPageFormView
              fields={fields}
              headerImage={filteredForm?.header_img || null}
              formHeading={filteredForm?.heading || ""}
              formDescription={filteredForm?.description || ""}
              bgColor={filteredForm?.bg_color}
              headingColor={filteredForm?.heading_color}
              descriptionColor={filteredForm?.description_color}
              fontFamily={`var(--font-${filteredForm?.font_family
                ?.toLowerCase()
                .replace(/\s+/g, "-")})`}
              onSubmit={handleSubmit}
              isSubmitting={isPending}
              workspace_id={filteredForm?.workspace_id}
              isCopyPage={isCopyPage}
            />
          ) : (
            <>
              <div
                className="flex flex-col items-center text-center w-full gap-1"
                style={{
                  fontFamily: `var(--font-${filteredForm?.font_family
                    ?.toLowerCase()
                    .replace(/\s+/g, "-")})`,
                }}
              >
                {filteredForm?.header_img && (
                  <div className="w-full max-h-52 mb-6 overflow-hidden rounded-lg border">
                    <Image
                      src={filteredForm?.header_img}
                      alt="Header"
                      className="w-full h-auto"
                      height={100}
                      width={100}
                      quality={100}
                      layout="responsive"
                      objectFit="cover"
                    />
                  </div>
                )}
                <h2
                  className="text-3xl font-bold w-full px-3"
                  style={{
                    color:
                      filteredForm?.heading_color ||
                      "var(--app-text-secondary)",
                    fontFamily: `var(--font-${filteredForm?.font_family
                      ?.toLowerCase()
                      .replace(/\s+/g, "-")})`,
                  }}
                >
                  {filteredForm?.heading}
                </h2>
                <div
                  className="ql-editor p-0 text-sm w-full px-3"
                  style={{
                    color:
                      filteredForm?.description_color ||
                      "var(--app-text-secondary)",
                    fontFamily: `var(--font-${filteredForm?.font_family
                      ?.toLowerCase()
                      .replace(/\s+/g, "-")})`,
                  }}
                  dangerouslySetInnerHTML={{
                    __html: decodeHtml(filteredForm?.description || ""),
                  }}
                />
              </div>
              <form
                onSubmit={(e) => handleSubmit(e, [])}
                className="w-full space-y-4"
                style={{
                  fontFamily: `var(--font-${filteredForm?.font_family
                    ?.toLowerCase()
                    .replace(/\s+/g, "-")})`,
                }}
              >
                {fields?.map((field: any) => {
                  const FormElement =
                    toolContainersElement[
                      field.component as keyof typeof toolContainersElement
                    ];
                  return (
                    <FormElement
                      key={field.id}
                      {...field}
                      isPreview={true}
                      workspace_id={filteredForm?.workspace_id}
                    />
                  );
                })}
                <div
                  className={`flex mt-6 ${
                    filteredForm?.button_properties?.position === "left"
                      ? "justify-start"
                      : filteredForm?.button_properties?.position === "center"
                      ? "justify-center"
                      : "justify-end"
                  }`}
                >
                  <Button
                    type="submit"
                    disabled={isPending || isCopyPage}
                    style={{
                      backgroundColor:
                        filteredForm?.button_properties?.backgroundColor ||
                        "var(--color-reverse-universal)",
                      color:
                        filteredForm?.button_properties?.textColor ||
                        "var(--color-universal)",
                    }}
                  >
                    {isPending
                      ? "Submitting..."
                      : filteredForm?.button_properties?.text || "Submit"}
                  </Button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>

      <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
        <span>Powered by</span>
        <Link
          href={`/signup?ref=${referrelCode}`}
          target="_blank"
          className="cursor-pointer"
        >
          <Image
            src="/logo.png"
            alt="Automate Business Logo"
            height={100}
            width={100}
            quality={100}
            className="h-8 w-auto ml-2"
          />
        </Link>
      </div>
    </div>
  );
};

export default FormWrapper;
