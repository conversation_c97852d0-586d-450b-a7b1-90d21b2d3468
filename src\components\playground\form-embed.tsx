import React, { useState } from "react";
import { Button } from "@/components/ui/button";

interface FormEmbedProps {
  open: boolean;
  onClose: () => void;
  formUrl: string; // The public URL of the form to embed
}

const DEFAULT_WIDTH = 640;
const DEFAULT_HEIGHT = 488;

const FormEmbed: React.FC<FormEmbedProps> = ({ open, onClose, formUrl }) => {
  const [width, setWidth] = useState(DEFAULT_WIDTH);
  const [height, setHeight] = useState(DEFAULT_HEIGHT);
  const [copied, setCopied] = useState(false);

  if (!open) return null;

  const embedHtml = `<iframe src="${formUrl}?embedded=true" width="${width}" height="${height}" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe>`;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(embedHtml);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Optionally show error
    }
  };

  return (
    <div className="max-w-xl w-full mx-auto mt-12 bg-[#f8f9fa] dark:bg-app-background border border-gray-200 dark:border-app-border-primary rounded-xl shadow-lg p-8">
      <h2 className="text-2xl font-serif font-bold mb-6 text-gray-800 dark:text-app-text-color">Embed HTML</h2>
      <label className="block text-sm font-medium text-gray-700 dark:text-app-text-secondary mb-2">Copy and paste this HTML to embed your form:</label>
      <div className="mb-6">
        <textarea
          className="w-full p-3 border border-gray-300 dark:border-app-border-secondary rounded-lg bg-white dark:bg-app-main-background text-gray-800 dark:text-app-text-color font-mono text-sm resize-none focus:ring-2 focus:ring-blue-200 focus:border-blue-400 transition"
          rows={4}
          value={embedHtml}
          readOnly
        />
      </div>
      <div className="flex flex-wrap gap-6 mb-8">
        <div>
          <label className="block text-xs text-gray-600 dark:text-app-text-secondary mb-1">Width</label>
          <input
            type="number"
            className="w-28 p-2 border border-gray-300 dark:border-app-border-secondary rounded-md bg-white dark:bg-app-main-background text-gray-800 dark:text-app-text-color focus:ring-2 focus:ring-blue-200 focus:border-blue-400 transition"
            value={width}
            min={100}
            onChange={e => setWidth(Number(e.target.value))}
          />
          <span className="ml-2 text-xs text-gray-500 dark:text-app-text-secondary">px</span>
        </div>
        <div>
          <label className="block text-xs text-gray-600 dark:text-app-text-secondary mb-1">Height</label>
          <input
            type="number"
            className="w-28 p-2 border border-gray-300 dark:border-app-border-secondary rounded-md bg-white dark:bg-app-main-background text-gray-800 dark:text-app-text-color focus:ring-2 focus:ring-blue-200 focus:border-blue-400 transition"
            value={height}
            min={100}
            onChange={e => setHeight(Number(e.target.value))}
          />
          <span className="ml-2 text-xs text-gray-500 dark:text-app-text-secondary">px</span>
        </div>
      </div>
      <div className="flex justify-end items-center gap-4">
        <Button onClick={handleCopy} variant="default" size="default">
          Copy
        </Button>
        {copied && (
          <span className="text-green-600 dark:text-green-400 text-base font-medium">Copied to clipboard!</span>
        )}
      </div>
    </div>
  );
};

export default FormEmbed;
