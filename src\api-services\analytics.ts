import { makeRequest } from "./utils";
import { useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/analytics`;

function getViewsTrend(formId: string, from: string, to: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/views-trend?formId=${formId}&&from=${from}&&to=${to}`,
        method: "GET"
    })
}

const useGetViewsTrend = (formId: string, from: string, to: string) => {
    return useQuery({
        queryKey: ['views-trend', formId, from, to],
        queryFn: () => getViewsTrend(formId, from, to)
    })
}

function getSubmissionsTrend(formId: string, from: string, to: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/responses-trend?formId=${formId}&&from=${from}&&to=${to}`,
        method: "GET"
    })
}

const useGetSubmissionsTrend = (formId: string, from: string, to: string) => {
    return useQuery({
        queryKey: ['submissions-trend', formId, from, to],
        queryFn: () => getSubmissionsTrend(formId, from, to)
    })
}

function getViewsByDevice(formId: string, from: string, to: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/getDeviceWiseResponseCount/${formId}?from=${from}&&to=${to}`,
        method: "GET"
    })
}

const useGetViewsByDevice = (formId: string, from: string, to: string) => {
    return useQuery({
        queryKey: ['views-by-device', formId, from, to],
        queryFn: () => getViewsByDevice(formId, from, to)
    })
}

function getCardData(formId: string, from: string, to: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/form/${formId}?from=${from}&&to=${to}`,
        method: "GET"
    })
}

const useGetCardData = (formId: string, from: string, to: string) => {
    return useQuery({
        queryKey: ['card-data', formId, from, to],
        queryFn: () => getCardData(formId, from, to)
    })
}


export { useGetViewsTrend, useGetSubmissionsTrend, useGetViewsByDevice, useGetCardData };




