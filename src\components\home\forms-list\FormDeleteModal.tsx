import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import React from "react";

interface CustomModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  onClose: () => void;
  onConfirm: () => void;
}

const FormDeleteModal: React.FC<CustomModalProps> = ({
  isOpen,
  title,
  message,
  onClose,
  onConfirm,
}) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed z-10 inset-0 flex items-center justify-center bg-black bg-opacity-50"
      onClick={onClose}
    >
      <div
        className="bg-app-hero-background text-app-text-color rounded-lg p-6 max-w-lg w-full shadow-lg relative flex flex-col gap-4"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          className="absolute top-3 right-3 text-app-text-secondary hover:text-app-text-color"
          onClick={onClose}
        >
          <X className="w-5 h-5" />
        </button>
        <div className="flex flex-col gap-2">
          <h2 className="text-lg font-semibold">{title}</h2>
          <p className="text-sm text-app-text-secondary">
            Are you sure you want to move the form{" "}
            <span className="text-app-text-color">{message}</span> to trash? You can restore it later from the trash.
          </p>
        </div>
        <div className="flex items-center justify-end gap-3 mt-4">
          <Button
            type="button"
            className="h-8 w-fit hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C]  rounded-xl hover:border-app-text-color"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="h-8  w-fit hover:text-red-500  hover:bg-white  bg-red-500 border border-red-500 text-white rounded-xl"
            onClick={onConfirm}
          >
            Move to Trash
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FormDeleteModal;
