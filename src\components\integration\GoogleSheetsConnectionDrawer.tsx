import React, { useState, useEffect } from "react";
import { X, Loader2, ExternalLink, FileSpreadsheet } from "lucide-react";
import {
  useAddConnection,
  useCreateNewSheet,
  useLinkSheet,
  useGetIntegrationActions,
  useGetConnections,
  useGetSheetDetails,
  useGetGooglePickerConfig,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import toast from "react-hot-toast";
import { useGetFormFields } from "@/api-services/form_fields";
import SelectInputCombo from "@/components/common/SelectInputCombo";
import GooglePickerDebug from "./GooglePickerDebug";
import {
  checkGoogleDrivePermissions,
  validatePickerConfig,
  getPermissionErrorMessage,
  debugGoogleDrivePermissions
} from "@/utils/googleDrivePermissions";

interface Sheet {
  webViewLink?: string;
  id?: string;
  name?: string;
  createdTime?: string;
  sheetId?: number;
  sheetName?: string;
  columnNames?: string[];
}

interface Action {
  id: string;
  name: string;
  description: string;
}

interface Connection {
  id: string;
  name: string;
}

interface ExistingConnection {
  formIntegatedId: string;
  credentialId: string;
  credentialName: string;
  enabled: boolean;
  connectedAt: string;
  metadata: {
    res: {
      spreadsheetUrl: string;
    };
    spreadsheetId: string;
  };
  mappedData?: {
    id: string;
    name: string;
    title: string;
    key: string;
  }[];
}

interface GoogleSheetsConnectionDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: ExistingConnection[];
  onRefresh?: () => void;
}

// Helper to build options for SelectInputCombo
const buildFieldOptions = (fields: any[]) => {
  const options: {
    id: string;
    name: string;
    title: string;
    index: number;
  }[] = [];
  let logicalIndex = 1;
  fields.forEach((f) => {
    if (f.component === "NAME_INPUT") {
      options.push({
        id: f.id,
        name: "firstName",
        title: f.firstNameTitle || "First Name",
        index: logicalIndex,
      });
      options.push({
        id: f.id,
        name: "lastName",
        title: f.lastNameTitle || "Last Name",
        index: logicalIndex,
      });
      logicalIndex++;
    } else if (f.component === "ADDRESS") {
      options.push({
        id: f.id,
        name: "address",
        title: "Address",
        index: logicalIndex,
      });
      const allowed = f.allowedAddressFields || {
        country: true,
        city: true,
        pincode: true,
        state: true,
      };
      if (allowed.city) {
        options.push({
          id: f.id,
          name: "city",
          title: "City",
          index: logicalIndex,
        });
      }
      if (allowed.state) {
        options.push({
          id: f.id,
          name: "state",
          title: "State",
          index: logicalIndex,
        });
      }
      if (allowed.country) {
        options.push({
          id: f.id,
          name: "country",
          title: "Country",
          index: logicalIndex,
        });
      }
      if (allowed.pincode) {
        options.push({
          id: f.id,
          name: "pincode",
          title: "Pincode",
          index: logicalIndex,
        });
      }
      logicalIndex++;
    } else {
      options.push({
        id: f.id,
        name: f.name,
        title: f.title || f.name,
        index: logicalIndex,
      });
      logicalIndex++;
    }
  });
  return options;
};

// Converts template to backend key (for saving to API)
const convertTemplateToMapped = (
  template: string,
  options: any[],
  fields: any[]
): string => {
  // Replace all {{index.Name}} with correct backend key
  return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
    // Find the option with matching index and name
    const opt = options.find(
      (opt) => String(opt.index) === idx && opt.name === name
    );
    if (!opt) return match;
    // For NAME_INPUT, use the new format {{fieldId.name.firstName}} or {{fieldId.name.lastName}}
    const field = fields.find((f) => f.id === opt.id);
    if (field && field.component === "NAME_INPUT") {
      return `{{${opt.id}.name.${opt.name}}}`;
    }
    // For ADDRESS, use {{fieldId.address.address}}, etc.
    if (field && field.component === "ADDRESS") {
      return `{{${opt.id}.address.${opt.name}}}`;
    }
    // For other fields, just use {{fieldId}}
    return `{{${opt.id}}}`;
  });
};

// Google Picker Component
interface GooglePickerProps {
  credentialId: string;
  onSelect: (spreadsheet: Sheet) => void;
  disabled?: boolean;
}

const GooglePicker: React.FC<GooglePickerProps> = ({
  credentialId,
  onSelect,
  disabled,
}) => {
  const [isPickerLoaded, setIsPickerLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const { mutate: getPickerConfig } = useGetGooglePickerConfig();

  useEffect(() => {
    let isMounted = true;

    const loadGooglePicker = async () => {
      try {
        // Check if Google API is already loaded
        if (window.gapi && window.google?.picker) {
          if (isMounted) {
            setIsPickerLoaded(true);
          }
          return;
        }

        // Check if script is already in the document
        const existingScript = document.querySelector('script[src="https://apis.google.com/js/api.js"]');
        if (existingScript) {
          // Wait for existing script to load
          existingScript.addEventListener('load', () => {
            if (window.gapi && isMounted) {
              window.gapi.load("picker", () => {
                if (isMounted) {
                  setIsPickerLoaded(true);
                }
              });
            }
          });
          return;
        }

        // Load Google API script
        const script = document.createElement("script");
        script.src = "https://apis.google.com/js/api.js";
        script.async = true;
        script.defer = true;

        script.onload = () => {
          if (window.gapi && isMounted) {
            window.gapi.load("picker", () => {
              if (isMounted) {
                setIsPickerLoaded(true);
              }
            });
          }
        };

        script.onerror = () => {
          if (isMounted) {
            setLoadError("Failed to load Google API script");
            toast.error("Failed to load Google Picker. Please check your internet connection and try again.");
          }
        };

        document.head.appendChild(script);
      } catch (error) {
        if (isMounted) {
          setLoadError("Error initializing Google Picker");
          console.error("Error loading Google Picker:", error);
        }
      }
    };

    loadGooglePicker();

    return () => {
      isMounted = false;
    };
  }, []);

  const openPicker = () => {
    if (!isPickerLoaded || disabled) {
      toast.error("Google Picker is not ready. Please wait and try again.");
      return;
    }

    if (!window.google?.picker) {
      toast.error("Google Picker API is not loaded. Please refresh the page and try again.");
      return;
    }

    setIsLoading(true);
    getPickerConfig(credentialId, {
      onSuccess: async (response) => {
        try {
          // Extract data from the nested response structure
          const configData = response?.data?.data;
          if (!configData) {
            throw new Error("Invalid picker configuration received");
          }

          const { apiKey, clientId, token } = configData;

          if (!apiKey || !token) {
            throw new Error("Missing required authentication data");
          }

          // Validate picker configuration
          const configValidation = validatePickerConfig(configData);
          if (!configValidation.isValid) {
            throw new Error(`Configuration errors: ${configValidation.errors.join(', ')}`);
          }

          // Check Google Drive permissions
          const permissionCheck = await checkGoogleDrivePermissions(token);
          if (!permissionCheck.hasPermission) {
            const errorMessage = getPermissionErrorMessage(permissionCheck);
            toast.error(errorMessage);
            throw new Error(`Permission error: ${permissionCheck.error}`);
          }

          // Debug in development mode
          if (process.env.NODE_ENV === 'development') {
            debugGoogleDrivePermissions(token, apiKey);
          }

          console.log("Picker config loaded and validated successfully");

          // Create Drive view with proper permissions
          const driveView = new window.google.picker.DocsView(window.google.picker.ViewId.SPREADSHEETS)
            .setIncludeFolders(false)
            .setSelectFolderEnabled(false)
            .setMimeTypes("application/vnd.google-apps.spreadsheet");

          const picker = new window.google.picker.PickerBuilder()
            .addView(driveView)
            .setOAuthToken(token)
            .setDeveloperKey(apiKey)
            .setAppId(clientId?.split('.')[0] || '') // Extract app ID from client ID
            .setCallback((data: any) => {
              try {
                console.log("Picker callback data:", data);

                if (data.action === window.google.picker.Action.PICKED) {
                  const doc = data.docs?.[0];
                  if (!doc) {
                    toast.error("No document selected");
                    return;
                  }

                  // Check if user has permission to access the file
                  if (doc.serviceId === 'docs' && doc.mimeType === 'application/vnd.google-apps.spreadsheet') {
                    const spreadsheet: Sheet = {
                      id: doc.id,
                      name: doc.name,
                      webViewLink: doc.url,
                      createdTime: doc.createdTime,
                    };
                    onSelect(spreadsheet);
                    toast.success("Spreadsheet selected successfully!");
                  } else {
                    toast.error("Please select a valid Google Spreadsheet");
                  }
                } else if (data.action === window.google.picker.Action.CANCEL) {
                  console.log("User cancelled Google Picker");
                } else if (data.action === window.google.picker.Action.LOADED) {
                  console.log("Google Picker loaded successfully");
                }
              } catch (callbackError) {
                console.error("Error in picker callback:", callbackError);
                toast.error("Error processing selected file");
              } finally {
                setIsLoading(false);
              }
            })
            .enableFeature(window.google.picker.Feature.NAV_HIDDEN)
            .enableFeature(window.google.picker.Feature.MULTISELECT_ENABLED, false)
            .setTitle("Select Google Spreadsheet")
            .setLocale('en') // Set locale
            .setOrigin(window.location.origin)
            .setSize(1051, 650)
            .build();

          picker.setVisible(true);
        } catch (error) {
          console.error("Error building picker:", error);
          toast.error("Failed to open Google Picker. Please try again.");
          setIsLoading(false);
        }
      },
      onError: (error) => {
        console.error("Error getting picker config:", error);
        toast.error("Failed to load Google Picker configuration. Please try again.");
        setIsLoading(false);
      },
    });
  };

  return (
    <div>
      <button
        onClick={openPicker}
        disabled={disabled || isLoading || !isPickerLoaded || !!loadError}
        className={`w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color flex items-center justify-center gap-2 ${
          disabled || isLoading || !isPickerLoaded || !!loadError
            ? "opacity-50 cursor-not-allowed"
            : "hover:bg-app-main-background"
        }`}
      >
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading Picker...
          </>
        ) : loadError ? (
          <>
            <FileSpreadsheet className="h-4 w-4" />
            Picker Error - Refresh Page
          </>
        ) : (
          <>
            <FileSpreadsheet className="h-4 w-4" />
            Select Spreadsheet
          </>
        )}
      </button>
      {!isPickerLoaded && !loadError && (
        <div className="text-sm text-gray-500 mt-1">
          Loading Google Picker...
        </div>
      )}
      {loadError && (
        <div className="text-sm text-red-500 mt-1">
          {loadError}. Please refresh the page and try again.
        </div>
      )}
    </div>
  );
};

// Enhanced type declaration for the Google Picker API
declare global {
  interface Window {
    google: {
      picker: {
        PickerBuilder: new () => {
          addView: (view: any) => any;
          setOAuthToken: (token: string) => any;
          setDeveloperKey: (key: string) => any;
          setAppId: (appId: string) => any;
          setCallback: (callback: (data: any) => void) => any;
          enableFeature: (feature: any, enabled?: boolean) => any;
          setTitle: (title: string) => any;
          setSelectableMimeTypes: (mimeTypes: string) => any;
          setOrigin: (origin: string) => any;
          setLocale: (locale: string) => any;
          setSize: (width: number, height: number) => any;
          build: () => {
            setVisible: (visible: boolean) => void;
          };
        };
        DocsView: new (viewId?: string) => {
          setIncludeFolders: (include: boolean) => any;
          setSelectFolderEnabled: (enabled: boolean) => any;
          setMimeTypes: (mimeTypes: string) => any;
          setMode: (mode: string) => any;
        };
        Action: {
          PICKED: string;
          CANCEL: string;
          LOADED: string;
        };
        Feature: {
          NAV_HIDDEN: string;
          MULTISELECT_ENABLED: string;
        };
        ViewId: {
          SPREADSHEETS: string;
          DOCS: string;
          FOLDERS: string;
        };
        DocsViewMode: {
          LIST: string;
          GRID: string;
        };
      };
    };
    gapi: {
      load: (api: string, callback: () => void) => void;
    };
  }
}

// Utility to check if a template references only valid fields
function isTemplateValid(template: string, fields: any[]): boolean {
  const regex = /\{\{([a-f0-9-]+)(?:\.[^\}]+)?\}\}/gi;
  let match;
  while ((match = regex.exec(template)) !== null) {
    const fieldId = match[1];
    if (!fields.some((f) => f.id === fieldId)) {
      return false;
    }
  }
  return true;
}

export default function GoogleSheetsConnectionDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: GoogleSheetsConnectionDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedSheet, setSelectedSheet] = useState<Sheet | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [createdSheetLink, setCreatedSheetLink] = useState<string | null>(null);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [selectedSpreadsheet, setSelectedSpreadsheet] = useState<Sheet | null>(
    null
  );
  const [formData, setFormData] = useState<{
    [key: string]: string;
  }>({});

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");
  const credentialId = searchParams.get("credential_id");
  const successState = searchParams.get("success");

  const { data: formFields } = useGetFormFields(formId!);
  const addConnectionMutation = useAddConnection();
  const { mutate: createSheet, isPending: isCreatingSheet } =
    useCreateNewSheet();
  const { mutate: linkSheet, isPending: isLinkingSheet } = useLinkSheet();

  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { mutate: disconnectIntegration, isPending } =
    useDisconnectIntegration();
  const {
    mutate: getSheetDetails,
    data: sheetDetails,
    isPending: isLoadingSheetDetails,
  } = useGetSheetDetails();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;
  const hasExistingConnection = existingConnections.length > 0;

  const GOOGLE_SHEETS_BASE_URL = "https://docs.google.com/spreadsheets/d/";

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (successState === "true" && credentialId && connections.length > 0) {
      setSelectedConnection(credentialId);
      setConnectionType("existing");
    }
  }, [successState, credentialId, connections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
      localStorage.setItem("selectedSheetsAction", initialActionId);
      const selectedActionObj = actions.find((a) => a.id === initialActionId);
      localStorage.setItem(
        "selectedSheetsActionName",
        selectedActionObj?.name || ""
      );
    }
  }, [initialActionId, actions]);

  useEffect(() => {
    if (selectedSpreadsheet?.id && selectedConnection) {
      console.log("Fetching sheet details with:", {
        credentialId: selectedConnection,
        spreadsheetId: selectedSpreadsheet.id,
      });
      getSheetDetails(
        {
          credentialId: selectedConnection,
          spreadsheetId: selectedSpreadsheet.id,
        },
        {
          onSuccess: (data) => {
            console.log("Sheet details response:", data);
          },
          onError: (error) => {
            console.error("Error fetching sheet details:", error);
          },
        }
      );
    }
  }, [selectedSpreadsheet, selectedConnection, getSheetDetails]);

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
    setSelectedSheet(null);
    setSelectedSpreadsheet(null);

    if (successState !== "true" && connectionId) {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set("credential_id", connectionId);
      window.history.pushState({}, "", newUrl);
    }
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
    setSelectedSheet(null);
    if (actionId) {
      const selectedActionObj = actions.find((a) => a.id === actionId);
      localStorage.setItem("selectedSheetsAction", actionId);
      localStorage.setItem(
        "selectedSheetsActionName",
        selectedActionObj?.name || ""
      );
    } else {
      localStorage.removeItem("selectedSheetsAction");
      localStorage.removeItem("selectedSheetsActionName");
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName) return;

    setIsAddingConnection(true);
    try {
      const response = await addConnectionMutation.mutateAsync({
        integrationId,
        formId: formId || "",
        formType: formType || "",
        name: connectionName,
        actionId: selectedAction || "",
      });

      if (response.data?.data?.id) {
        setSelectedConnection(response.data.data.id);
        setConnectionType("existing");
        refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    setIsDisconnecting(true);
    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: async () => {
          await onRefresh?.();
          toast.success("Sheet disconnected successfully!");
          setSaveSuccess(false);
          setCreatedSheetLink(null);
          setSelectedAction("");
          setSelectedConnection("");
          setSelectedSheet(null);
          setConnectionType("new");
          setConnectionName("");
        },
        onError: (error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect sheet. Please try again.");
        },
        onSettled: () => {
          setIsDisconnecting(false);
        },
      }
    );
  };

  const handleFormDataChange = async (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!selectedAction || !selectedConnection) return;

    if (existingConnections.length > 0) {
      toast.error("Form already has a connection. Please disconnect first.");
      return;
    }

    setIsSaving(true);
    setSaveSuccess(false);
    setCreatedSheetLink(null);

    const selectedActionObj = actions.find((a) => a.id === selectedAction);

    if (selectedActionObj?.name === "Create Sheet") {
      createSheet(
        {
          formId: formId as string,
          credentialId: selectedConnection,
          actionId: selectedAction,
        },
        {
          onSuccess: async (response) => {
            const link = response?.data?.data?.webViewLink;

            if (link) {
              toast.success("Sheet created and linked successfully!");
              setCreatedSheetLink(link);
              setSaveSuccess(true);
              await onRefresh?.();
            }
          },
          onError: (error: any) => {
            console.error("Error creating sheet:", error);
            setSaveSuccess(false);
            toast.error(
              error?.response?.data?.message ||
                "Failed to create sheet. Please try again."
            );
          },
          onSettled: () => {
            setIsSaving(false);
            onClose();
          },
        }
      );
    } else if (selectedActionObj?.name === "Link Sheet") {
      // Create column mapped data array
      const columnMappedData: any[] = [];
      const fields = formFields?.data?.fields || [];
      const options = buildFieldOptions(fields);

      // For each column in the selected sheet, create a mapping
      if (selectedSheet?.columnNames) {
        selectedSheet.columnNames.forEach((columnName) => {
          const template = formData[columnName];
          if (template) {
            columnMappedData.push({
              id: columnName,
              name: columnName,
              title: columnName,
              key: convertTemplateToMapped(template, options, fields),
            });
          }
        });
      }

      linkSheet(
        {
          formId: formId as string,
          credentialId: selectedConnection,
          spreadsheetId: selectedSpreadsheet?.id || "",
          actionId: selectedAction,
          sheetId: selectedSheet?.sheetId?.toString() || "",
          column_mapped_data: columnMappedData,
        },
        {
          onSuccess: async (response) => {
            if (response?.data?.success) {
              toast.success("Sheet linked successfully!");
              const sheetUrl = `${GOOGLE_SHEETS_BASE_URL}${selectedSheet?.id}`;
              setCreatedSheetLink(sheetUrl);
              setSaveSuccess(true);
              await onRefresh?.();
            }
          },
          onError: (error: any) => {
            console.error("Error linking sheet:", error);
            setSaveSuccess(false);
            toast.error(
              error?.response?.data?.message ||
                "Failed to link sheet. Please try again."
            );
          },
          onSettled: () => {
            setIsSaving(false);
            onClose();
          },
        }
      );
    } else {
      setIsSaving(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div className="fixed inset-0 bg-black/50 z-[199] transition-opacity" />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect Google Sheets Account
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">Google Sheets</h3>

              {/* Debug component for development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mb-6">
                  <GooglePickerDebug credentialId={selectedConnection} />
                </div>
              )}

              {hasExistingConnection && !saveSuccess && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-700 mb-2">
                    Connected to Google Sheets
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <ExternalLink className="h-4 w-4 text-blue-600" />
                    <Link
                      href={
                        existingConnections[0].metadata.spreadsheetId
                          ? `${GOOGLE_SHEETS_BASE_URL}${existingConnections[0].metadata.spreadsheetId}`
                          : existingConnections[0].metadata.res
                              ?.spreadsheetUrl || "#"
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      View connected sheet
                    </Link>
                  </div>
                </div>
              )}

              {saveSuccess && createdSheetLink && !hasExistingConnection && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-700 mb-2">
                    {actions.find((a) => a.id === selectedAction)?.name ===
                    "Create Sheet"
                      ? "Sheet created and linked successfully!"
                      : "Sheet linked successfully!"}
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <ExternalLink className="h-4 w-4 text-green-600" />
                    <a
                      href={createdSheetLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-green-600 hover:underline"
                    >
                      View sheet
                    </a>
                  </div>
                </div>
              )}

              {!hasExistingConnection && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Select Action
                    </label>
                    <select
                      value={selectedAction}
                      onChange={handleActionSelect}
                      className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                    >
                      <option value="">Select an action</option>
                      {actions.map((action) => (
                        <option key={action.id} value={action.id}>
                          {action.name}
                        </option>
                      ))}
                    </select>
                    {selectedAction && (
                      <p className="text-sm text-gray-500 mt-1">
                        {
                          actions.find((a) => a.id === selectedAction)
                            ?.description
                        }
                      </p>
                    )}
                  </div>

                  {selectedAction && (
                    <>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="new"
                            name="connectionType"
                            value="new"
                            checked={connectionType === "new"}
                            onChange={() => setConnectionType("new")}
                            className="w-4 h-4"
                          />
                          <label htmlFor="new" className="text-sm font-medium">
                            Add New Connection
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="existing"
                            name="connectionType"
                            value="existing"
                            checked={connectionType === "existing"}
                            onChange={() => setConnectionType("existing")}
                            disabled={!hasValidConnections}
                            className="w-4 h-4"
                          />
                          <label
                            htmlFor="existing"
                            className={`text-sm font-medium ${
                              !hasValidConnections ? "text-gray-400" : ""
                            }`}
                          >
                            Select Existing Connection
                          </label>
                        </div>
                      </div>

                      {connectionType === "new" ? (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              Connection Name
                            </label>
                            <input
                              type="text"
                              value={connectionName}
                              onChange={(e) =>
                                setConnectionName(e.target.value)
                              }
                              className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              placeholder="Enter connection name"
                            />
                          </div>
                          <button
                            onClick={handleAddConnection}
                            disabled={!connectionName || isAddingConnection}
                            className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                              !connectionName || isAddingConnection
                                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                                : "bg-app-text-color text-app-background hover:bg-opacity-90"
                            }`}
                          >
                            {isAddingConnection ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin" />
                                Adding...
                              </>
                            ) : (
                              <>
                                <FileSpreadsheet className="h-4 w-4" />
                                Add New Connection
                              </>
                            )}
                          </button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              Select Connection
                            </label>
                            <select
                              value={selectedConnection}
                              onChange={handleConnectionSelect}
                              disabled={!hasValidConnections}
                              className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            >
                              <option value="">Select a connection</option>
                              {connections.map((connection) => (
                                <option
                                  key={connection.id}
                                  value={connection.id}
                                >
                                  {connection.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          {selectedAction && selectedConnection && (
                            <>
                              {actions.find((a) => a.id === selectedAction)
                                ?.name === "Link Sheet" && (
                                <div className="space-y-4">
                                  <div>
                                    <label className="block text-sm font-medium mb-1">
                                      Select Spreadsheet
                                    </label>
                                    <GooglePicker
                                      credentialId={selectedConnection}
                                      onSelect={(spreadsheet) => {
                                        setSelectedSpreadsheet(spreadsheet);
                                        setSelectedSheet(null);
                                      }}
                                      // disabled={isLoadingSheets}
                                    />
                                    {selectedSpreadsheet && (
                                      <div className="mt-2 p-2 bg-app-background rounded-md">
                                        <div className="flex items-center gap-2 text-sm">
                                          <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                                          <span className="font-medium text-app-text-color">
                                            {selectedSpreadsheet.name}
                                          </span>
                                        </div>
                                        {selectedSpreadsheet.createdTime && (
                                          <div className="text-xs text-app-text-secondary mt-1">
                                            Created:{" "}
                                            {new Date(
                                              selectedSpreadsheet.createdTime
                                            ).toLocaleDateString()}
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>

                                  {selectedSpreadsheet && (
                                    <div>
                                      <label className="block text-sm font-medium mb-1">
                                        Select Sheet
                                      </label>
                                      <select
                                        value={
                                          selectedSheet?.sheetId?.toString() ||
                                          ""
                                        }
                                        onChange={(e) => {
                                          const sheet =
                                            sheetDetails?.data?.data?.find(
                                              (s: Sheet) =>
                                                s.sheetId?.toString() ===
                                                e.target.value
                                            );
                                          setSelectedSheet(sheet || null);
                                        }}
                                        disabled={isLoadingSheetDetails}
                                        className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                      >
                                        <option value="">Select a sheet</option>
                                        {sheetDetails?.data?.data?.map(
                                          (sheet: Sheet) => (
                                            <option
                                              key={sheet.sheetId}
                                              value={sheet.sheetId}
                                            >
                                              {sheet.sheetName ||
                                                `Sheet ${sheet.sheetId}`}
                                            </option>
                                          )
                                        )}
                                      </select>
                                      {isLoadingSheetDetails && (
                                        <div className="flex items-center gap-2 mt-2">
                                          <Loader2 className="h-4 w-4 animate-spin" />
                                          <span className="text-sm text-gray-500">
                                            Loading sheets...
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  {selectedSheet && (
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <ExternalLink className="h-4 w-4" />
                                        <Link
                                          href={
                                            selectedSpreadsheet?.id &&
                                            selectedSheet?.sheetId !== undefined
                                              ? `https://docs.google.com/spreadsheets/d/${selectedSpreadsheet.id}/edit#gid=${selectedSheet.sheetId}`
                                              : "#"
                                          }
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:underline"
                                        >
                                          View selected sheet
                                        </Link>
                                      </div>
                                      {selectedSheet.createdTime && (
                                        <div className="text-sm text-gray-500">
                                          Created:{" "}
                                          {new Date(
                                            selectedSheet.createdTime
                                          ).toLocaleDateString()}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              )}

                              {actions.find((a) => a.id === selectedAction)
                                ?.name === "Create Sheet" && (
                                <div className="p-4 bg-gray-50 rounded-md">
                                  <p className="text-sm text-gray-600">
                                    A new sheet will be created with the form
                                    name and will be automatically linked to
                                    your form.
                                  </p>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}

              {selectedSheet && selectedSheet.columnNames && (
                <div className="space-y-4 mt-4">
                  <h4 className="text-sm font-medium">
                    Map Form Fields to Sheet Columns
                  </h4>
                  {selectedSheet.columnNames.map((columnName) => (
                    <div key={columnName}>
                      <label className="block text-sm font-medium mb-1">
                        {columnName}
                      </label>
                      <SelectInputCombo
                        options={buildFieldOptions(
                          formFields?.data?.fields || []
                        )}
                        value={formData[columnName] || ""}
                        onChange={(val) =>
                          handleFormDataChange(columnName, val)
                        }
                        placeholder={`Map form fields to ${columnName}`}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {hasExistingConnection ? (
                  <button
                    onClick={handleDisconnect}
                    disabled={isDisconnecting}
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      isDisconnecting
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-red-500 text-white hover:bg-red-600"
                    }`}
                  >
                    {isDisconnecting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Disconnecting...
                      </>
                    ) : (
                      "Disconnect"
                    )}
                  </button>
                ) : (
                  <>
                    <button
                      onClick={onClose}
                      className="flex-1 px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedAction ||
                        (connectionType === "new" && !connectionName) ||
                        (connectionType === "existing" &&
                          !selectedConnection) ||
                        (actions.find((a) => a.id === selectedAction)?.name ===
                          "Link Sheet" &&
                          !selectedSheet) ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedAction ||
                        (connectionType === "new" && !connectionName) ||
                        (connectionType === "existing" &&
                          !selectedConnection) ||
                        (actions.find((a) => a.id === selectedAction)?.name ===
                          "Link Sheet" &&
                          !selectedSheet) ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save"
                      )}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
