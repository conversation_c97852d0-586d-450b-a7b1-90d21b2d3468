import React, { useState, useEffect } from "react";
import { X, Loader2, Network } from "lucide-react";
import {
  useGetIntegrationActions,
  useGetConnections,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import {
  useAddWhatsAppConnection,
  useGetWhatsAppTemplates,
  useLinkWhatsAppForm,
} from "@/api-services/whatsapp";
import { useSearchParams } from "next/navigation";
import { useGetFormFields } from "@/api-services/form_fields";
import { toast } from "react-hot-toast";
import SelectInputCombo, {
  convertTemplateToMapped,
  convertMappedToTemplate,
} from "@/components/common/SelectInputCombo";

interface Action {
  id: string;
  name: string;
  description: string;
}

interface Connection {
  id: string;
  name: string;
}

interface Template {
  name: string;
  components: {
    type: string;
    text: string;
    example?: {
      body_text: string[][];
    };
  }[];
  language: string;
  status: string;
  id: string;
}

interface WhatsappDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: {
    formIntegatedId: string;
    credentialId: string;
    credentialName: string;
    enabled: boolean;
    connectedAt: string;
    metadata: {
      template_id: string;
      template_name?: string;
    };
    mappedData: {
      id: string;
      name: string;
      title: string;
      key: string;
    }[];
    actionId: string;
  }[];
  onRefresh?: () => void;
}

export default function WhatsappDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: WhatsappDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [channelId, setChannelId] = useState("");
  const [wabaId, setWabaId] = useState("");
  const [phoneNumberId, setPhoneNumberId] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [formData, setFormData] = useState<{
    recipient: string;
    variables: { [key: string]: string };
    recipientTemplate?: string;
    [key: string]: any;
  }>({
    recipient: "",
    variables: {},
    recipientTemplate: "",
  });
  const [isSaving, setIsSaving] = useState(false);

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  const addWhatsAppConnectionMutation = useAddWhatsAppConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { data: templatesResponse, isLoading: isLoadingTemplates } =
    useGetWhatsAppTemplates(selectedConnection);
  const { data: formFields } = useGetFormFields(formId!);
  const { mutate: disconnectIntegration, isPending: isDisconnecting } =
    useDisconnectIntegration();
  const linkWhatsAppFormMutation = useLinkWhatsAppForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const templates: Template[] = templatesResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
    }
  }, [initialActionId]);

  useEffect(() => {
    if (saveSuccess && connectionName && connections.length > 0) {
      const newConnection = connections.find(
        (conn: Connection) => conn.name === connectionName
      );
      if (newConnection) {
        setConnectionType("existing");
        setSelectedConnection(newConnection.id);
      }
    }
  }, [connections, connectionName, saveSuccess]);

  useEffect(() => {
    if (existingConnections.length > 0) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      setSelectedConnection(connection.credentialId);
      setConnectionName(connection.credentialName);
      setSelectedAction(connection.actionId);
    }
  }, [existingConnections]);

  useEffect(() => {
    if (
      !isLoadingTemplates &&
      templatesResponse?.data?.data &&
      existingConnections.length > 0
    ) {
      const templates = templatesResponse.data.data;
      const connection = existingConnections[0];

      if (
        connection.metadata?.template_id ||
        connection.metadata?.template_name
      ) {
        const matchedTemplate = templates.find(
          (t: Template) =>
            t.id === connection.metadata.template_id ||
            t.name === connection.metadata.template_name
        );

        if (matchedTemplate) {
          setSelectedTemplate(matchedTemplate);
        }
      }
    }
  }, [isLoadingTemplates, templatesResponse?.data?.data, existingConnections]);

  useEffect(() => {
    if (
      existingConnections.length > 0 &&
      formFields?.data?.fields &&
      selectedTemplate
    ) {
      const connection = existingConnections[0];
      const fields = formFields.data.fields;
      const options = buildFieldOptions(fields);
      const newFormData: any = { recipient: "", variables: {} };
      if (connection.mappedData) {
        connection.mappedData.forEach((mappedField) => {
          if (mappedField.name === "recipient") {
            newFormData.recipientTemplate = isTemplateValid(
              mappedField.key,
              fields
            )
              ? convertMappedToTemplate(mappedField.key, options)
              : "";
          } else if (mappedField.name.startsWith("var")) {
            newFormData[`${mappedField.name}Template`] = isTemplateValid(
              mappedField.key,
              fields
            )
              ? convertMappedToTemplate(mappedField.key, options)
              : "";
          }
        });
      }
      setFormData((prev: any) => ({ ...prev, ...newFormData }));
    }
  }, [existingConnections, formFields?.data?.fields, selectedTemplate]);

  useEffect(() => {
    if (selectedConnection && !existingConnections.length) {
      setSelectedTemplate(null);
      setFormData({ recipient: "", variables: {} });
    }
  }, [selectedConnection, existingConnections.length]);

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: () => {
          toast.success("WhatsApp disconnected successfully!");
          onRefresh?.();
          onClose();
        },
        onError: (error: Error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect WhatsApp. Please try again.");
        },
      }
    );
  };

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);

    // Only reset if it's a new connection selection
    if (!existingConnections.length) {
      setSelectedTemplate(null);
      setFormData({ recipient: "", variables: {} });
    }
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
  };

  const handleTemplateSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const templateId = e.target.value;
    if (!templateId) return;

    const template = templates.find((t) => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      // Only reset form data if it's a new template selection and not from existing connection
      if (!existingConnections.length) {
        setFormData({
          recipient: "",
          variables: {},
        });
      }
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName || !apiKey || !channelId || !wabaId || !phoneNumberId)
      return;

    setIsAddingConnection(true);
    try {
      const response = await addWhatsAppConnectionMutation.mutateAsync({
        key: apiKey,
        name: connectionName,
        integration_id: integrationId,
        wa_channel_id: channelId,
        waba_id: wabaId,
        phone_number_id: phoneNumberId,
      });

      if (response?.success) {
        setSaveSuccess(true);
        setSuccessMessage(
          `Connection "${connectionName}" added successfully! You can now configure the WhatsApp settings.`
        );
        await refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
      toast.error("Failed to add connection. Please try again.");
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleSave = async () => {
    if (!formId || !selectedConnection || !selectedAction || !selectedTemplate)
      return;

    setIsSaving(true);
    try {
      // Create column mapped data array
      const columnMappedData: any[] = [];
      const fields = formFields?.data?.fields || [];
      const options = buildFieldOptions(fields);

      // Map recipient field
      if (formData.recipientTemplate) {
        columnMappedData.push({
          id: "recipient",
          name: "recipient",
          title: "Recipient",
          key: convertTemplateToMapped(
            formData.recipientTemplate,
            options,
            fields
          ),
        });
      }
      // Map template variables
      getTemplateVariables().forEach((_, index) => {
        const varKey = `var${index + 1}`;
        const varTemplate = formData[`${varKey}Template`];
        if (varTemplate) {
          columnMappedData.push({
            id: varKey,
            name: varKey,
            title: `Variable ${index + 1}`,
            key: convertTemplateToMapped(varTemplate, options, fields),
          });
        }
      });

      const response = await linkWhatsAppFormMutation.mutateAsync({
        form_id: formId,
        integration_id: integrationId,
        credential_id: selectedConnection,
        action_id: selectedAction,
        column_mapped_data: columnMappedData,
        template_id: selectedTemplate.id,
        template_name: selectedTemplate.name,
      });

      if (response?.success) {
        toast.success("WhatsApp integration linked successfully!");
        onRefresh?.();
        onClose();
      }
    } catch (error) {
      console.error("Error linking WhatsApp:", error);
      toast.error("Failed to link WhatsApp integration. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const getTemplateVariables = () => {
    if (!selectedTemplate) return [];

    const bodyComponent = selectedTemplate.components.find(
      (c) => c.type === "BODY"
    );
    if (!bodyComponent?.example?.body_text?.[0]) return [];

    return bodyComponent.example.body_text[0];
  };

  const renderTemplatePreview = () => {
    if (!selectedTemplate) return null;

    const bodyComponent = selectedTemplate.components.find(
      (c) => c.type === "BODY"
    );
    if (!bodyComponent) return null;

    let previewText = bodyComponent.text;

    // Replace each variable in the template
    getTemplateVariables().forEach((_, index) => {
      const variableKey = `var${index + 1}`;
      const varTemplate = formData[`${variableKey}Template`];

      if (varTemplate) {
        // Find all {{...}} in the varTemplate (e.g., {{1.firstName}} {{1.lastName}})
        const replaced = varTemplate.replace(
          /{{(.*?)}}/g,
          (match: string, fieldKey: string) => {
            // fieldKey might be like "1.firstName" or "6.Text"
            const [fieldId, subName] = fieldKey.split(".");
            const fieldOption = (formFields?.data?.fields || []).find(
              (f: any) => f.id === fieldId || f.id === fieldId.split("-")[0] // handle cases like "id-first"
            );
            if (fieldOption) {
              if (subName && fieldOption.component === "NAME_INPUT") {
                return subName === "firstName"
                  ? fieldOption.firstNameTitle || "First Name"
                  : fieldOption.lastNameTitle || "Last Name";
              }
              return fieldOption.title || fieldOption.name;
            }
            return match;
          }
        );
        // Replace {{index+1}} in the previewText with the replaced string
        previewText = previewText.replace(`{{${index + 1}}}`, replaced);
      } else {
        // If not mapped, leave as is
        previewText = previewText.replace(
          `{{${index + 1}}}`,
          `{{${index + 1}}}`
        );
      }
    });

    // Handle recipient field
    if (formData.recipientTemplate) {
      const replacedRecipient = formData.recipientTemplate.replace(
        /{{(.*?)}}/g,
        (match: string, fieldKey: string) => {
          const [fieldId, subName] = fieldKey.split(".");
          const fieldOption = (formFields?.data?.fields || []).find(
            (f: any) => f.id === fieldId || f.id === fieldId.split("-")[0]
          );
          if (fieldOption) {
            if (subName && fieldOption.component === "NAME_INPUT") {
              return subName === "firstName"
                ? fieldOption.firstNameTitle || "First Name"
                : fieldOption.lastNameTitle || "Last Name";
            }
            return fieldOption.title || fieldOption.name;
          }
          return match;
        }
      );
      previewText = `To: ${replacedRecipient}\n\n${previewText}`;
    }

    return (
      <div className="mt-4 p-4 bg-app-background rounded-md">
        <h4 className="text-sm font-medium mb-2">Template Preview</h4>
        <p className="text-sm whitespace-pre-wrap">{previewText}</p>
      </div>
    );
  };

  // Helper to build options for SelectInputCombo, handling NAME_INPUT and ADDRESS specially
  const buildFieldOptions = (fields: any[]) => {
    const options: {
      id: string;
      name: string;
      title: string;
      index: number;
    }[] = [];
    let logicalIndex = 1;
    fields.forEach((f) => {
      if (f.component === "NAME_INPUT") {
        options.push({
          id: f.id,
          name: "firstName",
          title: f.firstNameTitle || "First Name",
          index: logicalIndex,
        });
        options.push({
          id: f.id,
          name: "lastName",
          title: f.lastNameTitle || "Last Name",
          index: logicalIndex,
        });
        logicalIndex++;
      } else if (f.component === "ADDRESS") {
        options.push({
          id: f.id,
          name: "address",
          title: "Address",
          index: logicalIndex,
        });
        const allowed = f.allowedAddressFields || {
          country: true,
          city: true,
          pincode: true,
          state: true,
        };
        if (allowed.city) {
          options.push({
            id: f.id,
            name: "city",
            title: "City",
            index: logicalIndex,
          });
        }
        if (allowed.state) {
          options.push({
            id: f.id,
            name: "state",
            title: "State",
            index: logicalIndex,
          });
        }
        if (allowed.country) {
          options.push({
            id: f.id,
            name: "country",
            title: "Country",
            index: logicalIndex,
          });
        }
        if (allowed.pincode) {
          options.push({
            id: f.id,
            name: "pincode",
            title: "Pincode",
            index: logicalIndex,
          });
        }
        logicalIndex++;
      } else {
        options.push({
          id: f.id,
          name: f.name,
          title: f.title || f.name,
          index: logicalIndex,
        });
        logicalIndex++;
      }
    });
    return options;
  };

  // Converts backend key to template (for prefill in SelectInputCombo)
  const convertMappedToTemplate = (key: string, options: any[]): string => {
    if (!key) return "";
    // Handles keys like {{fieldId.name.firstName}}, {{fieldId.address.city}}, {{fieldId}}, etc.
    // 1. Handle keys with dot notation
    let result = key.replace(
      /\{\{([^\.\}]+)(?:\.[^\.\}]+)*\.([^\.\}]+)\}\}/g,
      (match, fieldId, fieldName) => {
        const opt = options.find(
          (opt) => opt.id === fieldId && opt.name === fieldName
        );
        if (!opt) return match;
        return `{{${opt.index}.${fieldName}}}`;
      }
    );
    // 2. Handle keys with only fieldId (no dot)
    result = result.replace(/\{\{([a-f0-9-]+)\}\}/gi, (match, fieldId) => {
      const opt = options.find((opt) => opt.id === fieldId);
      if (!opt) return match;
      return `{{${opt.index}.${opt.name}}}`;
    });
    return result;
  };

  // Converts template to backend key (for saving to API)
  const convertTemplateToMapped = (
    template: string,
    options: any[],
    fields: any[]
  ): string => {
    // Replace all {{index.Name}} with correct backend key
    return template.replace(/\{\{(\d+)\.([\w\s]+)\}\}/g, (match, idx, name) => {
      // Find the option with matching index and name
      const opt = options.find(
        (opt) => String(opt.index) === idx && opt.name === name
      );
      if (!opt) return match;
      // For NAME_INPUT, use the new format {{fieldId.name.firstName}} or {{fieldId.name.lastName}}
      const field = fields.find((f) => f.id === opt.id);
      if (field && field.component === "NAME_INPUT") {
        return `{{${opt.id}.name.${opt.name}}}`;
      }
      // For ADDRESS, use {{fieldId.address.address}}, etc.
      if (field && field.component === "ADDRESS") {
        return `{{${opt.id}.address.${opt.name}}}`;
      }
      // For other fields, just use {{fieldId}}
      return `{{${opt.id}}}`;
    });
  };

  // Utility to check if a template references only valid fields
  function isTemplateValid(template: string, fields: any[]): boolean {
    const regex = /\{\{([a-f0-9-]+)(?:\.[^\}]+)?\}\}/gi;
    let match;
    while ((match = regex.exec(template)) !== null) {
      const fieldId = match[1];
      if (!fields.some((f) => f.id === fieldId)) {
        return false;
      }
    }
    return true;
  }

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div className="fixed inset-0 bg-black/50 z-[199] transition-opacity" />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect WhatsApp by Automate Business
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">
                WhatsApp Integration by Automate Business
              </h3>

              {saveSuccess && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center justify-between">
                  <p className="text-sm text-green-800">{successMessage}</p>
                  <button
                    onClick={() => setSaveSuccess(false)}
                    className="text-green-600 hover:text-green-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action: Action) => (
                      <option key={action.id} value={action.id}>
                        {action.name.charAt(0).toUpperCase() +
                          action.name.slice(1)}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a: Action) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>

                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>

                    {connectionType === "new" ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Connection Name
                          </label>
                          <input
                            type="text"
                            value={connectionName}
                            onChange={(e) => setConnectionName(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter connection name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            WA Access Token
                          </label>
                          <input
                            type="text"
                            value={apiKey}
                            onChange={(e) => setApiKey(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your WA Access Token"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter the WA Access Token here. To obtain the WA
                            Access Token, log in to your Automate Business
                            account.
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            WhatsApp Channel ID
                          </label>
                          <input
                            type="text"
                            value={channelId}
                            onChange={(e) => setChannelId(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your WhatsApp Channel ID"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter your WhatsApp Channel ID. You can find this in
                            your WhatsApp Business Account settings.
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            WABA ID
                          </label>
                          <input
                            type="text"
                            value={wabaId}
                            onChange={(e) => setWabaId(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your WABA ID"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter your WhatsApp Business Account ID (WABA ID).
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Phone Number ID
                          </label>
                          <input
                            type="text"
                            value={phoneNumberId}
                            onChange={(e) => setPhoneNumberId(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your Phone Number ID"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter your WhatsApp Phone Number ID.
                          </p>
                        </div>
                        <button
                          onClick={handleAddConnection}
                          disabled={
                            !connectionName ||
                            !apiKey ||
                            !channelId ||
                            !wabaId ||
                            !phoneNumberId ||
                            isAddingConnection
                          }
                          className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                            !connectionName ||
                            !apiKey ||
                            !channelId ||
                            !wabaId ||
                            !phoneNumberId ||
                            isAddingConnection
                              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                              : "bg-app-text-color text-app-background hover:bg-opacity-90"
                          }`}
                        >
                          {isAddingConnection ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <Network className="h-4 w-4" />
                              Add New Connection
                            </>
                          )}
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Select Connection
                          </label>
                          <select
                            value={selectedConnection}
                            onChange={handleConnectionSelect}
                            disabled={!hasValidConnections}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                          >
                            <option value="">Select a connection</option>
                            {connections.map((connection: Connection) => (
                              <option key={connection.id} value={connection.id}>
                                {connection.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {selectedConnection && (
                          <>
                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Select Template
                              </label>
                              <select
                                value={selectedTemplate?.id || ""}
                                onChange={handleTemplateSelect}
                                disabled={isLoadingTemplates}
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select a template</option>
                                {templatesResponse?.data?.data &&
                                templatesResponse.data.data.length > 0 ? (
                                  templatesResponse.data.data.map(
                                    (template: Template) => (
                                      <option
                                        key={template.id}
                                        value={template.id}
                                      >
                                        {template.name}
                                      </option>
                                    )
                                  )
                                ) : (
                                  <option value="" disabled>
                                    {isLoadingTemplates
                                      ? "Loading templates..."
                                      : "No templates available"}
                                  </option>
                                )}
                              </select>
                              {isLoadingTemplates && (
                                <div className="mt-2 flex items-center gap-2">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="text-sm text-gray-500">
                                    Loading templates...
                                  </span>
                                </div>
                              )}
                            </div>

                            {selectedTemplate && (
                              <>
                                <div>
                                  <label className="block text-sm font-medium mb-1">
                                    Recipient's WhatsApp Number
                                  </label>
                                  <SelectInputCombo
                                    options={buildFieldOptions(
                                      formFields?.data?.fields || []
                                    )}
                                    value={convertMappedToTemplate(
                                      formData.recipientTemplate || "",
                                      buildFieldOptions(
                                        formFields?.data?.fields || []
                                      )
                                    )}
                                    onChange={(val) =>
                                      setFormData((prev: any) => ({
                                        ...prev,
                                        recipientTemplate: val,
                                      }))
                                    }
                                    placeholder="Type or insert fields for recipient"
                                  />
                                </div>

                                {getTemplateVariables().map((_, index) => (
                                  <div key={index}>
                                    <label className="block text-sm font-medium mb-1">
                                      Variable {index + 1}
                                    </label>
                                    <SelectInputCombo
                                      options={buildFieldOptions(
                                        formFields?.data?.fields || []
                                      )}
                                      value={convertMappedToTemplate(
                                        formData[`var${index + 1}Template`] ||
                                          "",
                                        buildFieldOptions(
                                          formFields?.data?.fields || []
                                        )
                                      )}
                                      onChange={(val) =>
                                        setFormData((prev: any) => ({
                                          ...prev,
                                          [`var${index + 1}Template`]: val,
                                        }))
                                      }
                                      placeholder={`Type or insert fields for variable ${
                                        index + 1
                                      }`}
                                    />
                                  </div>
                                ))}

                                {renderTemplatePreview()}
                              </>
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {existingConnections.length > 0 ? (
                  <>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        isDisconnecting
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-red-500 text-white hover:bg-red-600"
                      }`}
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        "Disconnect"
                      )}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedConnection ||
                        !selectedTemplate ||
                        !formData.recipientTemplate ||
                        !formData.recipientTemplate ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedConnection ||
                        !selectedTemplate ||
                        !formData.recipientTemplate ||
                        !formData.recipientTemplate ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Update"
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleSave}
                    disabled={
                      connectionType === "existing"
                        ? !selectedConnection ||
                          !selectedTemplate ||
                          !formData.recipientTemplate ||
                          !formData.recipientTemplate ||
                          isSaving
                        : false
                    }
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      connectionType === "existing" &&
                      (!selectedConnection ||
                        !selectedTemplate ||
                        !formData.recipientTemplate ||
                        !formData.recipientTemplate ||
                        isSaving)
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
